package com.edu.maizi_edu_sys.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import com.edu.maizi_edu_sys.config.CorrectionProperties;

import org.springframework.core.task.TaskExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {

    @Autowired
    private CorrectionProperties correctionProperties;

    /**
     * 专用于题目校对任务的线程池
     */
    @Bean("correctionExecutor")
    public org.springframework.core.task.TaskExecutor correctionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(correctionProperties.getExecutor().getCoreSize());
        executor.setMaxPoolSize(correctionProperties.getExecutor().getMaxSize());
        executor.setQueueCapacity(correctionProperties.getExecutor().getQueueCapacity());
        int core = correctionProperties.getExecutor().getCoreSize();
        int max = correctionProperties.getExecutor().getMaxSize();
        executor.setThreadNamePrefix(String.format("correction-exec(core=%d,max=%d)-", core, max));
        executor.initialize();
        return executor;
    }
}
