package com.edu.maizi_edu_sys.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

/**
 * Configuration class that provides a no-op validator to avoid 
 * the ValueExtractorManager initialization issue.
 */
@Configuration
public class NoopValidatorConfig {

    /**
     * Provides a primary validator bean that doesn't rely on Hibernate Validator
     * This will be used by both Spring MVC and Spring WebSocket
     */
    @Bean
    @Primary
    public Validator validator() {
        // Return a simple LocalValidatorFactoryBean without any custom configuration
        // This avoids the problematic ValueExtractorManager initialization
        return new LocalValidatorFactoryBean() {
            @Override
            public void afterPropertiesSet() {
                // Override to prevent initialization that would trigger Hibernate Validator
            }
        };
    }
}
