package com.edu.maizi_edu_sys.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(prefix = "user")
public class UserConfig {
    private String avatarPath = "uploads/avatars/";
    private String defaultAvatar = "default-avatar.png";
    private long maxAvatarSize = 5242880L; // 5MB
    private String[] allowedAvatarTypes = {"image/jpeg", "image/png", "image/gif"};
} 