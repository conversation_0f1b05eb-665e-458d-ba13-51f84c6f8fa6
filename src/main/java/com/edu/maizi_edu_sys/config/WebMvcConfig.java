package com.edu.maizi_edu_sys.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类
 * 配置静态资源映射、视图控制器等
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 配置静态资源处理器
     * 确保所有静态资源能够正确访问
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
                
        // CSS 资源映射
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/");
                
        // JavaScript 资源映射
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/");
                
        // 图片资源映射
        registry.addResourceHandler("/images/**")
                .addResourceLocations("classpath:/static/images/");
                
        // Assets 资源映射 (包含 JS、CSS 等)
        registry.addResourceHandler("/assets/**")
                .addResourceLocations("classpath:/static/assets/");
                
        // 头像资源映射
        registry.addResourceHandler("/avatars/**")
                .addResourceLocations("classpath:/uploads/avatars/", "file:./uploads/avatars/");
                
        // 管理员静态资源映射 - 更新为新的路径
        registry.addResourceHandler("/admin/assets/**")
                .addResourceLocations("classpath:/templates/admin/assets/");
                
        // 字体资源映射
        registry.addResourceHandler("/fonts/**")
                .addResourceLocations("classpath:/static/fonts/");
                
        // 试卷相关资源映射
        registry.addResourceHandler("/paper/**")
                .addResourceLocations("classpath:/static/paper/");
                
        // Favicon 映射
        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/static/favicon.ico");
                
        // 上传文件映射
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:./uploads/");
    }

    /**
     * 配置视图控制器
     * 为简单的页面跳转提供便捷配置
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 默认首页映射
        registry.addViewController("/").setViewName("redirect:/index");
        
        // 管理员页面简单映射
        registry.addViewController("/admin").setViewName("redirect:/admin/dashboard");
        
        // 错误页面映射
        registry.addViewController("/error").setViewName("error/error");
        
        // 维护页面映射
        registry.addViewController("/maintenance").setViewName("maintenance");
        
        // API文档页面映射
        registry.addViewController("/api-docs").setViewName("api-docs");
    }
} 