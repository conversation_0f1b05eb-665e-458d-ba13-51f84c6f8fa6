package com.edu.maizi_edu_sys.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.lang.NonNull;
import org.springframework.web.socket.config.annotation.WebSocketTransportRegistration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.config.ChannelRegistration;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Autowired
    private WebSocketErrorHandler errorHandler;
    
    @Autowired
    private WebSocketChannelInterceptor channelInterceptor;

    @Override
    public void configureMessageBroker(@NonNull MessageBrokerRegistry config) {
        // Enable a simple memory-based message broker for broadcasting to clients
        // Client will subscribe to these destination prefixes to receive messages
        config.enableSimpleBroker("/topic", "/queue");
        
        // Set prefix for messages from clients to server - controller methods will be mapped under this prefix
        config.setApplicationDestinationPrefixes("/app");
        
        // Enable user-specific messages with this prefix
        config.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(@NonNull StompEndpointRegistry registry) {
        // Register STOMP endpoints - clients will connect to this URL
        registry.addEndpoint("/ws")
            .setAllowedOriginPatterns("*") // Use origin patterns instead of origins for credentials support
            .withSockJS(); // Enable SockJS fallback options
            
        // Add the custom error handler
        registry.setErrorHandler(errorHandler);
    }
    
    @Override
    public void configureWebSocketTransport(@NonNull WebSocketTransportRegistration registration) {
        // Configure message size limits and timeouts
        registration.setMessageSizeLimit(128 * 1024); // 128KB
        registration.setSendBufferSizeLimit(512 * 1024); // 512KB
        registration.setSendTimeLimit(20 * 1000); // 20 seconds
    }
    
    @Override
    public void configureClientInboundChannel(@NonNull ChannelRegistration registration) {
        // Configure client inbound channel for better error handling
        registration.interceptors(channelInterceptor);
    }
} 