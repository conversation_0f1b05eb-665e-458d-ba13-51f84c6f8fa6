package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.service.memory.MemoryManager;
import com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 算法监控控制器
 * 提供算法性能监控和诊断的REST API
 * GET /api/algorithm/monitoring/stats      -   获取监控统计信息
 * GET /api/algorithm/monitoring/memory     -   获取内存使用情况
 * GET /api/algorithm/monitoring/health     -   获取系统健康状态
 * GET /api/algorithm/monitoring/metrics    -   获取详细性能指标
 * POST /api/algorithm/monitoring/reset     -   重置监控统计
 * POST /api/algorithm/monitoring/gc        -   触发垃圾回收
 */
@RestController
@RequestMapping("/api/algorithm/monitoring")
@Slf4j
public class AlgorithmMonitoringController {
    
    @Autowired
    private AlgorithmMonitoringService monitoringService;
    
    @Autowired
    private MemoryManager memoryManager;
    
    /**
     * 获取算法监控统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getMonitoringStats() {
        try {
            AlgorithmMonitoringService.MonitoringStats stats = monitoringService.getStats();
            MemoryManager.MemoryInfo memoryInfo = memoryManager.getMemoryInfo();
            
            Map<String, Object> response = new HashMap<>();
            response.put("algorithm_stats", stats);
            response.put("memory_info", memoryInfo);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting monitoring stats: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get monitoring stats: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 重置监控统计信息
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, String>> resetStats() {
        try {
            monitoringService.resetStats();
            Map<String, String> response = new HashMap<>();
            response.put("message", "Monitoring statistics reset successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error resetting monitoring stats: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to reset monitoring stats: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 获取内存使用情况
     */
    @GetMapping("/memory")
    public ResponseEntity<Map<String, Object>> getMemoryInfo() {
        try {
            MemoryManager.MemoryInfo memoryInfo = memoryManager.getMemoryInfo();
            
            Map<String, Object> response = new HashMap<>();
            response.put("total_memory_mb", memoryInfo.getTotalMemory() / 1024 / 1024);
            response.put("used_memory_mb", memoryInfo.getUsedMemory() / 1024 / 1024);
            response.put("free_memory_mb", memoryInfo.getFreeMemory() / 1024 / 1024);
            response.put("max_memory_mb", memoryInfo.getMaxMemory() / 1024 / 1024);
            response.put("usage_ratio", memoryInfo.getUsageRatio());
            response.put("pool_size", memoryInfo.getPoolSize());
            response.put("memory_pressure", memoryManager.isMemoryPressure());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting memory info: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get memory info: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 触发垃圾回收
     */
    @PostMapping("/gc")
    public ResponseEntity<Map<String, Object>> triggerGarbageCollection() {
        try {
            MemoryManager.MemoryInfo beforeGC = memoryManager.getMemoryInfo();
            
            // 触发垃圾回收
            System.gc();
            
            // 等待一小段时间让GC完成
            Thread.sleep(100);
            
            MemoryManager.MemoryInfo afterGC = memoryManager.getMemoryInfo();
            
            Map<String, Object> response = new HashMap<>();

            Map<String, Object> beforeGCMap = new HashMap<>();
            beforeGCMap.put("used_memory_mb", beforeGC.getUsedMemory() / 1024 / 1024);
            beforeGCMap.put("usage_ratio", beforeGC.getUsageRatio());
            response.put("before_gc", beforeGCMap);

            Map<String, Object> afterGCMap = new HashMap<>();
            afterGCMap.put("used_memory_mb", afterGC.getUsedMemory() / 1024 / 1024);
            afterGCMap.put("usage_ratio", afterGC.getUsageRatio());
            response.put("after_gc", afterGCMap);

            response.put("memory_freed_mb", (beforeGC.getUsedMemory() - afterGC.getUsedMemory()) / 1024 / 1024);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error triggering GC: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to trigger GC: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        try {
            AlgorithmMonitoringService.MonitoringStats stats = monitoringService.getStats();
            MemoryManager.MemoryInfo memoryInfo = memoryManager.getMemoryInfo();
            
            Map<String, Object> health = new HashMap<>();
            
            // 算法健康状态
            double successRate = stats.getSuccessRate();
            String algorithmStatus = successRate > 0.9 ? "HEALTHY" : 
                                   successRate > 0.7 ? "WARNING" : "CRITICAL";
            
            // 内存健康状态
            double memoryUsage = memoryInfo.getUsageRatio();
            String memoryStatus = memoryUsage < 0.7 ? "HEALTHY" :
                                memoryUsage < 0.9 ? "WARNING" : "CRITICAL";
            
            // 整体健康状态
            String overallStatus = "HEALTHY";
            if ("CRITICAL".equals(algorithmStatus) || "CRITICAL".equals(memoryStatus)) {
                overallStatus = "CRITICAL";
            } else if ("WARNING".equals(algorithmStatus) || "WARNING".equals(memoryStatus)) {
                overallStatus = "WARNING";
            }
            
            health.put("overall_status", overallStatus);
            health.put("algorithm_status", algorithmStatus);
            health.put("memory_status", memoryStatus);
            health.put("success_rate", successRate);
            health.put("memory_usage", memoryUsage);
            health.put("total_executions", stats.getTotalExecutions());
            health.put("thread_safety_violations", stats.getThreadSafetyViolations());
            health.put("concurrent_requests", stats.getConcurrentRequests());
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("Error getting health status: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get health status: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 获取详细的性能指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getDetailedMetrics() {
        try {
            AlgorithmMonitoringService.MonitoringStats stats = monitoringService.getStats();
            
            Map<String, Object> metrics = new HashMap<>();

            Map<String, Object> executionMetrics = new HashMap<>();
            executionMetrics.put("total_executions", stats.getTotalExecutions());
            executionMetrics.put("successful_executions", stats.getSuccessfulExecutions());
            executionMetrics.put("failed_executions", stats.getFailedExecutions());
            executionMetrics.put("success_rate", stats.getSuccessRate());
            executionMetrics.put("avg_execution_time_ms", stats.getAvgExecutionTime());
            executionMetrics.put("avg_fitness", stats.getAvgFitness());
            executionMetrics.put("avg_generations", stats.getAvgGenerations());
            metrics.put("execution_metrics", executionMetrics);

            Map<String, Object> repairMetrics = new HashMap<>();
            repairMetrics.put("repair_attempts", stats.getRepairAttempts());
            repairMetrics.put("repair_success_rate", stats.getRepairSuccessRate());
            repairMetrics.put("avg_repair_time_ms", stats.getAvgRepairTime());
            metrics.put("repair_metrics", repairMetrics);

            Map<String, Object> safetyMetrics = new HashMap<>();
            safetyMetrics.put("thread_safety_violations", stats.getThreadSafetyViolations());
            safetyMetrics.put("concurrent_requests", stats.getConcurrentRequests());
            safetyMetrics.put("memory_warnings", stats.getMemoryWarnings());
            metrics.put("safety_metrics", safetyMetrics);

            metrics.put("detailed_metrics", stats.getDetailedMetrics());
            metrics.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            log.error("Error getting detailed metrics: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get detailed metrics: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
