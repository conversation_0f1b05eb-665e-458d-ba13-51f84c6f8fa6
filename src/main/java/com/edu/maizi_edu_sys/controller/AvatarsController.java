package com.edu.maizi_edu_sys.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class AvatarsController {

    /**
     * 处理头像请求
     */
    @GetMapping("/avatars/{filename}")
    public ResponseEntity<Resource> getAvatar(@PathVariable String filename) {
        log.debug("请求头像文件: {}", filename);

        try {
            // 尝试从uploads目录获取头像
            Resource resource = new ClassPathResource("uploads/avatars/" + filename);
            if (resource.exists() && resource.isReadable()) {
                log.debug("找到头像文件: {}", filename);
                return ResponseEntity.ok()
                    .contentType(getMediaType(filename))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                    .body(resource);
            }

            log.warn("头像文件不存在: {}，返回默认头像", filename);
            // 如果不存在，返回默认头像
            return getDefaultAvatarResponse();

        } catch (Exception e) {
            log.error("获取头像文件失败: {}", e.getMessage());
            // 出错时返回默认头像
            return getDefaultAvatarResponse();
        }
    }

    /**
     * 处理默认头像请求
     */
    @GetMapping("/images/default-avatar.png")
    public ResponseEntity<Resource> getDefaultAvatar() {
        return getDefaultAvatarResponse();
    }

    /**
     * 获取默认头像响应
     */
    private ResponseEntity<Resource> getDefaultAvatarResponse() {
        try {
            Resource resource = new ClassPathResource("static/images/default-avatar.png");
            if (resource.exists() && resource.isReadable()) {
                return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_PNG)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"default-avatar.png\"")
                    .body(resource);
            } else {
                log.error("默认头像文件不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取默认头像失败: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 根据文件扩展名获取媒体类型
     */
    private MediaType getMediaType(String filename) {
        if (filename == null || !filename.contains(".")) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }

        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG;
            case "png":
                return MediaType.IMAGE_PNG;
            case "gif":
                return MediaType.IMAGE_GIF;
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }
}
