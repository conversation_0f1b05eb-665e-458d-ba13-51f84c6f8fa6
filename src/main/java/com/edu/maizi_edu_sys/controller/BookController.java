package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.Book;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.BookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/v1/books")
@RequiredArgsConstructor
@Slf4j
public class BookController {

    private final BookService bookService;

    /**
     * Search books by keyword
     * 此API不需要认证，允许公开访问
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchBooks(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int max_results) {
        
        List<Book> results = bookService.searchBooks(query, max_results);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "success");
        response.put("data", results);
        return ResponseEntity.ok().body(response);
    }

    /**
     * Get all books
     * 此API不需要认证，允许公开访问
     */
    @GetMapping
    public ApiResponse<?> getAllBooks(HttpServletRequest request) {
        log.info("Get all books request from: {}", request.getRemoteAddr());
        try {
            return bookService.getAllBooks();
        } catch (Exception e) {
            log.error("Error getting all books: {}", e.getMessage(), e);
            return ApiResponse.error("获取书籍列表失败: " + e.getMessage());
        }
    }

    /**
     * Get book by id
     */
    @GetMapping("/{id}")
    public ApiResponse<?> getBookById(@PathVariable(name = "id") Long id) {
        log.info("Getting book by id: {}", id);
        return bookService.getBookById(id);
    }

    /**
     * Create a new book
     */
    @PostMapping
    public ApiResponse<?> createBook(@RequestBody Book book, @RequestHeader("Authorization") String token) {
        log.info("Creating new book: {}", book.getTitle());
        return bookService.createBook(book, token);
    }

    /**
     * Update a book
     */
    @PutMapping("/{id}")
    public ApiResponse<?> updateBook(@PathVariable(name = "id") Long id, @RequestBody Book book, @RequestHeader("Authorization") String token) {
        log.info("Updating book with id: {}", id);
        return bookService.updateBook(id, book, token);
    }

    /**
     * Delete a book
     */
    @DeleteMapping("/{id}")
    public ApiResponse<?> deleteBook(@PathVariable(name = "id") Long id, @RequestHeader("Authorization") String token) {
        log.info("Deleting book with id: {}", id);
        return bookService.deleteBook(id, token);
    }

    /**
     * Get books by type
     */
    @GetMapping("/type/{type}")
    public ApiResponse<?> getBooksByType(@PathVariable(name = "type") String type) {
        log.info("Getting books by type: {}", type);
        return bookService.getBooksByType(type);
    }

    /**
     * Get books by current user
     */
    @GetMapping("/user")
    public ApiResponse<?> getBooksByUser(@RequestHeader("Authorization") String token) {
        log.info("Getting books by current user");
        return bookService.getBooksByUser(token);
    }
}