package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.ChatTitleUpdateDTO;
import com.edu.maizi_edu_sys.entity.ChatSession;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.dto.ChatBookUpdateRequest;
import com.edu.maizi_edu_sys.entity.dto.ChatCreateRequest;
import com.edu.maizi_edu_sys.entity.dto.ChatMessageRequest;
import com.edu.maizi_edu_sys.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
@Slf4j
public class ChatController {
    
    private final ChatService chatService;
    private final RedisTemplate<String, String> redisTemplate;

    @PostMapping("/create")
    public ApiResponse<?> createChat(@Valid @RequestBody ChatCreateRequest request, @RequestHeader("Authorization") String token) {
        log.debug("Creating new chat with title: {}, bookId: {}, knowId: {}, bookUrl: {}", 
                  request.getTitle(), request.getBookId(), request.getKnowId(), request.getBookUrl());
        
        // 如果token包含"Bearer "前缀，移除它
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 如果没有设置标题，使用默认标题
        if (request.getTitle() == null || request.getTitle().trim().isEmpty()) {
            request.setTitle("新对话");
        }
        
        if (request.getKnowId() == null || request.getKnowId().trim().isEmpty()) {
                log.warn("Creating chat with bookId but without knowId");
                // 提供默认值防止数据库约束错误
                request.setKnowId("default");
        }
        if (request.getBookUrl() == null || request.getBookUrl().trim().isEmpty()) {
                log.warn("Creating chat with bookId but without bookUrl");
                // 提供默认值防止数据库约束错误
                request.setBookUrl("https://default");
        }
        
        
        // 调用服务创建对话
        ApiResponse<?> response = chatService.createChat(request, token);
        
        // 记录创建结果
        if (response.getCode() == 200) {
            log.info("Successfully created new chat with id: {}", 
                    response.getData() != null ? ((ChatSession)response.getData()).getId() : "null");
        } else {
            log.warn("Failed to create chat: {}", response.getMessage());
        }
        
        return response;
    }

    @GetMapping("/history")
    public ApiResponse<?> getChatHistory(@RequestHeader("Authorization") String token) {
        log.debug("Getting chat history for token: {}", token.substring(0, Math.min(10, token.length())) + "...");
        ApiResponse<?> response = chatService.getChatHistory(token);
        log.debug("Chat history response status: {}, size: {}", 
            response.getCode(), 
            response.getData() != null ? "not null" : "null");
        return response;
    }

    @GetMapping("/messages/{chatId}")
    public ApiResponse<?> getChatMessages(@PathVariable(name = "chatId") Long chatId, @RequestHeader("Authorization") String token) {
        log.debug("Getting chat messages for chatId={} (using /messages/{chatId} endpoint)", chatId);
        ApiResponse<?> response = chatService.getChatMessages(chatId, token);
        log.debug("Chat messages response status: {}, data null? {}", 
            response.getCode(), 
            response.getData() == null ? "yes" : "no");
        return response;
    }

    @PostMapping("/send")
    public ApiResponse<?> sendMessage(@Valid @RequestBody ChatMessageRequest request, @RequestHeader("Authorization") String token) {
        log.debug("Sending message to chatId={}: {}", request.getChatId(), request.getMessage());
        
        // 如果token包含"Bearer "前缀，移除它
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 确保同时更新对话配置
        return chatService.sendMessage(request, token);
    }
    
    @PutMapping("/title/{chatId}")
    public ApiResponse<?> updateChatTitle(@PathVariable(name = "chatId") Long chatId, @RequestBody String title, @RequestHeader("Authorization") String token) {
        log.debug("Updating chat title: chatId={}, title={}", chatId, title);
        ApiResponse<?> response = chatService.updateChatTitle(chatId, title, token);
        log.debug("Title update response: {}", response);
        return response;
    }
    
    /**
     * 更新聊天关联的教材配置
     * @param chatId 聊天ID
     * @param bookData 教材数据：包含bookId, bookUrl, knowId
     * @param token 用户令牌
     * @return 操作结果
     */
    @PutMapping("/{chatId}/book")
    public ApiResponse<?> updateChatBook(
            @PathVariable(name = "chatId") Long chatId, 
            @RequestBody ChatBookUpdateRequest bookData,
            @RequestHeader("Authorization") String token) {
        log.debug("Updating chat book: chatId={}, bookData={}", chatId, bookData);
        ApiResponse<?> response = chatService.updateChatBook(chatId, bookData, token);
        log.debug("Book update response: {}", response);
        return response;
    }
    
    /**
     * 获取聊天详情，包括关联的教材信息
     * @param chatId 聊天ID
     * @param token 用户令牌
     * @return 聊天详情
     */
    @GetMapping("/{chatId}")
    public ApiResponse<?> getChatDetail(
            @PathVariable(name = "chatId") Long chatId,
            @RequestHeader("Authorization") String token) {
        log.debug("Getting chat detail: chatId={}", chatId);
        ApiResponse<?> response = chatService.getChatDetail(chatId, token);
        log.debug("Chat detail response: {}", response);
        return response;
    }

    @GetMapping("/{chatId}/messages")
    public ApiResponse<?> getChatMessagesListForChat(
            @PathVariable("chatId") Long chatId,
            @RequestHeader("Authorization") String token) {
        log.debug("Getting messages list for chatId={} (using /{chatId}/messages endpoint)", chatId);
        return chatService.getChatMessages(chatId, token);
    }

    /**
     * 更新聊天对话标题
     */
    @PostMapping("/update-title")
    public ApiResponse<?> updateChatTitle(@RequestBody ChatTitleUpdateDTO request, @RequestHeader("Authorization") String token) {
        // 验证权限
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 验证参数
        if (request.getChatId() == null || request.getNewTitle() == null || request.getNewTitle().trim().isEmpty()) {
            return ApiResponse.error("聊天ID和新标题不能为空");
        }
        
        // 调用服务层更新标题
        return chatService.updateChatTitle(request.getChatId(), request.getNewTitle().trim(), token);
    }

    /**
     * 删除聊天对话
     */
    @DeleteMapping("/{chatId}")
    public ApiResponse<?> deleteChat(@PathVariable Long chatId, @RequestHeader("Authorization") String token) {
        // 验证权限
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 使用缓存或其他机制检查短时间内的重复请求
        String cacheKey = "delete_chat_" + chatId + "_" + token;
        if (redisTemplate.hasKey(cacheKey)) {
            return ApiResponse.error("请勿重复提交删除请求");
        }
        
        // 设置短时间缓存，防止重复请求
        redisTemplate.opsForValue().set(cacheKey, "1", 5, TimeUnit.SECONDS);
        
        // 执行删除
        return chatService.deleteChat(chatId, token);
    }
} 