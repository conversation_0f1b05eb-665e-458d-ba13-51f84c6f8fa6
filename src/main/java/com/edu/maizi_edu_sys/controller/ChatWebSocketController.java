package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.dto.ChatMessageRequest;
import com.edu.maizi_edu_sys.entity.dto.WebSocketMessage;
import com.edu.maizi_edu_sys.service.ChatService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
@RequiredArgsConstructor
@Slf4j
public class ChatWebSocketController {
    private static final Logger logger = LoggerFactory.getLogger(ChatWebSocketController.class);
    private final SimpMessagingTemplate messagingTemplate;
    private final ChatService chatService;
    private final JwtUtil jwtUtil;
    private final UserService userService;

    /**
     * Handles WebSocket messages sent to /app/chat
     */
    @MessageMapping("/chat")
    public void handleChatMessage(@Payload WebSocketMessage message, SimpMessageHeaderAccessor headerAccessor) {
        try {
            logger.debug("Received message: {}", message);
            
            // Send acknowledgment back to sender
            WebSocketMessage ack = new WebSocketMessage();
            ack.setType("CHAT_RESPONSE_ACK");
            messagingTemplate.convertAndSendToUser(
                message.getSenderId(),
                "/queue/messages",
                ack
            );

            // Validate token and get user
            String token = message.getToken();
            if (token == null || token.isEmpty()) {
                sendErrorMessage(message.getSenderId(), "Authentication token is required");
                return;
            }
            
            try {
                String username = jwtUtil.getUsernameFromToken(token);
                User user = userService.getByUsername(username);
                
                if (user == null) {
                    sendErrorMessage(message.getSenderId(), "User not found");
                    return;
                }
                
                // Process the chat message
                processChatRequest(message, token, user);
                
            } catch (Exception e) {
                logger.error("Authentication error: ", e);
                sendErrorMessage(message.getSenderId(), "Authentication failed: " + e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("Error processing message: ", e);
            WebSocketMessage errorMessage = new WebSocketMessage();
            errorMessage.setType("ERROR");
            errorMessage.setContent("Error processing message: " + e.getMessage());
            messagingTemplate.convertAndSendToUser(
                message.getSenderId(),
                "/queue/messages",
                errorMessage
            );
        }
    }
    
    private void processChatRequest(WebSocketMessage message, String token, User user) {
        // Only log minimal information about the chat request
        log.warn("Processing chat request. ChatId: {}, User: {}", message.getChatId(), user.getUsername());
        
        try {
            // Create chat message request
            ChatMessageRequest request = new ChatMessageRequest();
            request.setChatId(message.getChatIdAsLong());
            request.setMessage(message.getContent());
            
            // Acknowledge receipt
            sendMessageToUser(message.getSenderId(), new WebSocketMessage(
                "CHAT_RESPONSE_ACK",
                message.getChatId(),
                message.getSenderId(),
                "Message received, processing...",
                null
            ));
            
            // Process streaming response asynchronously
            chatService.streamMessage(request, token, 
                (chunk) -> {
                    // Don't log chunks from AI, just send them
                    sendMessageToUser(message.getSenderId(), new WebSocketMessage(
                        "CHAT_RESPONSE_CHUNK",
                        message.getChatId(),
                        "bot",
                        chunk,
                        null
                    ));
                },
                (error) -> {
                    log.error("Error in streaming response: {}", error);
                    sendErrorMessage(message.getSenderId(), "AI model error: " + error);
                },
                () -> {
                    // Completion notification
                    sendMessageToUser(message.getSenderId(), new WebSocketMessage(
                        "CHAT_RESPONSE_COMPLETE",
                        message.getChatId(),
                        "bot",
                        null,
                        null
                    ));
                }
            );
        } catch (Exception e) {
            log.error("Error in processChatRequest: {}", e.getMessage(), e);
            sendErrorMessage(message.getSenderId(), "Error processing chat request: " + e.getMessage());
            throw e; // Rethrow to be caught by the caller
        }
    }
    
    private void sendMessageToUser(String userId, WebSocketMessage message) {
        try {
            // No need to log every message sent
            messagingTemplate.convertAndSendToUser(
                userId,
                "/queue/messages",
                message
            );
        } catch (Exception e) {
            log.error("Failed to send message to user {}: {}", userId, e.getMessage(), e);
        }
    }
    
    private void sendErrorMessage(String userId, String errorMessage) {
        WebSocketMessage message = new WebSocketMessage();
        message.setType("ERROR");
        message.setContent(errorMessage);
        sendMessageToUser(userId, message);
    }
} 