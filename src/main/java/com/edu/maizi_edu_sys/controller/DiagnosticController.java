package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Enumeration;

@Controller
@RequestMapping("/diagnostic")
public class DiagnosticController {

    @GetMapping
    public String diagnostic(HttpServletRequest request, Model model) {
        // Add system properties
        Properties properties = System.getProperties();
        model.addAttribute("systemProperties", properties);
        
        // Add Spring profiles
        String[] profiles = {"spring.profiles.active", "spring.thymeleaf.prefix", "spring.thymeleaf.suffix"};
        Map<String, String> springProperties = new HashMap<>();
        for (String profile : profiles) {
            springProperties.put(profile, System.getProperty(profile, "Not set"));
        }
        model.addAttribute("springProperties", springProperties);
        
        // Add request information
        Map<String, String> requestInfo = new HashMap<>();
        requestInfo.put("Request URI", request.getRequestURI());
        requestInfo.put("Request URL", request.getRequestURL().toString());
        requestInfo.put("Context Path", request.getContextPath());
        requestInfo.put("Servlet Path", request.getServletPath());
        requestInfo.put("Path Info", request.getPathInfo() != null ? request.getPathInfo() : "null");
        requestInfo.put("Query String", request.getQueryString() != null ? request.getQueryString() : "null");
        requestInfo.put("Remote Address", request.getRemoteAddr());
        
        // Add headers
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        
        model.addAttribute("requestInfo", requestInfo);
        model.addAttribute("headers", headers);
        
        // Add template diagnostic information
        model.addAttribute("templateName", "diagnostic");
        model.addAttribute("timestamp", System.currentTimeMillis());
        
        return "diagnostic";
    }
} 