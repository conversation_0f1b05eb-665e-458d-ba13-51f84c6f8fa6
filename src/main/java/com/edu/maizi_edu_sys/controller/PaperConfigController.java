package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.ApiResponse;
import com.edu.maizi_edu_sys.dto.PaperConfigDTO;
import com.edu.maizi_edu_sys.dto.PaginationResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.PaperConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 试卷配置管理控制器
 */
@RestController
@RequestMapping("/api/paper-configs")
@RequiredArgsConstructor
@Slf4j
public class PaperConfigController {

    private final PaperConfigService paperConfigService;
    private final AuthService authService;

    /**
     *  保存配置
     */
    @PostMapping
    public ResponseEntity<ApiResponse<PaperConfigDTO>> saveConfig(@Valid @RequestBody PaperConfigDTO configDTO) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            configDTO.setUserId(currentUserId);
            PaperConfigDTO savedConfig = paperConfigService.saveConfig(configDTO);

            log.info("用户 {} 保存配置成功: {}", currentUserId, savedConfig.getConfigName());
            return ResponseEntity.ok(new ApiResponse<>(true, "配置保存成功", savedConfig));
        } catch (Exception e) {
            log.error("保存配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  更新配置
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PaperConfigDTO>> updateConfig(
            @PathVariable Long id, 
            @Valid @RequestBody PaperConfigDTO configDTO) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            configDTO.setUserId(currentUserId);
            PaperConfigDTO updatedConfig = paperConfigService.updateConfig(id, configDTO);

            log.info("用户 {} 更新配置成功: {}", currentUserId, updatedConfig.getConfigName());
            return ResponseEntity.ok(new ApiResponse<>(true, "配置更新成功", updatedConfig));
        } catch (Exception e) {
            log.error("更新配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  删除配置
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteConfig(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            paperConfigService.deleteConfig(id, currentUserId);

            log.info("用户 {} 删除配置成功: {}", currentUserId, id);
            return ResponseEntity.ok(new ApiResponse<>(true, "配置删除成功", null));
        } catch (Exception e) {
            log.error("删除配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  获取配置详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PaperConfigDTO>> getConfig(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            PaperConfigDTO config = paperConfigService.getConfigById(id, currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取配置成功", config));
        } catch (Exception e) {
            log.error("获取配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  获取用户配置列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<PaperConfigDTO>>> getUserConfigs(
            @RequestParam(defaultValue = "false") boolean includePublic) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            List<PaperConfigDTO> configs;
            if (includePublic) {
                configs = paperConfigService.getUserAndPublicConfigs(currentUserId);
            } else {
                configs = paperConfigService.getUserConfigs(currentUserId);
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "获取配置列表成功", configs));
        } catch (Exception e) {
            log.error("获取配置列表失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  分页获取配置列表
     */
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<PaginationResponse<PaperConfigDTO>>> getConfigsPaginated(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "false") boolean includePublic) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            PaginationResponse<PaperConfigDTO> configs;
            if (includePublic) {
                configs = paperConfigService.getUserAndPublicConfigsPaginated(currentUserId, page, size);
            } else {
                configs = paperConfigService.getUserConfigsPaginated(currentUserId, page, size);
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "获取配置列表成功", configs));
        } catch (Exception e) {
            log.error("获取配置列表失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  搜索配置
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<PaperConfigDTO>>> searchConfigs(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "false") boolean includePublic) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            List<PaperConfigDTO> configs = paperConfigService.searchConfigs(currentUserId, keyword, includePublic);
            return ResponseEntity.ok(new ApiResponse<>(true, "搜索配置成功", configs));
        } catch (Exception e) {
            log.error("搜索配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  获取默认配置
     */
    @GetMapping("/default")
    public ResponseEntity<ApiResponse<PaperConfigDTO>> getDefaultConfig() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            PaperConfigDTO defaultConfig = paperConfigService.getDefaultConfig(currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取默认配置成功", defaultConfig));
        } catch (Exception e) {
            log.error("获取默认配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  设置默认配置
     */
    @PostMapping("/{id}/set-default")
    public ResponseEntity<ApiResponse<Void>> setDefaultConfig(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            paperConfigService.setDefaultConfig(id, currentUserId);
            log.info("用户 {} 设置默认配置成功: {}", currentUserId, id);
            return ResponseEntity.ok(new ApiResponse<>(true, "设置默认配置成功", null));
        } catch (Exception e) {
            log.error("设置默认配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  使用配置（更新使用记录）
     */
    @PostMapping("/{id}/use")
    public ResponseEntity<ApiResponse<Void>> useConfig(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            paperConfigService.useConfig(id, currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "配置使用记录更新成功", null));
        } catch (Exception e) {
            log.error("更新配置使用记录失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  复制配置
     */
    @PostMapping("/{id}/copy")
    public ResponseEntity<ApiResponse<PaperConfigDTO>> copyConfig(
            @PathVariable Long id, 
            @RequestParam String newConfigName) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            PaperConfigDTO copiedConfig = paperConfigService.copyConfig(id, currentUserId, newConfigName);
            log.info("用户 {} 复制配置成功: {} -> {}", currentUserId, id, copiedConfig.getConfigName());
            return ResponseEntity.ok(new ApiResponse<>(true, "配置复制成功", copiedConfig));
        } catch (Exception e) {
            log.error("复制配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  导出配置
     */
    @GetMapping("/{id}/export")
    public ResponseEntity<Resource> exportConfig(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest().build();
            }

            Resource resource = paperConfigService.exportConfig(id, currentUserId);
            
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
        } catch (Exception e) {
            log.error("导出配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     *  批量导出配置
     */
    @PostMapping("/export")
    public ResponseEntity<Resource> exportConfigs(@RequestBody List<Long> ids) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest().build();
            }

            Resource resource = paperConfigService.exportConfigs(ids, currentUserId);
            
            return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
        } catch (Exception e) {
            log.error("批量导出配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     *  导入配置
     */
    @PostMapping("/import")
    public ResponseEntity<ApiResponse<List<PaperConfigDTO>>> importConfigs(
            @RequestParam("file") MultipartFile file) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            List<PaperConfigDTO> importedConfigs = paperConfigService.importConfigs(file, currentUserId);
            log.info("用户 {} 导入配置成功，数量: {}", currentUserId, importedConfigs.size());
            return ResponseEntity.ok(new ApiResponse<>(true, 
                String.format("成功导入 %d 个配置", importedConfigs.size()), importedConfigs));
        } catch (Exception e) {
            log.error("导入配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  获取配置统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<PaperConfigService.ConfigStatistics>> getConfigStatistics() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            PaperConfigService.ConfigStatistics statistics = paperConfigService.getConfigStatistics(currentUserId);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取统计信息成功", statistics));
        } catch (Exception e) {
            log.error("获取配置统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  获取最近使用的配置
     */
    @GetMapping("/recent")
    public ResponseEntity<ApiResponse<List<PaperConfigDTO>>> getRecentConfigs(
            @RequestParam(defaultValue = "5") int limit) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            List<PaperConfigDTO> recentConfigs = paperConfigService.getRecentlyUsedConfigs(currentUserId, limit);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取最近使用配置成功", recentConfigs));
        } catch (Exception e) {
            log.error("获取最近使用配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     *  获取最常用的配置
     */
    @GetMapping("/most-used")
    public ResponseEntity<ApiResponse<List<PaperConfigDTO>>> getMostUsedConfigs(
            @RequestParam(defaultValue = "5") int limit) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "用户未登录", null));
            }

            List<PaperConfigDTO> mostUsedConfigs = paperConfigService.getMostUsedConfigs(currentUserId, limit);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取最常用配置成功", mostUsedConfigs));
        } catch (Exception e) {
            log.error("获取最常用配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ApiResponse<>(false, e.getMessage(), null));
        }
    }

    /**
     * 测试页面 - 用于测试API响应
     */
    @GetMapping("/test")
    public String testPage() {
        return "test/paper-config-api-test";
    }
}
