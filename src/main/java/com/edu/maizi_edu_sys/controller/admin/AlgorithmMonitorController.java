package com.edu.maizi_edu_sys.controller.admin;

import com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService;
import com.edu.maizi_edu_sys.service.AlgorithmMonitorDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 算法监控控制器
 */
@Controller
@RequestMapping("/admin/algorithm-monitor")
public class AlgorithmMonitorController {

    @Autowired
    private AlgorithmMonitoringService algorithmMonitoringService;
    
    @Autowired
    private AlgorithmMonitorDataService algorithmMonitorDataService;

    // 模拟存储运行中的算法实例
    private static final Map<String, AlgorithmExecutionInfo> runningAlgorithms = new ConcurrentHashMap<>();

    /**
     * 显示算法监控页面
     */
    @GetMapping("")
    public String showAlgorithmMonitor(Model model) {
        // 添加页面所需的基础数据
        model.addAttribute("pageTitle", "遗传算法监控");
        model.addAttribute("currentPage", "algorithm-monitor");
        
        // 获取概览统计数据
        Map<String, Object> overviewData = getOverviewData().getBody();
        model.addAttribute("overviewData", overviewData);
        
        return "admin/templates/algorithm-monitor";
    }

    /**
     * 获取概览数据API
     */
    @GetMapping("/api/overview")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getOverviewData() {
        try {
            Map<String, Object> data = algorithmMonitorDataService.getOverviewData();
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            // 如果数据库出错，返回默认数据
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("runningAlgorithms", 0);
            defaultData.put("todayPapers", 0);
            defaultData.put("avgGenerations", 0);
            defaultData.put("anomalyCount", 0);
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取试卷列表API
     */
    @GetMapping("/api/papers")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> getPapersList(
            @RequestParam(defaultValue = "today") String dateRange,
            @RequestParam(defaultValue = "all") String status,
            @RequestParam(defaultValue = "all") String performance,
            @RequestParam(required = false) String paperId) {

        try {
            List<Map<String, Object>> papers = algorithmMonitorDataService.getPapersList(dateRange, status, performance, paperId);
            return ResponseEntity.ok(papers);
        } catch (Exception e) {
            // 如果数据库出错，返回空列表
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * 获取特定试卷的详细监控数据
     */
    @GetMapping("/api/details/{paperId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getPaperDetails(@PathVariable String paperId) {
        try {
            Map<String, Object> details = algorithmMonitorDataService.getPaperDetails(paperId);
            return ResponseEntity.ok(details);
        } catch (Exception e) {
            // 如果数据库出错，返回默认数据
            Map<String, Object> defaultDetails = new HashMap<>();
            defaultDetails.put("paperId", paperId);
            defaultDetails.put("paperName", "未知试卷");
            defaultDetails.put("status", "unknown");
            defaultDetails.put("startTime", new Date());
            defaultDetails.put("progress", 0);
            return ResponseEntity.ok(defaultDetails);
        }
    }

    /**
     * 获取适应度雷达图数据
     */
    @GetMapping("/api/fitness-radar/{paperId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getFitnessRadarData(@PathVariable String paperId) {
        try {
            Map<String, Object> radarData = algorithmMonitorDataService.getFitnessRadarData(paperId);
            return ResponseEntity.ok(radarData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认雷达图数据
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("dimensions", Arrays.asList("难度分布", "知识点覆盖", "题型平衡", "时间分配", "分值分布", "认知层次", "重复度控制", "综合质量"));
            defaultData.put("values", Arrays.asList(0.8, 0.85, 0.75, 0.9, 0.7, 0.88, 0.82, 0.86));
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取种群进化热力图数据
     */
    @GetMapping("/api/population-heatmap/{paperId}")
    @ResponseBody
    public ResponseEntity<List<List<Object>>> getPopulationHeatmapData(@PathVariable String paperId) {
        try {
            List<List<Object>> heatmapData = algorithmMonitorDataService.getPopulationHeatmapData(paperId);
            return ResponseEntity.ok(heatmapData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认热力图数据
            List<List<Object>> defaultData = new ArrayList<>();
            for (int gen = 1; gen <= 20; gen++) {
                for (int ind = 1; ind <= 10; ind++) {
                    defaultData.add(Arrays.asList(gen, ind, Math.random() * 0.3 + 0.7));
                }
            }
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取约束处理分析数据
     */
    @GetMapping("/api/constraints/{paperId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getConstraintData(@PathVariable String paperId) {
        try {
            Map<String, Object> constraintData = algorithmMonitorDataService.getConstraintData(paperId);
            return ResponseEntity.ok(constraintData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认约束数据
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("constraints", Arrays.asList("难度约束", "知识点约束", "题型约束", "时间约束", "分值约束", "重复度约束"));
            defaultData.put("violations", Arrays.asList(0, 0, 0, 0, 0, 0));
            defaultData.put("repairs", Arrays.asList(0, 0, 0, 0, 0, 0));
            defaultData.put("failures", Arrays.asList(0, 0, 0, 0, 0, 0));
            defaultData.put("satisfactionRate", 1.0);
            defaultData.put("repairSuccessRate", 1.0);
            defaultData.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取收敛性分析数据
     */
    @GetMapping("/api/convergence/{paperId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getConvergenceData(@PathVariable String paperId) {
        try {
            Map<String, Object> convergenceData = algorithmMonitorDataService.getConvergenceData(paperId);
            return ResponseEntity.ok(convergenceData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认收敛数据
            Map<String, Object> defaultData = new HashMap<>();
            List<Integer> generations = new ArrayList<>();
            List<Double> bestFitness = new ArrayList<>();
            List<Double> avgFitness = new ArrayList<>();
            List<Double> diversity = new ArrayList<>();
            
            for (int i = 1; i <= 50; i++) {
                generations.add(i);
                bestFitness.add(0.5 + (i * 0.01));
                avgFitness.add(0.4 + (i * 0.008));
                diversity.add(1.0 - (i * 0.015));
            }
            
            defaultData.put("generations", generations);
            defaultData.put("bestFitness", bestFitness);
            defaultData.put("avgFitness", avgFitness);
            defaultData.put("diversity", diversity);
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 导出监控报告
     */
    @PostMapping("/api/export")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> exportReport(
            @RequestParam String paperId,
            @RequestParam String format) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 生成报告
            String reportUrl = generateReport(paperId, format);
            result.put("success", true);
            result.put("downloadUrl", reportUrl);
            result.put("message", "报告生成成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "报告生成失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有试卷的聚合适应度雷达图数据
     */
    @GetMapping("/api/fitness-radar/all")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAggregatedFitnessRadarData() {
        try {
            Map<String, Object> radarData = algorithmMonitorDataService.getAggregatedFitnessRadarData();
            return ResponseEntity.ok(radarData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认雷达图数据
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("dimensions", Arrays.asList("难度平衡", "知识点覆盖", "题型分布", "区分度", "时间控制", "出题效率", "约束满足", "整体质量"));
            defaultData.put("values", Arrays.asList(0.75, 0.80, 0.70, 0.85, 0.90, 0.65, 0.78, 0.82));
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取所有试卷的聚合收敛数据
     */
    @GetMapping("/api/convergence/all")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAggregatedConvergenceData() {
        try {
            Map<String, Object> convergenceData = algorithmMonitorDataService.getAggregatedConvergenceData();
            return ResponseEntity.ok(convergenceData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认收敛数据
            Map<String, Object> defaultData = new HashMap<>();
            List<Integer> generations = new ArrayList<>();
            List<Double> bestFitness = new ArrayList<>();
            List<Double> avgFitness = new ArrayList<>();
            
            for (int i = 1; i <= 30; i++) {
                generations.add(i);
                bestFitness.add(0.4 + (i * 0.02));
                avgFitness.add(0.3 + (i * 0.015));
            }
            
            defaultData.put("generations", generations);
            defaultData.put("bestFitness", bestFitness);
            defaultData.put("avgFitness", avgFitness);
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取所有试卷的聚合种群热力图数据
     */
    @GetMapping("/api/population/all")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAggregatedPopulationData() {
        try {
            Map<String, Object> populationData = algorithmMonitorDataService.getAggregatedPopulationData();
            return ResponseEntity.ok(populationData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认热力图数据
            Map<String, Object> defaultData = new HashMap<>();
            List<List<Double>> heatmapData = new ArrayList<>();
            
            for (int gen = 0; gen < 15; gen++) {
                List<Double> row = new ArrayList<>();
                for (int ind = 0; ind < 8; ind++) {
                    row.add(0.5 + Math.random() * 0.4);
                }
                heatmapData.add(row);
            }
            
            defaultData.put("heatmapData", heatmapData);
            return ResponseEntity.ok(defaultData);
        }
    }

    /**
     * 获取所有试卷的聚合约束数据
     */
    @GetMapping("/api/constraints/all")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAggregatedConstraintData() {
        try {
            Map<String, Object> constraintData = algorithmMonitorDataService.getAggregatedConstraintData();
            return ResponseEntity.ok(constraintData);
        } catch (Exception e) {
            // 如果数据库出错，返回默认数据
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("satisfied", 80);
            defaultData.put("violated", 15);
            defaultData.put("repaired", 5);
            return ResponseEntity.ok(defaultData);
        }
    }
    
    /**
     * 获取最近算法执行的代数信息
     */
    @GetMapping("/api/recent-generations")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> getRecentGenerationsInfo() {
        try {
            List<Map<String, Object>> generationsInfo = algorithmMonitorDataService.getRecentGenerationsInfo();
            return ResponseEntity.ok(generationsInfo);
        } catch (Exception e) {
            // 如果数据库出错，返回空列表
            return ResponseEntity.ok(new ArrayList<>());
        }
    }

    /**
     * 算法控制API - 暂停/恢复/停止
     */
    @PostMapping("/api/control/{paperId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> controlAlgorithm(
            @PathVariable String paperId,
            @RequestParam String action) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            switch (action.toLowerCase()) {
                case "pause":
                    pauseAlgorithm(paperId);
                    result.put("message", "算法已暂停");
                    break;
                case "resume":
                    resumeAlgorithm(paperId);
                    result.put("message", "算法已恢复");
                    break;
                case "stop":
                    stopAlgorithm(paperId);
                    result.put("message", "算法已停止");
                    break;
                default:
                    result.put("success", false);
                    result.put("message", "未知操作: " + action);
                    return ResponseEntity.badRequest().body(result);
            }
            
            result.put("success", true);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成报告
     */
    private String generateReport(String paperId, String format) {
        // 模拟报告生成
        String filename = String.format("algorithm_report_%s_%d.%s", 
                                       paperId, System.currentTimeMillis(), format);
        return "/downloads/" + filename;
    }

    /**
     * 暂停算法
     */
    private void pauseAlgorithm(String paperId) {
        AlgorithmExecutionInfo info = runningAlgorithms.get(paperId);
        if (info != null && "running".equals(info.getStatus())) {
            info.setPaused(true);
            System.out.println("算法已暂停: " + paperId);
            

        } else {
            throw new IllegalStateException("无法暂停算法: " + paperId + ", 当前状态: " + 
                                          (info != null ? info.getStatus() : "不存在"));
        }
    }

    /**
     * 恢复算法
     */
    private void resumeAlgorithm(String paperId) {
        AlgorithmExecutionInfo info = runningAlgorithms.get(paperId);
        if (info != null && info.isPaused()) {
            info.setPaused(false);
            System.out.println("算法已恢复: " + paperId);
            

        } else {
            throw new IllegalStateException("无法恢复算法: " + paperId + ", 当前状态: " + 
                                          (info != null ? info.getStatus() : "不存在"));
        }
    }

    /**
     * 停止算法
     */
    private void stopAlgorithm(String paperId) {
        AlgorithmExecutionInfo info = runningAlgorithms.get(paperId);
        if (info != null) {
            info.setStatus("stopped");
            info.setEndTime(new Date());
            System.out.println("算法已停止: " + paperId);
            

            
            // 从运行列表中移除
            runningAlgorithms.remove(paperId);
        } else {
            throw new IllegalStateException("无法停止算法: " + paperId + ", 算法不存在");
        }
    }
    
    /**
     * 重启算法
     */
    private void restartAlgorithm(String paperId) {
        AlgorithmExecutionInfo info = runningAlgorithms.get(paperId);
        if (info != null) {
            // 重置算法状态
            info.setStatus("running");
            info.setStartTime(new Date());
            info.setEndTime(null);
            info.setCurrentGeneration(0);
            info.setBestFitness(0.0);
            info.setAvgFitness(0.0);
            info.setConvergenceSpeed(0.0);
            info.setDiversityIndex(1.0);
            info.setConstraintViolations(0);
            info.setPaused(false);
            info.setErrorMessage(null);
            
            System.out.println("算法已重启: " + paperId);
            

        } else {
            throw new IllegalStateException("无法重启算法: " + paperId + ", 算法不存在");
        }
    }
    
    /**
     * 创建新的算法执行实例
     */
    private AlgorithmExecutionInfo createAlgorithmExecution(String paperId, String paperName) {
        AlgorithmExecutionInfo info = new AlgorithmExecutionInfo(paperId, paperName);
        runningAlgorithms.put(paperId, info);
        
        System.out.println("创建新的算法执行实例: " + paperId);
        
        // 记录创建事件到监控服务
        if (algorithmMonitoringService != null) {
            AlgorithmMonitoringService.ExecutionContext ctx = algorithmMonitoringService.startExecution(paperId);
            info.addMetric("execContext", ctx);
        }
        
        return info;
    }
    
    /**
     * 更新算法执行状态
     */
    private void updateAlgorithmStatus(String paperId, int generation, double bestFitness, 
                                     double avgFitness, double diversity) {
        AlgorithmExecutionInfo info = runningAlgorithms.get(paperId);
        if (info != null) {
            info.setCurrentGeneration(generation);
            info.setBestFitness(bestFitness);
            info.setAvgFitness(avgFitness);
            info.setDiversityIndex(diversity);
            
            // 计算收敛速度
            if (generation > 1) {
                Double previousBest = (Double) info.getMetric("previousBestFitness");
                if (previousBest != null) {
                    info.setConvergenceSpeed(bestFitness - previousBest);
                }
            }
            info.addMetric("previousBestFitness", bestFitness);
            
            // 检查是否收敛
            if (info.isConverged()) {
                info.setStatus("completed");
                System.out.println("算法收敛完成: " + paperId + ", 最终适应度: " + bestFitness);
                
                if (algorithmMonitoringService != null) {
                    AlgorithmMonitoringService.ExecutionContext ctx = (AlgorithmMonitoringService.ExecutionContext) info.getMetric("execContext");
                    if (ctx != null) {
                        algorithmMonitoringService.recordSuccess(ctx, bestFitness, generation);
                    }
                }
            }
        }
    }
    
    /**
     * 记录算法错误
     */
    private void recordAlgorithmError(String paperId, String errorMessage, Exception exception) {
        AlgorithmExecutionInfo info = runningAlgorithms.get(paperId);
        if (info != null) {
            info.setStatus("failed");
            info.setErrorMessage(errorMessage);
            info.setEndTime(new Date());
            
            System.err.println("算法执行失败: " + paperId + ", 错误: " + errorMessage);
            if (exception != null) {
                exception.printStackTrace();
            }
            
            if (algorithmMonitoringService != null) {
                AlgorithmMonitoringService.ExecutionContext ctx = (AlgorithmMonitoringService.ExecutionContext) info.getMetric("execContext");
                if (ctx != null) {
                    algorithmMonitoringService.recordFailure(ctx, "RuntimeError", errorMessage);
                }
            }
        }
    }
    
    /**
     * 获取算法执行统计信息
     */
    private Map<String, Object> getAlgorithmStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        long runningCount = runningAlgorithms.values().stream()
                .filter(info -> "running".equals(info.getStatus()))
                .count();
        
        long pausedCount = runningAlgorithms.values().stream()
                .filter(info -> info.isPaused())
                .count();
        
        long completedCount = runningAlgorithms.values().stream()
                .filter(info -> "completed".equals(info.getStatus()))
                .count();
        
        long failedCount = runningAlgorithms.values().stream()
                .filter(info -> "failed".equals(info.getStatus()))
                .count();
        
        double avgBestFitness = runningAlgorithms.values().stream()
                .filter(info -> "running".equals(info.getStatus()) || "completed".equals(info.getStatus()))
                .mapToDouble(AlgorithmExecutionInfo::getBestFitness)
                .average()
                .orElse(0.0);
        
        double avgGeneration = runningAlgorithms.values().stream()
                .filter(info -> "running".equals(info.getStatus()) || "completed".equals(info.getStatus()))
                .mapToDouble(AlgorithmExecutionInfo::getCurrentGeneration)
                .average()
                .orElse(0.0);
        
        stats.put("runningCount", runningCount);
        stats.put("pausedCount", pausedCount);
        stats.put("completedCount", completedCount);
        stats.put("failedCount", failedCount);
        stats.put("totalCount", runningAlgorithms.size());
        stats.put("avgBestFitness", avgBestFitness);
        stats.put("avgGeneration", avgGeneration);
        stats.put("timestamp", System.currentTimeMillis());
        
        return stats;
    }

    /**
     * 算法执行信息内部类
     */
    private static class AlgorithmExecutionInfo {
        private String paperId;
        private String paperName;
        private String status; // running, paused, completed, failed
        private Date startTime;
        private Date endTime;
        private int currentGeneration;
        private int maxGenerations;
        private double bestFitness;
        private double avgFitness;
        private double convergenceSpeed;
        private double diversityIndex;
        private int constraintViolations;
        private boolean isPaused;
        private String errorMessage;
        private Map<String, Object> additionalMetrics;
        
        // 默认构造函数
        public AlgorithmExecutionInfo() {
            this.additionalMetrics = new HashMap<>();
        }
        
        // 完整构造函数
        public AlgorithmExecutionInfo(String paperId, String paperName) {
            this();
            this.paperId = paperId;
            this.paperName = paperName;
            this.status = "running";
            this.startTime = new Date();
            this.currentGeneration = 0;
            this.maxGenerations = 200;
            this.bestFitness = 0.0;
            this.avgFitness = 0.0;
            this.convergenceSpeed = 0.0;
            this.diversityIndex = 1.0;
            this.constraintViolations = 0;
            this.isPaused = false;
        }
        
        // Getter和Setter方法
        public String getPaperId() {
            return paperId;
        }
        
        public void setPaperId(String paperId) {
            this.paperId = paperId;
        }
        
        public String getPaperName() {
            return paperName;
        }
        
        public void setPaperName(String paperName) {
            this.paperName = paperName;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
            if ("completed".equals(status) || "failed".equals(status)) {
                this.endTime = new Date();
            }
        }
        
        public Date getStartTime() {
            return startTime;
        }
        
        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }
        
        public Date getEndTime() {
            return endTime;
        }
        
        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }
        
        public int getCurrentGeneration() {
            return currentGeneration;
        }
        
        public void setCurrentGeneration(int currentGeneration) {
            this.currentGeneration = currentGeneration;
        }
        
        public int getMaxGenerations() {
            return maxGenerations;
        }
        
        public void setMaxGenerations(int maxGenerations) {
            this.maxGenerations = maxGenerations;
        }
        
        public double getBestFitness() {
            return bestFitness;
        }
        
        public void setBestFitness(double bestFitness) {
            this.bestFitness = bestFitness;
        }
        
        public double getAvgFitness() {
            return avgFitness;
        }
        
        public void setAvgFitness(double avgFitness) {
            this.avgFitness = avgFitness;
        }
        
        public double getConvergenceSpeed() {
            return convergenceSpeed;
        }
        
        public void setConvergenceSpeed(double convergenceSpeed) {
            this.convergenceSpeed = convergenceSpeed;
        }
        
        public double getDiversityIndex() {
            return diversityIndex;
        }
        
        public void setDiversityIndex(double diversityIndex) {
            this.diversityIndex = diversityIndex;
        }
        
        public int getConstraintViolations() {
            return constraintViolations;
        }
        
        public void setConstraintViolations(int constraintViolations) {
            this.constraintViolations = constraintViolations;
        }
        
        public boolean isPaused() {
            return isPaused;
        }
        
        public void setPaused(boolean paused) {
            isPaused = paused;
            if (paused) {
                this.status = "paused";
            } else if ("paused".equals(this.status)) {
                this.status = "running";
            }
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public Map<String, Object> getAdditionalMetrics() {
            return additionalMetrics;
        }
        
        public void setAdditionalMetrics(Map<String, Object> additionalMetrics) {
            this.additionalMetrics = additionalMetrics;
        }
        
        public void addMetric(String key, Object value) {
            this.additionalMetrics.put(key, value);
        }
        
        public Object getMetric(String key) {
            return this.additionalMetrics.get(key);
        }
        
        /**
         * 获取运行时长（毫秒）
         */
        public long getRunningDuration() {
            Date endTime = this.endTime != null ? this.endTime : new Date();
            return endTime.getTime() - startTime.getTime();
        }
        
        /**
         * 获取进度百分比
         */
        public double getProgress() {
            if (maxGenerations <= 0) return 0.0;
            return Math.min(100.0, (double) currentGeneration / maxGenerations * 100.0);
        }
        
        /**
         * 判断是否收敛
         */
        public boolean isConverged() {
            return bestFitness >= 0.95 || currentGeneration >= maxGenerations;
        }
        
        /**
         * 转换为Map用于JSON序列化
         */
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("paperId", paperId);
            map.put("paperName", paperName);
            map.put("status", status);
            map.put("startTime", startTime);
            map.put("endTime", endTime);
            map.put("currentGeneration", currentGeneration);
            map.put("maxGenerations", maxGenerations);
            map.put("bestFitness", bestFitness);
            map.put("avgFitness", avgFitness);
            map.put("convergenceSpeed", convergenceSpeed);
            map.put("diversityIndex", diversityIndex);
            map.put("constraintViolations", constraintViolations);
            map.put("isPaused", isPaused);
            map.put("errorMessage", errorMessage);
            map.put("runningDuration", getRunningDuration());
            map.put("progress", getProgress());
            map.put("isConverged", isConverged());
            map.putAll(additionalMetrics);
            return map;
        }
    }
}
