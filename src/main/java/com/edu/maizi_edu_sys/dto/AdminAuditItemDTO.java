package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 管理员审核项DTO
 */
@Data
public class AdminAuditItemDTO {
    /**
     * 审核ID
     */
    private Long id;
    
    /**
     * 题目ID
     */
    private Long topicId;
    
    /**
     * 题目标题
     */
    private String topicTitle;
    
    /**
     * 题目内容
     */
    private String topicContent;
    
    /**
     * 题目类型
     */
    private String topicType;
    
    /**
     * 知识点名称
     */
    private String knowledgePoint;
    
    /**
     * 提交者ID
     */
    private Long submitterId;
    
    /**
     * 提交者姓名
     */
    private String submitterName;
    
    /**
     * 审核状态 (0-待审核, 1-已通过, 2-已拒绝)
     */
    private Integer status;
    
    /**
     * 审核意见
     */
    private String auditComment;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 审核人姓名
     */
    private String auditorName;
    
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    
    /**
     * 审核结果 (1-通过, 2-拒绝)
     */
    private Integer auditResult;
} 