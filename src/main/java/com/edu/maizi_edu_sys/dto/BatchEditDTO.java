package com.edu.maizi_edu_sys.dto;

import java.util.List;

/**
 * 批量编辑DTO
 * 用于批量编辑试卷的数据传输
 */
public class BatchEditDTO {
    
    /**
     * 要编辑的试卷ID列表
     */
    private List<Long> paperIds;
    
    /**
     * 标题前缀
     */
    private String titlePrefix;
    
    /**
     * 标题后缀
     */
    private String titleSuffix;
    
    /**
     * 分类标签
     */
    private String categoryTag;
    
    /**
     * 难度等级
     */
    private Integer difficultyLevel;
    
    /**
     * 试卷状态
     */
    private String status;
    
    /**
     * 试卷描述
     */
    private String description;
    
    /**
     * 是否公开
     */
    private Boolean isPublic;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    // 构造函数
    public BatchEditDTO() {}
    
    public BatchEditDTO(List<Long> paperIds) {
        this.paperIds = paperIds;
    }
    
    // Getter和Setter方法
    public List<Long> getPaperIds() {
        return paperIds;
    }
    
    public void setPaperIds(List<Long> paperIds) {
        this.paperIds = paperIds;
    }
    
    public String getTitlePrefix() {
        return titlePrefix;
    }
    
    public void setTitlePrefix(String titlePrefix) {
        this.titlePrefix = titlePrefix;
    }
    
    public String getTitleSuffix() {
        return titleSuffix;
    }
    
    public void setTitleSuffix(String titleSuffix) {
        this.titleSuffix = titleSuffix;
    }
    
    public String getCategoryTag() {
        return categoryTag;
    }
    
    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }
    
    public Integer getDifficultyLevel() {
        return difficultyLevel;
    }
    
    public void setDifficultyLevel(Integer difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsPublic() {
        return isPublic;
    }
    
    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    /**
     * 检查是否有有效的编辑字段
     */
    public boolean hasValidFields() {
        return (titlePrefix != null && !titlePrefix.trim().isEmpty()) ||
               (titleSuffix != null && !titleSuffix.trim().isEmpty()) ||
               (categoryTag != null && !categoryTag.trim().isEmpty()) ||
               difficultyLevel != null ||
               (status != null && !status.trim().isEmpty()) ||
               (description != null && !description.trim().isEmpty()) ||
               isPublic != null ||
               (tags != null && !tags.isEmpty());
    }
    
    @Override
    public String toString() {
        return "BatchEditDTO{" +
                "paperIds=" + paperIds +
                ", titlePrefix='" + titlePrefix + '\'' +
                ", titleSuffix='" + titleSuffix + '\'' +
                ", categoryTag='" + categoryTag + '\'' +
                ", difficultyLevel=" + difficultyLevel +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                ", isPublic=" + isPublic +
                ", tags=" + tags +
                '}';
    }
}