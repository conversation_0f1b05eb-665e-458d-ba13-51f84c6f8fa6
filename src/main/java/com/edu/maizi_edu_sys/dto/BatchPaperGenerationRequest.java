package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 批量生成试卷请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchPaperGenerationRequest {

    @NotBlank(message = "试卷标题不能为空")
    private String title;

    @NotNull(message = "生成套数不能为空")
    @Min(value = 1, message = "生成套数至少为1")
    @Max(value = 10, message = "生成套数最多为10")
    private Integer paperCount;

    private String paperType = "standard"; // 试卷版本

    @Valid
    private List<KnowledgePointConfigRequest> knowledgePointConfigs;

    private Double totalScore; // 试卷总分

    @NotNull(message = "题型数量配置不能为空")
    private Map<String, Integer> topicTypeCounts;

    @NotNull(message = "题型分数配置不能为空")
    private Map<String, Integer> typeScoreMap;

    @Valid
    private DifficultyDistribution difficultyCriteria;

    /**
     * 难度分布配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifficultyDistribution {
        private Double easy;
        private Double medium;
        private Double hard;
    }
}
