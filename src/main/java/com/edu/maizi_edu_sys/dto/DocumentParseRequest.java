package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文档解析请求DTO
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentParseRequest {

    /**
     * 文档URL或文件路径
     */
    private String url;

    /**
     * 批量文档URL列表
     */
    private List<String> urls;

    /**
     * 文件数量（批量上传时使用）
     */
    private Integer fileCount;

    /**
     * 是否启用OCR识别
     */
    @Builder.Default
    private Boolean ocr = true;

    /**
     * 是否启用公式识别
     */
    @Builder.Default
    private Boolean formula = true;

    /**
     * 是否启用表格识别
     */
    @Builder.Default
    private Boolean table = true;

    /**
     * 语言设置
     */
    @Builder.Default
    private String language = "ch";

    /**
     * 输出格式
     */
    private List<String> outputFormat;

    /**
     * 额外格式
     */
    private List<String> extraFormats;

    /**
     * 模型版本
     */
    @Builder.Default
    private String modelVersion = "v2";

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 自定义参数
     */
    private String customParams;

    /**
     * 预估页数
     */
    private Integer estimatedPages;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 页面范围
     */
    private List<String> pageRanges;

    /**
     * 文件列表（批量处理时使用）
     */
    private List<FileInfo> files;

    /**
     * 文件信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileInfo {
        private String url;
        private String fileName;
        private Long fileSize;
        private String fileType;
        private Integer estimatedPages;
        private String dataId;
        private Boolean isOcr;
        private java.util.List<String> pageRanges;
    }
}
