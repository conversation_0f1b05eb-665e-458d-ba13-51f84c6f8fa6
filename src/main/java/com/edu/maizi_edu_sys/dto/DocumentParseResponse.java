package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档解析响应DTO
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Data
@Accessors(chain = true)
public class DocumentParseResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 批次ID (批量处理时)
     */
    private String batchId;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 任务状态
     */
    private String state;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 结果压缩包URL
     */
    private String fullZipUrl;

    /**
     * 错误信息
     */
    private String errMsg;

    /**
     * 解析进度
     */
    private ExtractProgress extractProgress;

    /**
     * 解析结果列表
     */
    private List<ParseResult> results;

    /**
     * 解析进度信息
     */
    @Data
    @Accessors(chain = true)
    public static class ExtractProgress {
        /**
         * 已解析页数
         */
        private Integer extractedPages;

        /**
         * 总页数
         */
        private Integer totalPages;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 进度百分比
         */
        private Double progressPercent;
    }

    /**
     * 解析结果
     */
    @Data
    @Accessors(chain = true)
    public static class ParseResult {
        /**
         * 结果类型
         */
        private String type;

        /**
         * 内容
         */
        private String content;

        /**
         * 文件URL
         */
        private String fileUrl;

        /**
         * 质量评分
         */
        private Integer qualityScore;
    }
}
