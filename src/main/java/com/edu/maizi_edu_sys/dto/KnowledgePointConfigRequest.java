package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * DTO for configuring question generation for a specific knowledge point.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgePointConfigRequest {

    @NotNull(message = "Knowledge ID cannot be null")
    private Long knowledgeId;

    // 知识点名称（用于前端显示，后端填充）
    private String knowledgeName;

    @Min(value = 0, message = "Question count cannot be negative")
    private Integer questionCount; // Number of questions from this specific knowledge point. If null, global type counts are used.

    @NotNull(message = "IncludeShortAnswer flag cannot be null")
    private Boolean includeShortAnswer = false; // Whether to include short answer questions from this knowledge point, defaults to false

    // 简答题独立数量控制（不计入基础题量）
    @Min(value = 0, message = "Short answer count cannot be negative")
    private Integer shortAnswerCount = 0; // 简答题数量（额外的，不算在questionCount中）

    /**
     * 获取该知识点的基础题目数量（不包括简答题）
     */
    public Integer getBasicQuestionCount() {
        return questionCount != null ? questionCount : 0;
    }

    /**
     * 获取该知识点的总题目数量（包括简答题）
     */
    public Integer getTotalQuestionCount() {
        return getBasicQuestionCount() + (shortAnswerCount != null ? shortAnswerCount : 0);
    }

    /**
     * 检查是否有简答题配置
     */
    public boolean hasShortAnswerConfiguration() {
        return (includeShortAnswer != null && includeShortAnswer) &&
               (shortAnswerCount != null && shortAnswerCount > 0);
    }

    /**
     * 获取简答题数量
     */
    public Integer getShortAnswerCount() {
        return shortAnswerCount != null ? shortAnswerCount : 0;
    }

    /**
     * 检查是否需要简答题
     */
    public boolean needsShortAnswer() {
        return (includeShortAnswer != null && includeShortAnswer) ||
               (shortAnswerCount != null && shortAnswerCount > 0);
    }
}