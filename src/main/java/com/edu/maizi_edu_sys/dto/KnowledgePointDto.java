package com.edu.maizi_edu_sys.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 知识点数据传输对象
 * 用于前后端交互的标准化知识点数据结构
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgePointDto {

    /**
     * 知识点ID（主键）
     */
    private Long id;

    /**
     * 知识点ID（know_id，用于题目查询）
     */
    private Integer knowledgeId;

    /**
     * 知识点名称
     */
    private String name;

    /**
     * 分组/分类ID
     */
    private Integer groupId;

    /**
     * 分组/分类名称
     */
    private String groupName;

    /**
     * 是否免费 (1=免费, 0=付费)
     */
    private Integer isFree;

    /**
     * 知识点描述
     */
    private String description;

    /**
     * 关联题目数量
     */
    private Integer topicCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 从Map转换为DTO的静态工厂方法
     */
    public static KnowledgePointDto fromMap(java.util.Map<String, Object> map) {
        if (map == null) {
            return null;
        }

        KnowledgePointDto dto = new KnowledgePointDto();

        // 设置ID (必须字段)
        if (map.containsKey("id")) {
            Object idObj = map.get("id");
            if (idObj instanceof Long) {
                dto.setId((Long) idObj);
            } else if (idObj instanceof Integer) {
                dto.setId(((Integer) idObj).longValue());
            } else if (idObj != null) {
                try {
                    dto.setId(Long.valueOf(idObj.toString()));
                } catch (NumberFormatException e) {
                    // 忽略转换错误
                }
            }
        }

        // 设置知识点ID (know_id)
        if (map.containsKey("knowledgeId")) {
            Object knowIdObj = map.get("knowledgeId");
            System.out.println("从Map中获取knowledgeId: " + knowIdObj);

            if (knowIdObj instanceof Integer) {
                dto.setKnowledgeId((Integer) knowIdObj);
                System.out.println("设置knowledgeId(Integer): " + dto.getKnowledgeId());
            } else if (knowIdObj != null) {
                try {
                    dto.setKnowledgeId(Integer.valueOf(knowIdObj.toString()));
                    System.out.println("设置knowledgeId(String): " + dto.getKnowledgeId());
                } catch (NumberFormatException e) {
                    // 忽略转换错误
                    System.out.println("转换knowledgeId失败: " + e.getMessage());
                }
            }
        } else {
            System.out.println("Map中不包含knowledgeId字段");
        }

        // 设置名称 (尝试多个可能的键)
        if (map.containsKey("name")) {
            dto.setName((String) map.get("name"));
        } else if (map.containsKey("pointName")) {
            dto.setName((String) map.get("pointName"));
        } else if (map.containsKey("knowledgeName")) {
            dto.setName((String) map.get("knowledgeName"));
        } else if (map.containsKey("title")) {
            dto.setName((String) map.get("title"));
        }

        // 设置分组ID
        if (map.containsKey("groupId")) {
            Object groupIdObj = map.get("groupId");
            if (groupIdObj instanceof Integer) {
                dto.setGroupId((Integer) groupIdObj);
            } else if (groupIdObj != null) {
                try {
                    dto.setGroupId(Integer.valueOf(groupIdObj.toString()));
                } catch (NumberFormatException e) {
                    // 忽略转换错误
                }
            }
        } else if (map.containsKey("classificationId")) {
            Object classIdObj = map.get("classificationId");
            if (classIdObj instanceof Integer) {
                dto.setGroupId((Integer) classIdObj);
            } else if (classIdObj != null) {
                try {
                    dto.setGroupId(Integer.valueOf(classIdObj.toString()));
                } catch (NumberFormatException e) {
                    // 忽略转换错误
                }
            }
        }

        // 设置分组名称
        if (map.containsKey("groupName")) {
            dto.setGroupName((String) map.get("groupName"));
        }

        // 设置是否免费
        if (map.containsKey("isFree")) {
            Object isFreeObj = map.get("isFree");
            if (isFreeObj instanceof Integer) {
                dto.setIsFree((Integer) isFreeObj);
            } else if (isFreeObj instanceof Boolean) {
                dto.setIsFree((Boolean) isFreeObj ? 1 : 0);
            } else if (isFreeObj != null) {
                try {
                    dto.setIsFree(Integer.valueOf(isFreeObj.toString()));
                } catch (NumberFormatException e) {
                    // 默认为非免费
                    dto.setIsFree(0);
                }
            } else {
                // 默认为非免费
                dto.setIsFree(0);
            }
        } else {
            // 默认为非免费
            dto.setIsFree(0);
        }

        // 设置描述
        if (map.containsKey("description")) {
            dto.setDescription((String) map.get("description"));
        }

        // 设置题目数量
        if (map.containsKey("topicCount")) {
            Object countObj = map.get("topicCount");
            if (countObj instanceof Integer) {
                dto.setTopicCount((Integer) countObj);
            } else if (countObj != null) {
                try {
                    dto.setTopicCount(Integer.valueOf(countObj.toString()));
                } catch (NumberFormatException e) {
                    // 默认为0
                    dto.setTopicCount(0);
                }
            } else {
                // 默认为0
                dto.setTopicCount(0);
            }
        }

        return dto;
    }
}