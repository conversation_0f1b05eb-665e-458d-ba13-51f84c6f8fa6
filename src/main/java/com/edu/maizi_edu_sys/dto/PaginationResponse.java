package com.edu.maizi_edu_sys.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用分页响应数据结构
 * @param <T> 分页数据项类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationResponse<T> {
    /**
     * 当前页的数据列表
     */
    private List<T> content;
    
    /**
     * 总记录数
     */
    private long totalElements;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 当前页码（从0开始）
     */
    private int number;
    
    /**
     * 每页大小
     */
    private int size;
    
    /**
     * 是否是第一页
     */
    private boolean first;
    
    /**
     * 是否是最后一页
     */
    private boolean last;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 辅助构造方法，从MyBatis Plus的IPage创建
     */
    public static <T> PaginationResponse<T> fromPage(com.baomidou.mybatisplus.core.metadata.IPage<T> page) {
        PaginationResponse<T> response = new PaginationResponse<>();
        response.setContent(page.getRecords());
        response.setTotalElements(page.getTotal());
        response.setTotalPages((int) page.getPages());
        response.setNumber((int) page.getCurrent() - 1); // MyBatis Plus 从1开始，我们转为从0开始
        response.setSize((int) page.getSize());
        response.setFirst(page.getCurrent() == 1);
        response.setLast(page.getCurrent() == page.getPages());
        response.setHasNext(page.getCurrent() < page.getPages());
        response.setHasPrevious(page.getCurrent() > 1);
        return response;
    }
} 