package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 试卷查重请求DTO
 */
@Data
public class PaperDuplicateCheckRequest {
    
    /**
     * 要进行查重的试卷ID列表
     */
    @NotEmpty(message = "试卷ID列表不能为空")
    @Size(min = 2, message = "至少需要选择2套试卷进行对比")
    private List<Long> paperIds;
    
    /**
     * 查重模式
     * EXACT: 精确匹配（默认）
     * SIMILAR: 相似匹配
     */
    private DuplicateCheckMode mode = DuplicateCheckMode.EXACT;
    
    /**
     * 相似度阈值（仅在SIMILAR模式下有效）
     * 范围：0.0-1.0，默认0.8
     */
    private Double similarityThreshold = 0.8;
    
    /**
     * 是否包含题目详情
     */
    private Boolean includeTopicDetails = true;
    
    /**
     * 查重模式枚举
     */
    public enum DuplicateCheckMode {
        EXACT,    // 精确匹配
        SIMILAR   // 相似匹配
    }
}
