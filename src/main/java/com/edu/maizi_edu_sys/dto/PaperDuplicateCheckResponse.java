package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 试卷查重响应DTO
 */
@Data
public class PaperDuplicateCheckResponse {

    /**
     * 查重结果的唯一ID，用于后续查询完整结果
     */
    private String resultId;
    
    /**
     * 参与查重的试卷信息
     */
    private List<PaperInfo> papers;
    
    /**
     * 重复题目分组
     */
    private List<DuplicateGroup> duplicateGroups;
    
    /**
     * 每套试卷的统计信息
     */
    private List<PaperStatistics> paperStatistics;
    
    /**
     * 总重复题目数量
     */
    private Integer totalDuplicateCount;
    
    /**
     * 唯一题目数量
     */
    private Integer uniqueTopicCount;
    
    /**
     * 重复组数量
     */
    private Integer duplicateGroupCount;
    
    /**
     * 试卷信息
     */
    @Data
    public static class PaperInfo {
        private Long id;
        private String title;
        private LocalDateTime createTime;
        private Integer totalScore;
        private Double difficulty;
    }
    
    /**
     * 重复题目分组
     */
    @Data
    public static class DuplicateGroup {
        /**
         * 题目哈希标识
         */
        private String topicHash;
        
        /**
         * 题目详情
         */
        private TopicDetail topicDetail;
        
        /**
         * 出现位置列表
         */
        private List<TopicOccurrence> occurrences;
        
        /**
         * 重复次数
         */
        private Integer duplicateCount;
        
        /**
         * 涉及的试卷数量
         */
        public Integer getInvolvedPaperCount() {
            return (int) occurrences.stream()
                .map(TopicOccurrence::getPaperId)
                .distinct()
                .count();
        }
    }
    
    /**
     * 题目出现位置
     */
    @Data
    public static class TopicOccurrence {
        private Long paperId;
        private String paperTitle;
        private Integer topicIndex;  // 在试卷中的位置（从1开始）
        private Integer topicId;
    }
    
    /**
     * 题目详情
     */
    @Data
    public static class TopicDetail {
        private Integer id;
        private String title;
        private String type;
        private String options;
        private String answer;
        private Integer score;
        private Double difficulty;
        
        /**
         * 获取题型中文名称
         */
        public String getTypeDisplayName() {
            if (type == null) return "未知";
            switch (type.toLowerCase()) {
                case "choice": return "单选题";
                case "multiple": return "多选题";
                case "judge": return "判断题";
                case "fill": return "填空题";
                case "short": return "简答题";
                case "subjective": return "主观题";
                case "group": return "组合题";
                default: return type;
            }
        }
        
        /**
         * 获取难度显示名称（与后端难度定义保持一致）
         */
        public String getDifficultyDisplayName() {
            if (difficulty == null) return "未知";
            // 与后端难度定义保持一致：≤0.4为简单，≤0.7为中等，≤1为难题
            if (difficulty <= 0.4) return "简单";
            if (difficulty <= 0.7) return "中等";
            if (difficulty <= 1.0) return "困难";
            return "简单";
        }
    }
    
    /**
     * 试卷统计信息
     */
    @Data
    public static class PaperStatistics {
        private Long paperId;
        private String paperTitle;
        private Integer totalTopics;      // 总题目数
        private Integer duplicateTopics;  // 重复题目数
        private Integer uniqueTopics;     // 唯一题目数
        private Double duplicateRate;     // 重复率
        
        /**
         * 获取重复率百分比字符串
         */
        public String getDuplicateRatePercent() {
            return String.format("%.1f%%", duplicateRate * 100);
        }
        
        /**
         * 获取重复等级
         */
        public String getDuplicateLevel() {
            if (duplicateRate == null || duplicateRate == 0) return "无重复";
            if (duplicateRate < 0.1) return "轻微重复";
            if (duplicateRate < 0.3) return "中度重复";
            if (duplicateRate < 0.5) return "高度重复";
            return "严重重复";
        }
        
        /**
         * 获取重复等级对应的CSS类名
         */
        public String getDuplicateLevelClass() {
            if (duplicateRate == null || duplicateRate == 0) return "success";
            if (duplicateRate < 0.1) return "info";
            if (duplicateRate < 0.3) return "warning";
            return "danger";
        }
    }
}
