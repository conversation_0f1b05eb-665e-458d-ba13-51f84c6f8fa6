package com.edu.maizi_edu_sys.dto;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 试卷生成响应DTO
 */
@Data
public class PaperGenerationResponse {

    /**
     * 试卷ID
     */
    private Long id;

    /**
     * 试卷标题
     */
    private String title;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 实际总分
     */
    private Integer actualTotalScore;

    /**
     * 知识点ID
     */
    private Integer knowledgeId;

    /**
     * 知识点名称
     */
    private String knowledgeName;

    /**
     * 选中的题目
     */
    private List<Topic> selectedTopics;

    /**
     * 各题型数量统计
     */
    private Map<String, Integer> typeCountMap;

    /**
     * 各题型分数统计
     */
    private Map<String, Integer> typeScoreMap;

    /**
     * 试卷难度
     */
    private Double paperDifficulty;

    /**
     * 是否成功生成
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 警告信息（非阻断性提示）
     */
    private String warningMessage;

    /**
     * 难度分布
     */
    private Map<String, Double> difficultyDistribution;
} 