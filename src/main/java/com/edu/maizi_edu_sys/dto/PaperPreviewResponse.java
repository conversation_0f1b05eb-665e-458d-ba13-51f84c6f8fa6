package com.edu.maizi_edu_sys.dto;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 试卷预览响应DTO
 * 包含预览题目和统计信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperPreviewResponse {
    
    /**
     * 试卷标题
     */
    private String title;

    /**
     * 预览题目列表（按题型分组）
     * 键为题型名称（如"singleChoice", "multipleChoice"等）
     * 值为该题型的预览题目列表
     */
    private Map<String, List<Topic>> previewTopicsByType;

    /**
     * 各题型的可用题目总数统计
     * 键为题型名称，值为该题型在所选知识点中的总题目数
     */
    private Map<String, Integer> availableCountsByType;

    /**
     * 各题型的请求数量
     * 键为题型名称，值为用户请求的该题型题目数量
     */
    private Map<String, Integer> requestedCountsByType;

    /**
     * 各题型的分值
     * 键为题型名称，值为该题型每题的分值
     */
    private Map<String, Integer> scoresByType;

    /**
     * 预览统计信息
     */
    private PreviewStats stats;

    /**
     * 警告信息列表
     * 例如：某些题型题目不足的警告
     */
    private List<String> warnings;

    /**
     * 预览统计信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PreviewStats {
        /**
         * 总题目数（用户请求的）
         */
        private Integer totalRequestedQuestions;

        /**
         * 总可用题目数
         */
        private Integer totalAvailableQuestions;

        /**
         * 预期总分
         */
        private Integer expectedTotalScore;

        /**
         * 实际可达到的总分
         */
        private Integer actualTotalScore;

        /**
         * 难度分布统计
         */
        private Map<String, Integer> difficultyDistribution;

        /**
         * 知识点分布统计
         */
        private Map<String, Integer> knowledgePointDistribution;
    }
}
