package com.edu.maizi_edu_sys.dto;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 试卷统计信息DTO
 * 用于传输试卷相关的统计数据
 */
public class PaperStatisticsDTO {
    
    /**
     * 总试卷数
     */
    private Long totalPapers;
    
    /**
     * 今日创建的试卷数
     */
    private Long todayPapers;
    
    /**
     * 本周创建的试卷数
     */
    private Long weekPapers;
    
    /**
     * 本月创建的试卷数
     */
    private Long monthPapers;
    
    /**
     * 总下载次数
     */
    private Long totalDownloads;
    
    /**
     * 今日下载次数
     */
    private Long todayDownloads;
    
    /**
     * 平均试卷分数
     */
    private Double averageScore;
    
    /**
     * 最高试卷分数
     */
    private Double maxScore;
    
    /**
     * 最低试卷分数
     */
    private Double minScore;
    
    /**
     * 按难度分组的试卷数量
     * key: 难度等级 (1-简单, 2-中等, 3-困难)
     * value: 试卷数量
     */
    private Map<Integer, Long> papersByDifficulty;
    
    /**
     * 按状态分组的试卷数量
     * key: 状态 (draft-草稿, published-已发布, archived-已归档)
     * value: 试卷数量
     */
    private Map<String, Long> papersByStatus;
    
    /**
     * 按分数范围分组的试卷数量
     * key: 分数范围 (0-50, 51-80, 81-100, 100+)
     * value: 试卷数量
     */
    private Map<String, Long> papersByScoreRange;
    
    /**
     * 最近7天的创建趋势
     * key: 日期字符串 (yyyy-MM-dd)
     * value: 当日创建的试卷数量
     */
    private Map<String, Long> recentTrend;
    
    /**
     * 最受欢迎的试卷（按下载次数排序）
     */
    private java.util.List<PopularPaperDTO> popularPapers;
    
    /**
     * 统计数据更新时间
     */
    private LocalDateTime updateTime;
    
    // 构造函数
    public PaperStatisticsDTO() {
        this.updateTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getTotalPapers() {
        return totalPapers;
    }
    
    public void setTotalPapers(Long totalPapers) {
        this.totalPapers = totalPapers;
    }
    
    public Long getTodayPapers() {
        return todayPapers;
    }
    
    public void setTodayPapers(Long todayPapers) {
        this.todayPapers = todayPapers;
    }
    
    public Long getWeekPapers() {
        return weekPapers;
    }
    
    public void setWeekPapers(Long weekPapers) {
        this.weekPapers = weekPapers;
    }
    
    public Long getMonthPapers() {
        return monthPapers;
    }
    
    public void setMonthPapers(Long monthPapers) {
        this.monthPapers = monthPapers;
    }
    
    public Long getTotalDownloads() {
        return totalDownloads;
    }
    
    public void setTotalDownloads(Long totalDownloads) {
        this.totalDownloads = totalDownloads;
    }
    
    public Long getTodayDownloads() {
        return todayDownloads;
    }
    
    public void setTodayDownloads(Long todayDownloads) {
        this.todayDownloads = todayDownloads;
    }
    
    public Double getAverageScore() {
        return averageScore;
    }
    
    public void setAverageScore(Double averageScore) {
        this.averageScore = averageScore;
    }
    
    public Double getMaxScore() {
        return maxScore;
    }
    
    public void setMaxScore(Double maxScore) {
        this.maxScore = maxScore;
    }
    
    public Double getMinScore() {
        return minScore;
    }
    
    public void setMinScore(Double minScore) {
        this.minScore = minScore;
    }
    
    public Map<Integer, Long> getPapersByDifficulty() {
        return papersByDifficulty;
    }
    
    public void setPapersByDifficulty(Map<Integer, Long> papersByDifficulty) {
        this.papersByDifficulty = papersByDifficulty;
    }
    
    public Map<String, Long> getPapersByStatus() {
        return papersByStatus;
    }
    
    public void setPapersByStatus(Map<String, Long> papersByStatus) {
        this.papersByStatus = papersByStatus;
    }
    
    public Map<String, Long> getPapersByScoreRange() {
        return papersByScoreRange;
    }
    
    public void setPapersByScoreRange(Map<String, Long> papersByScoreRange) {
        this.papersByScoreRange = papersByScoreRange;
    }
    
    public Map<String, Long> getRecentTrend() {
        return recentTrend;
    }
    
    public void setRecentTrend(Map<String, Long> recentTrend) {
        this.recentTrend = recentTrend;
    }
    
    public java.util.List<PopularPaperDTO> getPopularPapers() {
        return popularPapers;
    }
    
    public void setPopularPapers(java.util.List<PopularPaperDTO> popularPapers) {
        this.popularPapers = popularPapers;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    /**
     * 受欢迎试卷DTO
     */
    public static class PopularPaperDTO {
        private Long id;
        private String title;
        private Long downloadCount;
        private Double totalScore;
        private LocalDateTime createTime;
        
        public PopularPaperDTO() {}
        
        public PopularPaperDTO(Long id, String title, Long downloadCount, Double totalScore, LocalDateTime createTime) {
            this.id = id;
            this.title = title;
            this.downloadCount = downloadCount;
            this.totalScore = totalScore;
            this.createTime = createTime;
        }
        
        // Getter和Setter方法
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public Long getDownloadCount() {
            return downloadCount;
        }
        
        public void setDownloadCount(Long downloadCount) {
            this.downloadCount = downloadCount;
        }
        
        public Double getTotalScore() {
            return totalScore;
        }
        
        public void setTotalScore(Double totalScore) {
            this.totalScore = totalScore;
        }
        
        public LocalDateTime getCreateTime() {
            return createTime;
        }
        
        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }
    }
    
    @Override
    public String toString() {
        return "PaperStatisticsDTO{" +
                "totalPapers=" + totalPapers +
                ", todayPapers=" + todayPapers +
                ", weekPapers=" + weekPapers +
                ", monthPapers=" + monthPapers +
                ", totalDownloads=" + totalDownloads +
                ", todayDownloads=" + todayDownloads +
                ", averageScore=" + averageScore +
                ", maxScore=" + maxScore +
                ", minScore=" + minScore +
                ", updateTime=" + updateTime +
                '}';
    }
}