package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 题目审核DTO
 * 
 * 修复说明：将所有大整数ID字段改为String类型，避免JavaScript精度丢失
 */
@Data
public class TopicAuditDTO {
    
    /**
     * 审核记录ID - 使用String类型避免JavaScript长整数精度丢失
     */
    private String id;
    
    /**
     * 用户ID - 使用String类型避免JavaScript长整数精度丢失
     */
    private String userId;
    
    /**
     * 提交用户名
     */
    private String username;
    
    /**
     * 知识点ID
     */
    private Integer knowId;
    
    /**
     * 知识点名称
     */
    private String knowledgeName;
    
    private String type;
    private String title;
    
    /**
     * 题目选项 - 使用@JsonRawValue避免双重JSON序列化
     */
    @com.fasterxml.jackson.annotation.JsonRawValue
    private String options;
    
    /**
     * 子题目 - 使用@JsonRawValue避免双重JSON序列化
     */
    @com.fasterxml.jackson.annotation.JsonRawValue
    private String subs;
    private String answer;
    private String parse;
    private Integer score;
    private String source;
    private Double difficulty;
    private String tags;
    private Integer auditStatus;
    
    /**
     * 审核状态文本
     */
    private String auditStatusText;
    
    /**
     * 审核员ID - 使用String类型避免JavaScript长整数精度丢失
     */
    private String auditorId;
    
    /**
     * 审核员名称
     */
    private String auditorName;
    
    private LocalDateTime auditTime;
    private String auditComment;
    private Integer autoApproved;
    private LocalDateTime submitTime;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
} 