package com.edu.maizi_edu_sys.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * AI题目修正结果DTO
 * 对应大模型返回的修正JSON格式
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopicCorrectionDTO {
    
    /**
     * 题目ID
     */
    @JsonProperty("id")
    private Integer id;
    
    /**
     * 修正原因说明
     */
    @JsonProperty("reason")
    private String reason;
    
    /**
     * 需要更新的字段映射
     * key: 字段名 (type, title, options, subs, answer, parse, difficulty)
     * value: 新值
     */
    @JsonProperty("updates")
    private Map<String, Object> updates;
    
    /**
     * 验证修正结果是否有效
     */
    public boolean isValid() {
        return id != null && id > 0 && 
               reason != null && !reason.trim().isEmpty() &&
               updates != null && !updates.isEmpty();
    }
    
    /**
     * 获取安全的字段值（确保options和subs是JSON字符串，并过滤null值）
     */
    public Map<String, Object> getSafeUpdates() {
        if (updates == null) {
            return null;
        }
        
        // 确保options和subs字段是JSON字符串格式，并过滤null值
        Map<String, Object> safeUpdates = new java.util.HashMap<>();
        
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 跳过null值
            if (value == null) {
                continue;
            }
            
            // 处理options字段
            if ("options".equals(key) && !(value instanceof String)) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    safeUpdates.put(key, mapper.writeValueAsString(value));
                } catch (Exception e) {
                    // 如果转换失败，跳过该字段
                    continue;
                }
            }
            // 处理subs字段
            else if ("subs".equals(key) && !(value instanceof String)) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    safeUpdates.put(key, mapper.writeValueAsString(value));
                } catch (Exception e) {
                    // 如果转换失败，跳过该字段
                    continue;
                }
            }
            // 其他字段直接添加
            else {
                safeUpdates.put(key, value);
            }
        }
        
        return safeUpdates.isEmpty() ? null : safeUpdates;
    }
    
    @Override
    public String toString() {
        return String.format("TopicCorrectionDTO{id=%d, reason='%s', updates=%s}", 
                           id, reason, updates != null ? updates.keySet() : "null");
    }
}