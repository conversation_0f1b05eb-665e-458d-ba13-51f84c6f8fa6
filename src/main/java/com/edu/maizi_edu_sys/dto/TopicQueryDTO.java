package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class TopicQueryDTO {

    private String keyword; // For searching in title, options, etc.
    private String type;    // For filtering by topic type (e.g., single_choice, multiple_choice)
    private Double difficultyMin;
    private Double difficultyMax;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAtStart;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAtEnd;

    private Integer knowId; // To filter by knowledge point ID

    // Standard pagination parameters
    private Integer pageNum = 1;
    private Integer pageSize = 10;

    // Sorting parameters
    private String sortBy; // Field to sort by (e.g., "createdAt", "difficulty", "type")
    private String sortOrder; // Sort order ("ASC" or "DESC")
}
