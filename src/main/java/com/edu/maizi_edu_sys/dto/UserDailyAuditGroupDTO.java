package com.edu.maizi_edu_sys.dto;

import lombok.Data;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户每日审核分组DTO
 */
@Data
public class UserDailyAuditGroupDTO {
    
    /**
     * 用户ID - 使用String类型避免JavaScript长整数精度丢失
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 提交日期
     */
    private LocalDate submitDate;
    
    /**
     * 提交时间段（小时）
     */
    private String submitHour = "";
    
    /**
     * 该用户当天提交的题目数量
     */
    private Integer topicCount = 0;
    
    /**
     * 待审核题目数量
     */
    private Integer pendingCount = 0;
    
    /**
     * 已通过题目数量
     */
    private Integer approvedCount = 0;
    
    /**
     * 已拒绝题目数量
     */
    private Integer rejectedCount = 0;
    
    /**
     * 该组中最早的提交时间
     */
    private String earliestSubmitTime = "";
    
    /**
     * 该组中最晚的提交时间
     */
    private String latestSubmitTime = "";
    
    /**
     * 该组下的审核ID列表字符串（用于接收GROUP_CONCAT结果）
     */
    private String auditIds;
    
    /**
     * 批次信息（单批次/多批次）
     */
    private String batchInfo = "单批次";
    
    /**
     * 题目类型统计 - 直接使用数量字段
     */
    private Integer choiceCount = 0;        // 选择题数量
    private Integer multipleCount = 0;      // 多选题数量
    private Integer judgeCount = 0;         // 判断题数量
    private Integer fillCount = 0;          // 填空题数量
    private Integer shortCount = 0;         // 简答题数量
    private Integer essayCount = 0;         // 论述题数量
    
    /**
     * 解析审核ID列表
     */
    public List<Long> getAuditIdList() {
        if (auditIds == null || auditIds.trim().isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return Arrays.stream(auditIds.split(","))
                    .map(String::trim)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取通过率
     */
    public Double getApprovalRate() {
        if (topicCount == null || topicCount == 0) {
            return 0.0;
        }
        return (double) approvedCount / topicCount * 100;
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (pendingCount > 0) {
            return "待审核";
        } else if (approvedCount > 0 && rejectedCount == 0) {
            return "全部通过";
        } else if (rejectedCount > 0 && approvedCount == 0) {
            return "全部拒绝";
        } else if (approvedCount > 0 && rejectedCount > 0) {
            return "部分通过";
        } else {
            return "无数据";
        }
    }
    
    /**
     * 获取批次描述
     */
    public String getBatchDescription() {
        if (batchInfo == null) {
            return "单批次";
        }
        return batchInfo;
    }
    
    /**
     * 获取题目类型分布摘要
     */
    public String getTypeDistribution() {
        StringBuilder sb = new StringBuilder();
        if (choiceCount > 0) sb.append("单选:").append(choiceCount).append(" ");
        if (multipleCount > 0) sb.append("多选:").append(multipleCount).append(" ");
        if (judgeCount > 0) sb.append("判断:").append(judgeCount).append(" ");
        if (fillCount > 0) sb.append("填空:").append(fillCount).append(" ");
        if (shortCount > 0) sb.append("简答:").append(shortCount).append(" ");
        if (essayCount > 0) sb.append("论述:").append(essayCount).append(" ");
        return sb.toString().trim();
    }
    
    /**
     * 获取时间跨度描述
     */
    public String getTimeSpanDescription() {
        if (earliestSubmitTime != null && latestSubmitTime != null && 
            !earliestSubmitTime.equals(latestSubmitTime)) {
            return earliestSubmitTime + " ~ " + latestSubmitTime;
        } else if (earliestSubmitTime != null) {
            return earliestSubmitTime;
        } else {
            return "未知时间";
        }
    }
} 