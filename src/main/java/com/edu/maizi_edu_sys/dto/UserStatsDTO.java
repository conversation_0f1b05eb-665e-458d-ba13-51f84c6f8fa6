package com.edu.maizi_edu_sys.dto;

import lombok.Data;

/**
 * 用户统计DTO
 */
@Data
public class UserStatsDTO {
    /**
     * 总上传量
     */
    private Long totalUploaded;
    
    /**
     * 已通过审核
     */
    private Long approvedCount;
    
    /**
     * 待审核
     */
    private Long pendingCount;
    
    /**
     * 已拒绝
     */
    private Long rejectedCount;
    
    /**
     * 本周上传
     */
    private Long weeklyUploaded;
    
    /**
     * 连续上传天数
     */
    private Integer consecutiveDays;
    
    /**
     * 最高单日上传
     */
    private Long maxDailyUpload;
    
    /**
     * 平均审核时长(小时)
     */
    private Double averageAuditTime;
    
    /**
     * 个人排名
     */
    private Integer ranking;
    
    /**
     * 质量评分
     */
    private Double qualityScore;
    
    /**
     * 通过率
     */
    private Double passRate;
} 