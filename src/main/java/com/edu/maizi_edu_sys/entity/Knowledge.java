package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识点实体类
 */
@Data
@TableName("wm_knowledge")
public class Knowledge {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer knowledgeId;

    private String knowledgeName;

    private String groupName;

    private Integer isFree;

    private Integer sort;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableLogic
    private Integer isDeleted;
}