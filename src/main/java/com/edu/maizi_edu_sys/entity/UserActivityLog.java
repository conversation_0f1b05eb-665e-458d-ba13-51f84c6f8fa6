package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户行为日志实体
 */
@Data
@TableName("user_activity_logs")
public class UserActivityLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 活动类型
     */
    private String activityType;
    
    /**
     * 活动描述
     */
    private String description;
    
    /**
     * 模块名称
     */
    private String module;
    
    /**
     * 操作对象ID（如试卷ID、题目ID等）
     */
    private String targetId;
    
    /**
     * 操作对象类型
     */
    private String targetType;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 请求路径
     */
    private String requestPath;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 操作结果（SUCCESS, FAILED）
     */
    private String result;
    
    /**
     * 错误信息（如果操作失败）
     */
    private String errorMessage;
    
    /**
     * 操作耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 额外数据（JSON格式）
     */
    private String extraData;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 活动类型枚举
     */
    public static class ActivityType {
        public static final String LOGIN = "LOGIN";
        public static final String LOGOUT = "LOGOUT";
        public static final String GENERATE_PAPER = "GENERATE_PAPER";
        public static final String UPLOAD_TOPICS = "UPLOAD_TOPICS";
        public static final String DUPLICATE_CHECK = "DUPLICATE_CHECK";
        public static final String VIEW_TOPICS = "VIEW_TOPICS";
        public static final String EDIT_TOPIC = "EDIT_TOPIC";
        public static final String DELETE_TOPIC = "DELETE_TOPIC";
        public static final String EXPORT_PAPER = "EXPORT_PAPER";
        public static final String CHAT_GENERATE = "CHAT_GENERATE";
        public static final String VIEW_PROFILE = "VIEW_PROFILE";
        public static final String UPDATE_PROFILE = "UPDATE_PROFILE";
        public static final String SEARCH = "SEARCH";
        public static final String FILTER = "FILTER";
        public static final String DOWNLOAD = "DOWNLOAD";
    }
    
    /**
     * 模块名称枚举
     */
    public static class Module {
        public static final String PAPER_GENERATION = "PAPER_GENERATION";
        public static final String TOPIC_MANAGEMENT = "TOPIC_MANAGEMENT";
        public static final String DUPLICATE_CHECK = "DUPLICATE_CHECK";
        public static final String USER_MANAGEMENT = "USER_MANAGEMENT";
        public static final String CHAT_SYSTEM = "CHAT_SYSTEM";
        public static final String SYSTEM = "SYSTEM";
    }
    
    /**
     * 操作结果枚举
     */
    public static class Result {
        public static final String SUCCESS = "SUCCESS";
        public static final String FAILED = "FAILED";
    }
}
