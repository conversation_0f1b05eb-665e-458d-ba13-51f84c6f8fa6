package com.edu.maizi_edu_sys.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageDTO {
    private Long id;            // Matches chat_messages.id
    private Long sessionId;     // Matches chat_messages.session_id
    private String content;     // Matches chat_messages.content
    private Boolean isUser;     // Matches chat_messages.is_user
    private LocalDateTime createdAt; // Matches chat_messages.created_at
    
    // Optional derived/convenience fields not directly in database
    private String sender;      // Derived from is_user ("user" or "ai")
} 