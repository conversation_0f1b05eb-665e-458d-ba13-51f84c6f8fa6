package com.edu.maizi_edu_sys.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    private String type;       // Message type (CHAT_REQUEST, CHAT_RESPONSE_CHUNK, etc.)
    private String chatId;     // Chat session ID (as String to handle both String and Long)
    private String senderId;   // Sender identifier
    private String content;    // Message content
    private String token;      // Authentication token
    
    // Helper method to get chatId as Long
    public Long getChatIdAsLong() {
        if (chatId == null || chatId.isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(chatId);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 