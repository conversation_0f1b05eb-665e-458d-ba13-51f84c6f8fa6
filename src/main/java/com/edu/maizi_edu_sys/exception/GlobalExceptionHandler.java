package com.edu.maizi_edu_sys.exception;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;


@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<?> handleValidationExceptions(MethodArgumentNotValidException ex) {
        String message = ex.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        return ApiResponse.error(message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<?> handleConstraintViolation(ConstraintViolationException ex) {
        String message = ex.getConstraintViolations().iterator().next().getMessage();
        return ApiResponse.error(message);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handleException(Exception ex, HttpServletRequest request) {
        // 检查是否是客户端断开连接导致的异常
        if (isClientAbortException(ex)) {
            log.info("客户端断开连接，请求中断 - URI: {}", request.getRequestURI());
            return null; // 直接返回null，避免进一步处理
        }

        log.error("Unexpected error in {}: {}", request.getRequestURI(), ex.getMessage(), ex);

        // 检查请求是否是文件下载相关的
        String requestURI = request.getRequestURI();
        String acceptHeader = request.getHeader("Accept");

        // 如果是PDF/Word下载请求，返回错误文件而不是JSON
        if (isFileDownloadRequest(requestURI, acceptHeader)) {
            return handleFileDownloadError(ex, requestURI);
        }

        // 对于普通API请求，返回JSON响应
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .contentType(MediaType.APPLICATION_JSON)
                .body(ApiResponse.error("服务器内部错误"));
    }

    /**
     * 判断是否是文件下载请求
     */
    private boolean isFileDownloadRequest(String requestURI, String acceptHeader) {
        if (requestURI == null) return false;

        // 检查URL路径
        boolean isDownloadPath = requestURI.contains("/download") ||
                                requestURI.contains("/papers/") && (requestURI.endsWith(".pdf") || requestURI.endsWith(".docx"));

        // 检查Accept头
        boolean isFileAccept = acceptHeader != null &&
                              (acceptHeader.contains("application/pdf") ||
                               acceptHeader.contains("application/vnd.openxmlformats") ||
                               acceptHeader.contains("application/octet-stream"));

        return isDownloadPath || isFileAccept;
    }

    /**
     * 处理文件下载错误，返回错误文件而不是JSON
     */
    private ResponseEntity<Resource> handleFileDownloadError(Exception ex, String requestURI) {
        try {
            // 创建错误PDF内容
            String errorContent = createErrorFileContent(ex, requestURI);
            byte[] errorBytes = errorContent.getBytes("UTF-8");

            ByteArrayResource errorResource = new ByteArrayResource(errorBytes);

            // 根据请求类型返回相应的错误文件
            if (requestURI.contains("word") || requestURI.contains("docx")) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"error.docx\"")
                        .body(errorResource);
            } else {
                // 默认返回文本文件，避免PDF格式问题
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .contentType(MediaType.TEXT_PLAIN)
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"error.txt\"")
                        .body(errorResource);
            }
        } catch (Exception e) {
            log.error("Failed to create error file: {}", e.getMessage());
            // 如果创建错误文件也失败，返回简单的文本响应
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(new ByteArrayResource("文件生成失败，请稍后重试".getBytes()));
        }
    }

    /**
     * 创建错误文件内容
     */
    private String createErrorFileContent(Exception ex, String requestURI) {
        StringBuilder content = new StringBuilder();
        content.append("=== 文件生成错误报告 ===\n\n");
        content.append("请求路径: ").append(requestURI).append("\n");
        content.append("错误时间: ").append(java.time.LocalDateTime.now()).append("\n");
        content.append("错误类型: ").append(ex.getClass().getSimpleName()).append("\n");
        content.append("错误信息: ").append(ex.getMessage() != null ? ex.getMessage() : "未知错误").append("\n\n");
        content.append("解决建议:\n");
        content.append("1. 请稍后重试\n");
        content.append("2. 如果问题持续存在，请联系系统管理员\n");
        content.append("3. 请检查试卷数据是否完整\n\n");
        content.append("技术支持: 请将此错误报告发送给技术人员\n");

        return content.toString();
    }

    /**
     * 检查异常是否是客户端断开连接导致的
     */
    private boolean isClientAbortException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }

        // 检查异常类型
        if (throwable instanceof org.apache.catalina.connector.ClientAbortException) {
            return true;
        }

        // 检查异常消息
        String message = throwable.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("connection reset") ||
                lowerMessage.contains("broken pipe") ||
                lowerMessage.contains("connection aborted") ||
                lowerMessage.contains("你的主机中的软件中止了一个已建立的连接") ||
                lowerMessage.contains("远程主机强迫关闭了一个现有的连接") ||
                lowerMessage.contains("client abort")) {
                return true;
            }
        }

        // 检查根本原因
        Throwable cause = throwable.getCause();
        if (cause != null && cause != throwable) {
            return isClientAbortException(cause);
        }

        return false;
    }
}