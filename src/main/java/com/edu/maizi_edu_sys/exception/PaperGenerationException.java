package com.edu.maizi_edu_sys.exception;

import java.util.Map;

/**
 * 组卷异常类
 * 用于处理组卷过程中的各种异常情况
 */
public class PaperGenerationException extends RuntimeException {
    
    private final ErrorCode errorCode;
    private final String userMessage;
    private final Map<String, Object> context;
    
    public PaperGenerationException(ErrorCode errorCode, String userMessage) {
        this(errorCode, userMessage, null, null);
    }
    
    public PaperGenerationException(ErrorCode errorCode, String userMessage, Map<String, Object> context) {
        this(errorCode, userMessage, context, null);
    }
    
    public PaperGenerationException(ErrorCode errorCode, String userMessage, Map<String, Object> context, Throwable cause) {
        super(errorCode.getMessage() + (userMessage != null ? ": " + userMessage : ""), cause);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
        this.context = context;
    }
    
    public ErrorCode getErrorCode() {
        return errorCode;
    }
    
    public String getUserMessage() {
        return userMessage;
    }
    
    public Map<String, Object> getContext() {
        return context;
    }
    
    /**
     * 错误代码枚举
     */
    public enum ErrorCode {
        INSUFFICIENT_TOPICS(1001, "题库题目不足"),
        CONSTRAINT_CONFLICT(1002, "约束条件冲突"),
        ALGORITHM_TIMEOUT(1003, "算法执行超时"),
        MEMORY_OVERFLOW(1004, "内存使用超限"),
        INVALID_PARAMETERS(1005, "参数配置无效"),
        DATA_INCONSISTENCY(1006, "数据不一致"),
        THREAD_SAFETY_VIOLATION(1007, "线程安全违规"),
        POPULATION_DEGENERATE(1008, "种群退化"),
        REPAIR_FAILURE(1009, "修复算子失败"),
        EVALUATION_FAILURE(1010, "适应度评估失败");
        
        private final int code;
        private final String message;
        
        ErrorCode(int code, String message) {
            this.code = code;
            this.message = message;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
