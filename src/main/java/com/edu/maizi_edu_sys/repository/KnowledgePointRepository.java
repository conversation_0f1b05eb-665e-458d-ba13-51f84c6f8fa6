package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 知识点数据访问层
 */
@Mapper
public interface KnowledgePointRepository extends BaseMapper<KnowledgePoint> {

    /**
     * 获取有题目的知识点ID列表
     * @param knowledgeIds 需要检查的知识点ID列表
     * @return 有题目的知识点ID列表
     */
    List<Long> findKnowledgePointsWithQuestions(@Param("knowledgeIds") List<Long> knowledgeIds);

    /**
     * 获取知识点题目数量统计
     * @param knowledgeIds 知识点ID列表
     * @return 知识点ID到题目数量的映射
     */
    List<Map<String, Object>> countQuestionsByKnowledgePoints(@Param("knowledgeIds") List<Long> knowledgeIds);

    /**
     * 获取有简答题的知识点ID列表
     * @param knowledgeIds 需要检查的知识点ID列表
     * @return 有简答题的知识点ID列表
     */
    List<Long> findKnowledgePointsWithShortAnswerQuestions(@Param("knowledgeIds") List<Long> knowledgeIds);

    /**
     * 获取知识点的题目类型统计
     * @param knowledgeIds 知识点ID列表
     * @return 知识点ID到题目类型统计的映射
     */
    List<Map<String, Object>> countQuestionTypesByKnowledgePoints(@Param("knowledgeIds") List<Long> knowledgeIds);
}