package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.Paper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 试卷数据访问层，使用MyBatis-Plus实现分页和复杂查询
 */
@Mapper
public interface PaperRepository extends BaseMapper<Paper> {
    
    /**
     * 查询未被删除的指定ID试卷
     * @param id 试卷ID
     * @return 试卷实体（可能为空）
     */
    @Select("SELECT * FROM papers WHERE id = #{id} AND is_deleted = false")
    Optional<Paper> findByIdAndIsDeletedFalse(@Param("id") Long id);
    
    /**
     * 根据类型查询试卷
     * @param type 试卷类型
     * @return 试卷列表
     */
    @Select("SELECT * FROM papers WHERE type = #{type} AND is_deleted = false ORDER BY create_time DESC")
    List<Paper> findByTypeAndIsDeletedFalse(@Param("type") Integer type);

    /**
     * 根据标题关键词查找试卷
     */
    @Select("SELECT * FROM papers WHERE title LIKE CONCAT('%', #{keyword}, '%') AND is_deleted = false ORDER BY create_time DESC")
    List<Paper> findByTitleContaining(@Param("keyword") String keyword);

    /**
     * 查找所有试卷，按创建时间倒序
     */
    @Select("SELECT * FROM papers WHERE is_deleted = false ORDER BY create_time DESC")
    List<Paper> findAllOrderByCreateTimeDesc();
    
    /**
     * 根据标题模糊查询试卷
     * @param title 标题关键词
     * @return 试卷列表
     */
    @Select("SELECT * FROM papers WHERE title LIKE CONCAT('%', #{title}, '%') AND is_deleted = false ORDER BY create_time DESC")
    List<Paper> findByTitleContainingAndIsDeletedFalse(@Param("title") String title);
    
    /**
     * 获取所有未删除的试卷，按创建时间降序排列
     * @return 试卷列表
     */
    @Select("SELECT * FROM papers WHERE is_deleted = false ORDER BY create_time DESC")
    List<Paper> findAllByIsDeletedFalseOrderByCreateTimeDesc();
    
    /**
     * 保存试卷实体
     * MyBatis-Plus的BaseMapper已经提供了insert和updateById方法，可以直接使用
     * 这里定义一个兼容之前JPA风格的save方法，内部使用MyBatis-Plus的方法
     * @param paper 要保存的试卷实体
     * @return 保存后的试卷实体（包含自动生成的ID等）
     */
    default Paper save(Paper paper) {
        if (paper.getId() == null) {
            // 新增
            this.insert(paper);
        } else {
            // 更新
            this.updateById(paper);
        }
        return paper;
    }
} 