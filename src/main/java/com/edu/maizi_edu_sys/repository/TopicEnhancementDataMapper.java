package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目增强数据访问层
 */
@Mapper
public interface TopicEnhancementDataMapper extends BaseMapper<TopicEnhancementData> {

    // 可以在这里添加自定义SQL方法，例如批量更新等

    @Update("UPDATE topic_enhancement_data SET quality_score = #{qualityScore} WHERE topic_id = #{topicId}")
    int updateQualityScore(@Param("topicId") Integer topicId, @Param("qualityScore") BigDecimal qualityScore);

    @Update("UPDATE topic_enhancement_data SET submission_count = submission_count + 1 WHERE topic_id = #{topicId}")
    int incrementSubmissionCount(@Param("topicId") Integer topicId);

    @Update("UPDATE topic_enhancement_data SET error_submission_count = error_submission_count + 1 WHERE topic_id = #{topicId}")
    int incrementErrorSubmissionCount(@Param("topicId") Integer topicId);
    
    @Update("<script>" +
       "UPDATE topic_enhancement_data SET last_used_time = #{timestamp} " +
       "WHERE topic_id IN " +
       "<foreach item='item' index='index' collection='topicIds' open='(' separator=',' close=')'>" +
           "#{item}" +
       "</foreach>" +
    "</script>")
    int updateLastUsedTimestampBatch(@Param("topicIds") List<Integer> topicIds, @Param("timestamp") LocalDateTime timestamp);

    @Select("SELECT id, topic_id, cognitive_level, usage_count, last_used_time, create_time, update_time " +
            "FROM topic_enhancement_data WHERE topic_id=#{topicId}")
    TopicEnhancementData selectById(@Param("topicId") Integer topicId);
    
    @Select("<script>SELECT id, topic_id, cognitive_level, usage_count, last_used_time, create_time, update_time " +
            "FROM topic_enhancement_data WHERE topic_id IN " +
            "<foreach collection='topicIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<TopicEnhancementData> selectBatchIds(@Param("topicIds") List<Integer> topicIds);

    @Update("UPDATE topic_enhancement_data SET usage_count = usage_count + 1 WHERE topic_id = #{topicId}")
    int incrementUsageCount(@Param("topicId") Integer topicId);

    @Update("<script>" +
       "UPDATE topic_enhancement_data SET usage_count = usage_count + 1 " +
       "WHERE topic_id IN " +
       "<foreach item='item' index='index' collection='topicIds' open='(' separator=',' close=')'>" +
           "#{item}" +
       "</foreach>" +
    "</script>")
    int incrementUsageCountBatch(@Param("topicIds") List<Integer> topicIds);

    /**
     * 根据题目ID查询题目增强数据
     */
    @Select("SELECT * FROM topic_enhancement_data WHERE topic_id = #{topicId}")
    TopicEnhancementData selectByTopicId(@Param("topicId") Integer topicId);
} 