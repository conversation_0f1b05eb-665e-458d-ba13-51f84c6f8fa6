package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.UserActivityLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户行为日志Repository
 */
@Mapper
public interface UserActivityLogRepository extends BaseMapper<UserActivityLog> {
    
    /**
     * 获取最近的用户活动日志
     */
    @Select("SELECT * FROM user_activity_logs ORDER BY create_time DESC LIMIT #{limit}")
    List<UserActivityLog> findRecentActivities(@Param("limit") int limit);

    /**
     * 分页获取用户活动日志
     */
    @Select("SELECT * FROM user_activity_logs ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<UserActivityLog> findActivitiesWithPagination(@Param("limit") int limit, @Param("offset") int offset);

    /**
     * 获取活动日志总数
     */
    @Select("SELECT COUNT(*) FROM user_activity_logs")
    long countAllActivities();
    
    /**
     * 根据用户ID获取活动日志
     */
    @Select("SELECT * FROM user_activity_logs WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT #{limit}")
    List<UserActivityLog> findByUserId(@Param("userId") Long userId, @Param("limit") int limit);
    
    /**
     * 根据活动类型获取日志
     */
    @Select("SELECT * FROM user_activity_logs WHERE activity_type = #{activityType} ORDER BY create_time DESC LIMIT #{limit}")
    List<UserActivityLog> findByActivityType(@Param("activityType") String activityType, @Param("limit") int limit);
    
    /**
     * 获取指定时间范围内的活动日志
     */
    @Select("SELECT * FROM user_activity_logs WHERE create_time BETWEEN #{startTime} AND #{endTime} ORDER BY create_time DESC")
    List<UserActivityLog> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取活动统计数据
     */
    @Select("SELECT activity_type, COUNT(*) as count FROM user_activity_logs WHERE create_time >= #{startTime} GROUP BY activity_type ORDER BY count DESC")
    List<Map<String, Object>> getActivityStatistics(@Param("startTime") LocalDateTime startTime);
    
    /**
     * 获取用户活动统计
     */
    @Select("SELECT username, COUNT(*) as count FROM user_activity_logs WHERE create_time >= #{startTime} GROUP BY username ORDER BY count DESC LIMIT #{limit}")
    List<Map<String, Object>> getUserActivityStatistics(@Param("startTime") LocalDateTime startTime, @Param("limit") int limit);
    
    /**
     * 获取模块使用统计
     */
    @Select("SELECT module, COUNT(*) as count FROM user_activity_logs WHERE create_time >= #{startTime} GROUP BY module ORDER BY count DESC")
    List<Map<String, Object>> getModuleStatistics(@Param("startTime") LocalDateTime startTime);
    
    /**
     * 删除过期的日志记录
     */
    @Select("DELETE FROM user_activity_logs WHERE create_time < #{expireTime}")
    int deleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime);
}
