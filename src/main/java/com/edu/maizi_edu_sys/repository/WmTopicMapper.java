package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.WmTopic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface WmTopicMapper extends BaseMapper<WmTopic> {
    
    @Select("SELECT * FROM wm_topic WHERE know_id = #{knowId} AND type = #{type} AND star = #{star}")
    List<WmTopic> findIdsByKnowledgeAndTypeAndDifficulty(Integer knowId, String type, Integer star);
} 