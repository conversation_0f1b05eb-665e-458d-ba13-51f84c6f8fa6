package com.edu.maizi_edu_sys.runner;

import com.edu.maizi_edu_sys.entity.AlgorithmExecution;
import com.edu.maizi_edu_sys.entity.AlgorithmGenerationData;
import com.edu.maizi_edu_sys.entity.FitnessDimensionData;
import com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository;
import com.edu.maizi_edu_sys.repository.AlgorithmGenerationDataRepository;
import com.edu.maizi_edu_sys.repository.FitnessDimensionDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 测试数据初始化器
 * 用于在系统启动时插入算法监控测试数据
 */
@Slf4j
@Component
@Order(100) // 确保在其他初始化器之后运行
public class TestDataInitializer implements ApplicationRunner {

    @Autowired
    private AlgorithmExecutionRepository algorithmExecutionRepository;
    
    @Autowired
    private AlgorithmGenerationDataRepository algorithmGenerationDataRepository;
    
    @Autowired
    private FitnessDimensionDataRepository fitnessDimensionDataRepository;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 检查是否已有测试数据
        long existingCount = algorithmExecutionRepository.selectCount(null);
        if (existingCount > 0) {
            log.info("数据库中已存在 {} 条算法执行记录，跳过测试数据初始化", existingCount);
            return;
        }
        
        log.info("开始初始化算法监控测试数据...");
        
        try {
            initializeTestData();
            log.info("算法监控测试数据初始化完成");
        } catch (Exception e) {
            log.error("初始化测试数据失败", e);
        }
    }
    
    private void initializeTestData() {
        // 创建测试执行记录
        AlgorithmExecution execution1 = createTestExecution(
            "test_paper_001", "数学期末考试 #001", 
            AlgorithmExecution.ExecutionStatus.COMPLETED, 100);
        
        AlgorithmExecution execution2 = createTestExecution(
            "test_paper_002", "物理模拟考试 #002", 
            AlgorithmExecution.ExecutionStatus.RUNNING, 150);
        
        AlgorithmExecution execution3 = createTestExecution(
            "test_paper_003", "化学单元测试 #003", 
            AlgorithmExecution.ExecutionStatus.COMPLETED, 80);
        
        AlgorithmExecution execution4 = createTestExecution(
            "test_paper_004", "英语综合测试 #004", 
            AlgorithmExecution.ExecutionStatus.FAILED, 120);
        
        AlgorithmExecution execution5 = createTestExecution(
            "test_paper_005", "历史期中考试 #005", 
            AlgorithmExecution.ExecutionStatus.PAUSED, 200);
        
        // 保存执行记录
        algorithmExecutionRepository.insert(execution1);
        algorithmExecutionRepository.insert(execution2);
        algorithmExecutionRepository.insert(execution3);
        algorithmExecutionRepository.insert(execution4);
        algorithmExecutionRepository.insert(execution5);
        
        // 为每个执行记录创建代数数据
        createGenerationData(execution1.getId(), 95, true);
        createGenerationData(execution2.getId(), 68, false);
        createGenerationData(execution3.getId(), 78, true);
        createGenerationData(execution4.getId(), 15, false);
        createGenerationData(execution5.getId(), 42, false);
        
        // 创建适应度维度数据
        createFitnessDimensionData(execution1.getId(), 95);
        createFitnessDimensionData(execution2.getId(), 68);
        
        log.info("成功创建 5 个测试执行记录和相关数据");
    }
    
    private AlgorithmExecution createTestExecution(String paperId, String paperName, 
                                                 AlgorithmExecution.ExecutionStatus status, 
                                                 int maxGenerations) {
        AlgorithmExecution execution = new AlgorithmExecution();
        execution.setPaperId(paperId);
        execution.setPaperName(paperName);
        execution.setAlgorithmType("genetic");
        execution.setStatus(status);
        execution.setStartTime(new Date(System.currentTimeMillis() - 3600000)); // 1小时前
        execution.setMaxGenerations(maxGenerations);
        execution.setTargetFitness(0.95);
        execution.setPopulationSize(50);
        execution.setMutationRate(0.1);
        execution.setCrossoverRate(0.8);
        execution.setSelectionMethod("tournament");
        
        if (status == AlgorithmExecution.ExecutionStatus.COMPLETED || 
            status == AlgorithmExecution.ExecutionStatus.FAILED) {
            execution.setEndTime(new Date(System.currentTimeMillis() - 1800000)); // 30分钟前
        }
        
        return execution;
    }
    
    private void createGenerationData(Long executionId, int maxGeneration, boolean completed) {
        for (int gen = 1; gen <= maxGeneration; gen += Math.max(1, maxGeneration / 10)) {
            AlgorithmGenerationData data = new AlgorithmGenerationData();
            data.setExecutionId(executionId);
            data.setGeneration(gen);
            
            // 模拟适应度逐渐提高
            double progress = (double) gen / maxGeneration;
            data.setBestFitness(0.3 + progress * 0.65);
            data.setAvgFitness(0.2 + progress * 0.5);
            data.setWorstFitness(0.1 + progress * 0.3);
            data.setDiversityIndex(1.0 - progress * 0.7);
            data.setConvergenceSpeed(0.02 - progress * 0.015);
            data.setConstraintViolations(Math.max(0, 10 - gen / 5));
            data.setFitnessStdDev(0.2 - progress * 0.1);
            
            algorithmGenerationDataRepository.insert(data);
        }
    }
    
    private void createFitnessDimensionData(Long executionId, int generation) {
        String[] dimensions = {
            "难度分布", "知识点覆盖", "题型平衡", "时间分配",
            "分值分布", "认知层次", "重复度控制", "综合质量"
        };
        
        for (String dimension : dimensions) {
            FitnessDimensionData data = new FitnessDimensionData();
            data.setExecutionId(executionId);
            data.setDimensionName(dimension);
            data.setGeneration(generation);
            data.setDimensionValue(0.8 + Math.random() * 0.2); // 0.8-1.0之间的随机值
            data.setFitnessValue(0.85 + Math.random() * 0.15);
            data.setWeight(1.0);
            
            fitnessDimensionDataRepository.insert(data);
        }
    }
}
