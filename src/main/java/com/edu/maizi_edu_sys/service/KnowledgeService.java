package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.dto.KnowledgeDTO;
import com.edu.maizi_edu_sys.dto.KnowledgeGroupDTO;
import com.edu.maizi_edu_sys.entity.Knowledge;
import com.edu.maizi_edu_sys.mapper.KnowledgeMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 知识点服务
 */
@Service
public class KnowledgeService extends ServiceImpl<KnowledgeMapper, Knowledge> {

    private final KnowledgeMapper knowledgeMapper;
    private static final Logger log = LoggerFactory.getLogger(KnowledgeService.class);

    @Autowired
    public KnowledgeService(KnowledgeMapper knowledgeMapper) {
        this.knowledgeMapper = knowledgeMapper;
    }

    /**
     * 获取所有知识点分类及其知识点数量
     */
    public List<KnowledgeGroupDTO> getKnowledgeGroups() {
        return knowledgeMapper.selectKnowledgeGroups();
    }

    /**
     * 根据分类名获取知识点列表
     */
    public List<KnowledgeDTO> getKnowledgePointsByGroup(String groupName) {
        return knowledgeMapper.selectKnowledgePointsByGroup(groupName);
    }

    /**
     * 根据分类ID获取知识点列表 (并转换为Map)
     */
    public List<Map<String, Object>> getKnowledgePointMapsByGroupId(Integer groupId) {
        List<KnowledgeGroupDTO> groups = getKnowledgeGroups();
        Optional<KnowledgeGroupDTO> targetGroup = groups.stream()
                .filter(g -> g.getId().equals(groupId))
                .findFirst();

        if (targetGroup.isPresent()) {
            String groupName = targetGroup.get().getGroupName();
            List<KnowledgeDTO> knowledgePointsDTOs = getKnowledgePointsByGroup(groupName);

            // Convert List<KnowledgeDTO> to List<Map<String, Object>>
            ObjectMapper objectMapper = new ObjectMapper();
            return knowledgePointsDTOs.stream().map(dto -> {
                try {
                    // First, convert DTO to JSON string, then JSON string to Map
                    String json = objectMapper.writeValueAsString(dto);
                    Map<String, Object> map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});

                    // 确保包含knowledgeId字段
                    if (dto.getKnowledgeId() != null && !map.containsKey("knowledgeId")) {
                        map.put("knowledgeId", dto.getKnowledgeId());
                    }

                    return map;
                } catch (Exception e) {
                    // Log error or handle appropriately
                    log.error("Error converting KnowledgeDTO to Map for ID: " + dto.getId(), e);
                    return null; // Or an empty map, or skip this item
                }
            }).filter(map -> map != null) // Filter out nulls if conversion failed
            .collect(Collectors.toList());
        } else {
            return new ArrayList<>(); // Or throw an exception if group not found
        }
    }

    /**
     * 更新分类排序
     */
    @Transactional
    public void updateGroupOrder(List<KnowledgeGroupDTO> groups) {
        groups.forEach(group -> {
            LambdaUpdateWrapper<Knowledge> updateWrapper = Wrappers.lambdaUpdate(Knowledge.class)
                    .eq(Knowledge::getId, group.getId())
                    .set(Knowledge::getSort, group.getSort());
            update(updateWrapper);
        });
    }

    /**
     * 批量更新知识点排序
     */
    @Transactional
    public boolean batchUpdateSort(List<Knowledge> knowledgeList) {
        return updateBatchById(knowledgeList);
    }

    /**
     * 根据ID获取知识点详情
     */
    public KnowledgeDTO getKnowledgeById(Integer id) {
        Knowledge knowledge = getById(id);
        if (knowledge == null) {
            return null;
        }

        KnowledgeDTO dto = new KnowledgeDTO();
        BeanUtils.copyProperties(knowledge, dto);
        if (knowledge.getCreatedAt() != null) {
            dto.setCreatedAt(knowledge.getCreatedAt().toString());
        }

        return dto;
    }

    /**
     * 创建知识点
     */
    @Transactional
    public boolean createKnowledge(KnowledgeDTO knowledgeDTO) {
        Knowledge knowledge = new Knowledge();
        BeanUtils.copyProperties(knowledgeDTO, knowledge);
        knowledge.setKnowledgeId(generateKnowledgeId());
        knowledge.setIsDeleted(0);

        return save(knowledge);
    }

    /**
     * 更新知识点
     */
    @Transactional
    public boolean updateKnowledge(KnowledgeDTO knowledgeDTO) {
        Knowledge knowledge = getById(knowledgeDTO.getId());
        if (knowledge == null) {
            return false;
        }

        BeanUtils.copyProperties(knowledgeDTO, knowledge);
        return updateById(knowledge);
    }

    /**
     * 删除知识点（软删除）
     */
    @Transactional
    public boolean deleteKnowledge(int id) {
        return removeById(id); // MyBatis-Plus会使用@TableLogic注解自动处理逻辑删除
    }

    /**
     * 批量删除知识点
     */
    @Transactional
    public boolean batchDeleteKnowledge(List<Integer> ids) {
        return removeByIds(ids);
    }

    /**
     * 根据分组名称删除整个分组
     */
    @Transactional
    public boolean deleteByGroupName(String groupName) {
        LambdaQueryWrapper<Knowledge> queryWrapper = Wrappers.lambdaQuery(Knowledge.class)
                .eq(Knowledge::getGroupName, groupName);
        return remove(queryWrapper);
    }

    /**
     * 生成唯一的知识点ID
     */
    private int generateKnowledgeId() {
        Integer maxId = knowledgeMapper.selectMaxKnowledgeId();
        return (maxId == null ? 0 : maxId) + 1;
    }

    /**
     * 统计每个分组的知识点数量
     */
    public List<KnowledgeGroupDTO> countByGroups() {
        return knowledgeMapper.countByGroups();
    }

    /**
     * 根据条件查询知识点
     */
    public List<Knowledge> searchKnowledge(String keyword, String groupName, Integer isFree) {
        LambdaQueryWrapper<Knowledge> queryWrapper = Wrappers.lambdaQuery(Knowledge.class);

        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(Knowledge::getKnowledgeName, keyword);
        }

        if (groupName != null && !groupName.trim().isEmpty()) {
            queryWrapper.eq(Knowledge::getGroupName, groupName);
        }

        if (isFree != null) {
            queryWrapper.eq(Knowledge::getIsFree, isFree);
        }

        queryWrapper.orderByAsc(Knowledge::getSort);

        return list(queryWrapper);
    }

    /**
     * 将Knowledge转换为KnowledgeDTO
     */
    public KnowledgeDTO convertToDTO(Knowledge knowledge) {
        return convertToDTO(knowledge, true);
    }

    /**
     * 将Knowledge转换为KnowledgeDTO
     * @param knowledge 知识点实体
     * @param includeTopicCount 是否包含题目数量统计
     */
    public KnowledgeDTO convertToDTO(Knowledge knowledge, boolean includeTopicCount) {
        if (knowledge == null) {
            return null;
        }

        KnowledgeDTO dto = new KnowledgeDTO();
        BeanUtils.copyProperties(knowledge, dto);
        if (knowledge.getCreatedAt() != null) {
            dto.setCreatedAt(knowledge.getCreatedAt().toString());
        }

        // 获取题目数量统计
        if (includeTopicCount) {
            try {
                Integer topicCount = knowledgeMapper.selectTopicCountByKnowledgeId(knowledge.getKnowledgeId());
                dto.setTopicCount(topicCount != null ? topicCount : 0);
            } catch (Exception e) {
                log.warn("获取知识点 {} 的题目数量失败: {}", knowledge.getKnowledgeId(), e.getMessage());
                dto.setTopicCount(0);
            }
        } else {
            dto.setTopicCount(0);
        }

        return dto;
    }

    /**
     * 批量将Knowledge转换为KnowledgeDTO
     */
    public List<KnowledgeDTO> convertToDTOList(List<Knowledge> knowledgeList) {
        return convertToDTOList(knowledgeList, true);
    }

    /**
     * 批量将Knowledge转换为KnowledgeDTO
     * @param knowledgeList 知识点列表
     * @param includeTopicCount 是否包含题目数量统计
     */
    public List<KnowledgeDTO> convertToDTOList(List<Knowledge> knowledgeList, boolean includeTopicCount) {
        if (knowledgeList == null) {
            return new ArrayList<>();
        }

        return knowledgeList.stream()
                .map(knowledge -> convertToDTO(knowledge, includeTopicCount))
                .collect(Collectors.toList());
    }

    /**
     * 添加知识点
     */
    public KnowledgeDTO addKnowledgePoint(KnowledgeDTO knowledgeDTO) {
        // Implementation needed
        return null;
    }

    /**
     * 更新知识点
     */
    public KnowledgeDTO updateKnowledgePoint(Integer id, KnowledgeDTO knowledgeDTO) {
        // Implementation needed
        return null;
    }

    /**
     * 删除知识点
     */
    public boolean deleteKnowledgePoint(Integer id) {
        // Implementation needed
        return false;
    }

    /**
     * 获取所有知识点（支持搜索和分页）
     */
    public List<Map<String, Object>> getAllKnowledgePoints(String search, Integer page, Integer limit) {
        // Implementation needed
        return null;
    }

    /**
     * 获取常用知识点（按题目数量排序）
     */
    public List<Map<String, Object>> getPopularKnowledgePoints(Integer limit) {
        // Implementation needed
        return null;
    }

    /**
     * 获取最近使用的知识点
     */
    public List<Map<String, Object>> getRecentKnowledgePoints(Integer limit) {
        // Implementation needed
        return null;
    }

    /**
     * 获取所有知识点分类（Map形式）
     */
    public List<Map<String, Object>> getAllKnowledgeGroups() {
        // Implementation needed
        return null;
    }

    /**
     * 根据知识点分组名称获取知识点
     */
    public List<Map<String, Object>> getKnowledgePointsByGroupName(String groupName) {
        // Implementation needed
        return null;
    }

    /**
     * 根据分类ID、搜索条件和分页参数获取知识点
     */
    public List<Map<String, Object>> getKnowledgePointsByGroup(Integer groupId, String search, Integer page, Integer limit) {
        // Implementation needed
        return null;
    }

    /**
     * 根据ID获取知识点的know_id
     * 用于解决前端传递id而不是know_id的问题
     */
    public Integer getKnowIdById(Integer id) {
        try {
            Knowledge knowledge = getById(id);
            if (knowledge != null) {
                return knowledge.getKnowledgeId();
            }
            return null;
        } catch (Exception e) {
            log.error("获取知识点know_id失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取知识点的题型统计
     */
    public Map<String, Integer> getKnowledgePointTypeStats(Integer knowledgeId) {
        try {
            Map<String, Integer> stats = knowledgeMapper.selectKnowledgePointTypeStats(knowledgeId);

            // 确保所有题型都有值，即使是0
            if (stats == null) {
                stats = new java.util.HashMap<>();
            }

            // 设置默认值
            stats.putIfAbsent("singleChoice", 0);
            stats.putIfAbsent("multipleChoice", 0);
            stats.putIfAbsent("judgment", 0);
            stats.putIfAbsent("fillBlank", 0);
            stats.putIfAbsent("shortAnswer", 0);
            stats.putIfAbsent("subjective", 0);
            stats.putIfAbsent("groupQuestion", 0);

            log.info("知识点 {} 的题型统计: {}", knowledgeId, stats);
            return stats;
        } catch (Exception e) {
            log.error("获取知识点题型统计失败: knowledgeId={}", knowledgeId, e);
            // 返回默认的空统计
            Map<String, Integer> defaultStats = new java.util.HashMap<>();
            defaultStats.put("singleChoice", 0);
            defaultStats.put("multipleChoice", 0);
            defaultStats.put("judgment", 0);
            defaultStats.put("fillBlank", 0);
            defaultStats.put("shortAnswer", 0);
            defaultStats.put("subjective", 0);
            defaultStats.put("groupQuestion", 0);
            return defaultStats;
        }
    }
}