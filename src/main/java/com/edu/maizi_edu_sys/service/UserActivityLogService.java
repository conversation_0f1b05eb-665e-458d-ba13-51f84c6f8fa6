package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.UserActivityLog;
import com.edu.maizi_edu_sys.repository.UserActivityLogRepository;
import com.edu.maizi_edu_sys.util.UserContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户行为日志服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserActivityLogService {
    
    private final UserActivityLogRepository activityLogRepository;
    
    /**
     * 记录用户活动日志（异步）
     */
    @Async
    public void logActivity(Long userId, String username, String activityType, String description, String module) {
        logActivity(userId, username, activityType, description, module, null, null, null);
    }
    
    /**
     * 记录用户活动日志（异步，带目标对象）
     */
    @Async
    public void logActivity(Long userId, String username, String activityType, String description, 
                           String module, String targetId, String targetType, String extraData) {
        try {
            // 如果没有传入用户信息，尝试从上下文获取
            Long actualUserId = userId != null ? userId : UserContextUtil.getCurrentUserId();
            String actualUsername = username != null ? username : UserContextUtil.getCurrentUsername();

            UserActivityLog activityLog = new UserActivityLog();
            activityLog.setUserId(actualUserId);
            activityLog.setUsername(actualUsername);
            activityLog.setActivityType(activityType);
            activityLog.setDescription(description);
            activityLog.setModule(module);
            activityLog.setTargetId(targetId);
            activityLog.setTargetType(targetType);
            activityLog.setExtraData(extraData);
            activityLog.setResult(UserActivityLog.Result.SUCCESS);
            activityLog.setCreateTime(LocalDateTime.now());

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                activityLog.setIpAddress(getClientIpAddress(request));
                activityLog.setUserAgent(request.getHeader("User-Agent"));
                activityLog.setRequestPath(request.getRequestURI());
                activityLog.setRequestMethod(request.getMethod());
            } else {
                // 如果没有请求上下文，使用工具类获取
                activityLog.setIpAddress(UserContextUtil.getClientIpAddress());
                activityLog.setUserAgent(UserContextUtil.getUserAgent());
            }

            activityLogRepository.insert(activityLog);
            log.info("记录用户活动日志：用户={}, 活动={}, 描述={}", actualUsername, activityType, description);

        } catch (Exception e) {
            log.error("记录用户活动日志失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 记录失败的活动日志
     */
    @Async
    public void logFailedActivity(Long userId, String username, String activityType, String description,
                                 String module, String errorMessage) {
        try {
            // 如果没有传入用户信息，尝试从上下文获取
            Long actualUserId = userId != null ? userId : UserContextUtil.getCurrentUserId();
            String actualUsername = username != null ? username : UserContextUtil.getCurrentUsername();

            UserActivityLog activityLog = new UserActivityLog();
            activityLog.setUserId(actualUserId);
            activityLog.setUsername(actualUsername);
            activityLog.setActivityType(activityType);
            activityLog.setDescription(description);
            activityLog.setModule(module);
            activityLog.setResult(UserActivityLog.Result.FAILED);
            activityLog.setErrorMessage(errorMessage);
            activityLog.setCreateTime(LocalDateTime.now());

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                activityLog.setIpAddress(getClientIpAddress(request));
                activityLog.setUserAgent(request.getHeader("User-Agent"));
                activityLog.setRequestPath(request.getRequestURI());
                activityLog.setRequestMethod(request.getMethod());
            } else {
                // 如果没有请求上下文，使用工具类获取
                activityLog.setIpAddress(UserContextUtil.getClientIpAddress());
                activityLog.setUserAgent(UserContextUtil.getUserAgent());
            }

            activityLogRepository.insert(activityLog);
            log.warn("记录失败活动日志：用户={}, 活动={}, 错误={}", actualUsername, activityType, errorMessage);

        } catch (Exception e) {
            log.error("记录失败活动日志失败：{}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取最近的活动日志
     */
    public List<UserActivityLog> getRecentActivities(int limit) {
        return activityLogRepository.findRecentActivities(limit);
    }

    /**
     * 分页获取活动日志
     */
    public List<UserActivityLog> getActivitiesWithPagination(int page, int size) {
        int offset = page * size;
        return activityLogRepository.findActivitiesWithPagination(size, offset);
    }

    /**
     * 获取活动日志总数
     */
    public long getTotalActivitiesCount() {
        return activityLogRepository.countAllActivities();
    }
    
    /**
     * 获取用户的活动日志
     */
    public List<UserActivityLog> getUserActivities(Long userId, int limit) {
        return activityLogRepository.findByUserId(userId, limit);
    }
    
    /**
     * 获取活动统计数据
     */
    public List<Map<String, Object>> getActivityStatistics(LocalDateTime startTime) {
        return activityLogRepository.getActivityStatistics(startTime);
    }
    
    /**
     * 获取用户活动统计
     */
    public List<Map<String, Object>> getUserActivityStatistics(LocalDateTime startTime, int limit) {
        return activityLogRepository.getUserActivityStatistics(startTime, limit);
    }
    
    /**
     * 获取模块使用统计
     */
    public List<Map<String, Object>> getModuleStatistics(LocalDateTime startTime) {
        return activityLogRepository.getModuleStatistics(startTime);
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 清理过期日志（保留最近30天）
     */
    public void cleanupExpiredLogs() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(30);
            int deletedCount = activityLogRepository.deleteExpiredLogs(expireTime);
            log.info("清理过期活动日志：删除了{}条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理过期活动日志失败：{}", e.getMessage(), e);
        }
    }
}
