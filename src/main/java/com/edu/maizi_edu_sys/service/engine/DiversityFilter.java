package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目多样性过滤器组件。
 * 主要职责是确保选出的题目在内容、考点或其他维度上具有足够的多样性，
 * 避免试卷中出现过多相似或重复模式的题目。
 * 目前的实现主要基于重用间隔过滤，未来可以扩展更多样性策略。
 */
@Component
@Slf4j
public class DiversityFilter {

    // 知识点级别多样性控制配置参数
    @Value("${algorithm.diversity.knowledge-point-level.enabled:true}")
    private boolean knowledgePointLevelEnabled;

    @Value("${algorithm.diversity.knowledge-point-level.min-reuse-interval-days:1}")
    private int knowledgePointMinReuseIntervalDays;

    @Value("${algorithm.diversity.knowledge-point-level.max-topics-per-knowledge-point:50}")
    private int maxTopicsPerKnowledgePoint;

    @Value("${algorithm.diversity.knowledge-point-level.priority-weight-usage:10.0}")
    private double priorityWeightUsage;

    @Value("${algorithm.diversity.knowledge-point-level.priority-weight-time:1.0}")
    private double priorityWeightTime;

    /**
     * 根据多样性标准（当前主要为题目重用间隔）过滤候选题目列表。
     *
     * <p>此方法的核心逻辑是：</p>
     * <ul>
     *   <li>如果设置了最小重用间隔 ({@code minReuseIntervalDays > 0})，则会构建一个时间阈值。</li>
     *   <li>遍历输入的题目列表 ({@code topics})。</li>
     *   <li>对于每道题目，查询其在 {@code enhancementDataMap} 中的增强数据（特别是 {@code lastUsedTime}）。</li>
     *   <li>如果题目的 {@code lastUsedTime} 为空，或者早于计算出的重用时间阈值，则该题目被视为符合重用策略，得以保留。</li>
     *   <li>如果未设置重用间隔，则所有题目均通过此过滤器。</li>
     * </ul>
     *
     * <p><strong>注意:</strong> 当前版本的"多样性"主要体现在时间上的重用避免。
     * 更高级的多样性可能需要考虑题目内容相似度、考点覆盖的细粒度均衡性、
     * 题目来源、甚至是题目难度微观分布的均匀性等，这些可以在未来版本中扩展。
     * 例如，可以引入基于题目文本embedding的相似度计算，或者基于题目元数据（如标签、子知识点）的分布统计。
     * </p>
     *
     * @param topics 待过滤的候选题目列表。
     * @param enhancementDataMap Map，键为题目ID，值为题目的 {@link TopicEnhancementData} 对象，包含上次使用时间等信息。
     * @param minReuseIntervalDays 最小重用间隔天数。如果为 null 或小于等于0，则不执行基于重用间隔的过滤。
     * @return 经过重用间隔过滤后的题目列表。如果输入题目列表为空，则返回空列表。
     */
    public List<Topic> filter(List<Topic> topics,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             Integer minReuseIntervalDays) {

        if (topics == null || topics.isEmpty()) {
            log.warn("DiversityFilter: Input topics list is null or empty, returning empty list.");
            return Collections.emptyList();
        }

        // 计算重用时间阈值
        // 如果 minReuseIntervalDays 有效（大于0），则计算出允许题目被再次使用的最早日期
        final LocalDateTime reuseThresholdDate = (minReuseIntervalDays != null && minReuseIntervalDays > 0) ?
                LocalDateTime.now().minusDays(minReuseIntervalDays) : null;

        if (reuseThresholdDate != null) {
            log.info("DiversityFilter: Applying reuse interval filter. Topics last used before {} will be kept. Min reuse interval: {} days.",
                     reuseThresholdDate, minReuseIntervalDays);
        } else {
            log.info("DiversityFilter: No reuse interval specified or invalid (<=0). Skipping reuse filtering.");
            return topics; // 没有有效的重用间隔，直接返回原始列表
        }

        @SuppressWarnings("null")
        List<Topic> filteredTopics = topics.stream()
            .filter(topic -> {
                // 获取该题目的增强数据
                TopicEnhancementData ed = enhancementDataMap.get(topic.getId());
                // 筛选逻辑：
                // 1. 如果题目没有增强数据 (ed == null)，或者
                // 2. 增强数据中没有上次使用时间 (ed.getLastUsedTime() == null)，或者
                // 3. 上次使用时间早于重用阈值 (ed.getLastUsedTime().isBefore(reuseThresholdDate))
                // 则该题目符合重用策略，予以保留。
                boolean canBeReused = ed == null || ed.getLastUsedTime() == null ||
                       ed.getLastUsedTime().isBefore(reuseThresholdDate);
                if (!canBeReused) {
                    log.trace("DiversityFilter: Topic ID {} (last used: {}) filtered out due to reuse interval.", topic.getId(), ed.getLastUsedTime());
                }
                return canBeReused;
            })
            .collect(Collectors.toList());
        log.info("DiversityFilter: {} topics remaining after reuse interval filtering (out of {} initial topics).", filteredTopics.size(), topics.size());
        return filteredTopics;
    }

    /**
     * 按知识点分组进行多样性过滤，确保每个知识点内部的题目也满足重用间隔要求。
     *
     * <p>此方法在全局重用间隔过滤的基础上，进一步在知识点级别应用多样性控制：</p>
     * <ul>
     *   <li>将题目按知识点分组</li>
     *   <li>在每个知识点内部应用重用间隔过滤</li>
     *   <li>支持知识点级别的题目数量限制</li>
     *   <li>确保知识点内部题目的多样性</li>
     * </ul>
     *
     * @param topics 待过滤的候选题目列表
     * @param enhancementDataMap 题目增强数据映射
     * @param minReuseIntervalDays 最小重用间隔天数
     * @param maxTopicsPerKnowledgePoint 每个知识点最大题目数量（可选，null表示不限制）
     * @return 经过知识点级别多样性过滤后的题目列表
     */
    public List<Topic> filterByKnowledgePoint(List<Topic> topics,
                                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                                             Integer minReuseIntervalDays,
                                             Integer maxTopicsPerKnowledgePoint) {

        if (topics == null || topics.isEmpty()) {
            log.warn("DiversityFilter: Input topics list is null or empty for knowledge point filtering.");
            return Collections.emptyList();
        }

        log.info("DiversityFilter: Starting knowledge point level diversity filtering for {} topics. " +
                "MinReuseInterval: {} days, MaxTopicsPerKP: {}",
                topics.size(), minReuseIntervalDays, maxTopicsPerKnowledgePoint);

        // 按知识点分组 (User guarantees all topics have knowId)
        Map<Integer, List<Topic>> topicsByKnowledgePoint = topics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        log.info("DiversityFilter: Topics grouped into {} knowledge points", topicsByKnowledgePoint.size());

        List<Topic> filteredTopics = new ArrayList<>();

        // 对每个知识点内的题目进行多样性过滤
        for (Map.Entry<Integer, List<Topic>> entry : topicsByKnowledgePoint.entrySet()) {
            Integer knowledgePointId = entry.getKey();
            List<Topic> kpTopics = entry.getValue();

            log.debug("DiversityFilter: Processing knowledge point {} with {} initial topics",
                     knowledgePointId, kpTopics.size());

            // 在知识点内部应用重用间隔过滤
            List<Topic> kpFilteredByReuse = filter(kpTopics, enhancementDataMap, minReuseIntervalDays);
            int countAfterReuseFilter = kpFilteredByReuse.size();

            List<Topic> currentKpSelection = kpFilteredByReuse;

            // 如果设置了每个知识点的最大题目数量限制
            if (maxTopicsPerKnowledgePoint != null && maxTopicsPerKnowledgePoint > 0
                && countAfterReuseFilter > maxTopicsPerKnowledgePoint) {
                
                log.debug("DiversityFilter: Knowledge point {} has {} topics after reuse filtering, exceeding maxTopicsPerKnowledgePoint of {}. Applying prioritization.",
                         knowledgePointId, countAfterReuseFilter, maxTopicsPerKnowledgePoint);

                // 按使用频率和时间排序，优先选择使用较少且较久未用的题目
                currentKpSelection = prioritizeTopicsByUsageAndTime(currentKpSelection, enhancementDataMap);
                currentKpSelection = currentKpSelection.subList(0, maxTopicsPerKnowledgePoint);

                log.debug("DiversityFilter: Limited knowledge point {} to {} topics (selected from {} after reuse filtering by priority).",
                         knowledgePointId, currentKpSelection.size(), countAfterReuseFilter);
            } else {
                log.debug("DiversityFilter: Knowledge point {} has {} topics after reuse filtering. MaxTopicsPerKnowledgePoint ({}) not exceeded or not applicable.",
                         knowledgePointId, countAfterReuseFilter, maxTopicsPerKnowledgePoint);
            }

            filteredTopics.addAll(currentKpSelection);

            log.debug("DiversityFilter: Knowledge point {} contributed {} topics after all filtering for this KP.",
                     knowledgePointId, currentKpSelection.size());
        }

        log.info("DiversityFilter: Knowledge point level filtering completed. {} topics remaining (from {} initial).",
                filteredTopics.size(), topics.size());

        return filteredTopics;
    }

    /**
     * 按使用频率和时间优先级排序题目。
     * 优先选择使用次数少且最后使用时间较早的题目，以提高题目多样性。
     *
     * @param topics 待排序的题目列表
     * @param enhancementDataMap 题目增强数据映射
     * @return 按优先级排序后的题目列表
     */
    private List<Topic> prioritizeTopicsByUsageAndTime(List<Topic> topics,
                                                      Map<Integer, TopicEnhancementData> enhancementDataMap) {

        return topics.stream()
            .sorted((t1, t2) -> {
                TopicEnhancementData ed1 = enhancementDataMap.get(t1.getId());
                TopicEnhancementData ed2 = enhancementDataMap.get(t2.getId());

                // 计算题目1的优先级分数（分数越低优先级越高）
                double score1 = calculateTopicPriorityScore(ed1);
                // 计算题目2的优先级分数
                double score2 = calculateTopicPriorityScore(ed2);

                return Double.compare(score1, score2);
            })
            .collect(Collectors.toList());
    }

    /**
     * 计算题目的优先级分数。
     * 分数越低表示优先级越高（应该优先选择）。
     *
     * @param enhancementData 题目增强数据
     * @return 优先级分数
     */
    private double calculateTopicPriorityScore(TopicEnhancementData enhancementData) {
    if (enhancementData == null) {
        // Topics with no enhancement data (e.g., new or untracked) get the highest priority.
        return 0.0; 
    }

    double score = 0.0; // Lower score means higher priority

    // Component 1: Penalty based on Usage Count
    Integer usageCount = enhancementData.getUsageCount();
    if (usageCount != null && usageCount > 0) {
        // More usage -> higher score -> lower priority.
        score += usageCount * priorityWeightUsage; // Uses configured weight
    }

    // Component 2: Penalty based on Last Used Time relative to knowledge point reuse interval
    LocalDateTime lastUsedTime = enhancementData.getLastUsedTime();
    if (lastUsedTime != null) {
        long daysSinceLastUse = java.time.temporal.ChronoUnit.DAYS.between(lastUsedTime, LocalDateTime.now());
        // This penalty is based on knowledgePointMinReuseIntervalDays.
        // If this scoring method is ever used to prioritize topics in a context where globalMinReuseIntervalDays
        // was the primary filter (i.e., knowledgePointLevelEnabled=false), and these two intervals differ significantly,
        // this specific penalty term might need adjustment or context (e.g., passing the relevant interval).
        // For now, it consistently uses the knowledge point interval as a heuristic for recency penalty.
        // Penalty applies if used more recently than knowledgePointMinReuseIntervalDays.
        score += Math.max(0, knowledgePointMinReuseIntervalDays - daysSinceLastUse) * priorityWeightTime;
    }

    // Topics never used (usageCount is null/0, lastUsedTime is null) will correctly get a score of 0.0 if enhancementData is not null.
    return score;
}

    /**
     * 综合过滤方法，同时应用全局和知识点级别的多样性控制。
     *
     * @param topics 待过滤的候选题目列表
     * @param enhancementDataMap 题目增强数据映射
     * @param minReuseIntervalDays 最小重用间隔天数
     * @param maxTopicsPerKnowledgePoint 每个知识点最大题目数量
     * @return 经过综合多样性过滤后的题目列表
     */
    public List<Topic> filterWithKnowledgePointDiversity(List<Topic> topics,
                                                        Map<Integer, TopicEnhancementData> enhancementDataMap,
                                                        Integer minReuseIntervalDays,
                                                        Integer maxTopicsPerKnowledgePoint) {

        log.info("DiversityFilter: Starting comprehensive diversity filtering with KP-level control");

        // 首先应用全局重用间隔过滤
        List<Topic> globalFiltered = filter(topics, enhancementDataMap, minReuseIntervalDays);

        // 然后应用知识点级别的多样性控制
        return filterByKnowledgePoint(globalFiltered, enhancementDataMap, minReuseIntervalDays, maxTopicsPerKnowledgePoint);
    }

    /**
     * 智能多样性过滤方法，根据配置自动决定是否启用知识点级别的多样性控制。
     * 这是推荐使用的主要过滤方法。
     *
     * @param topics 待过滤的候选题目列表
     * @param enhancementDataMap 题目增强数据映射
     * @param globalMinReuseIntervalDays 全局最小重用间隔天数
     * @return 经过智能多样性过滤后的题目列表
     */
    public List<Topic> smartFilter(List<Topic> topics,
                                  Map<Integer, TopicEnhancementData> enhancementDataMap,
                                  Integer globalMinReuseIntervalDays) {

        log.info("DiversityFilter: Starting smart diversity filtering. KP-level enabled: {}", knowledgePointLevelEnabled);

        if (!knowledgePointLevelEnabled) {
            // 如果未启用知识点级别控制，只使用全局过滤
            log.info("DiversityFilter: Knowledge point level filtering disabled, using global filtering only");
            return filter(topics, enhancementDataMap, globalMinReuseIntervalDays);
        }

        // 启用知识点级别控制
        log.info("DiversityFilter: Knowledge point level filtering enabled. " +
                "KP min reuse interval: {} days, Max topics per KP: {}",
                knowledgePointMinReuseIntervalDays, maxTopicsPerKnowledgePoint);

        // 首先应用全局重用间隔过滤
        List<Topic> globalFiltered = filter(topics, enhancementDataMap, globalMinReuseIntervalDays);

        // 然后应用知识点级别的多样性控制，使用配置的参数
        return filterByKnowledgePoint(globalFiltered, enhancementDataMap,
                                    knowledgePointMinReuseIntervalDays, maxTopicsPerKnowledgePoint);
    }

    /**
     * 题型感知的智能过滤方法，确保每种需要的题型都能保留足够的题目。
     *
     * @param topics 待过滤的候选题目列表
     * @param enhancementDataMap 题目增强数据映射
     * @param globalMinReuseIntervalDays 全局最小重用间隔天数
     * @param requiredTypeCounts 需要的题型数量映射（数据库格式，如 "short" -> 2）
     * @return 经过题型感知过滤后的题目列表
     */
    public List<Topic> smartFilterWithTypeAwareness(List<Topic> topics,
                                                   Map<Integer, TopicEnhancementData> enhancementDataMap,
                                                   Integer globalMinReuseIntervalDays,
                                                   Map<String, Integer> requiredTypeCounts) {

        if (topics == null || topics.isEmpty()) {
            log.warn("DiversityFilter: Input topics list for type-aware filtering is null or empty, returning empty list.");
            return Collections.emptyList();
        }
        // It's important to check requiredTypeCounts emptiness later, but primary logging for input topics should happen first.

        log.info("DiversityFilter: Starting type-aware smart filtering. Initial total topics: {}. Required type counts from request: {}",
                 topics.size(), requiredTypeCounts);

        // 按题型分组 (ensure topics with null type are handled gracefully)
        Map<String, List<Topic>> topicsByType = topics.stream()
                .filter(t -> t.getType() != null) // Add a null check for robustness
                .collect(Collectors.groupingBy(Topic::getType));

        // Log initial counts per type based on actual input topics
        log.info("DiversityFilter: Initial topic distribution by type (from input list):");
        topicsByType.forEach((typeKey, typeTopicsList) -> // Renamed 'type' to 'typeKey' to avoid conflict with loop var later
            log.info("  - Type '{}': {} topics", typeKey, typeTopicsList.size())
        );
        // Also log if any types present in the input topics are not in requiredTypeCounts (informational)
        topicsByType.keySet().forEach(presentType -> {
            if (requiredTypeCounts == null || !requiredTypeCounts.containsKey(presentType)) {
                log.info("  - Type '{}': {} topics (present in input, but not in requiredTypeCounts or map is null)", presentType, topicsByType.get(presentType).size());
            }
        });
        // And ensure all *required* types are logged, even if no topics exist for them in the input
        if (requiredTypeCounts != null) {
            requiredTypeCounts.keySet().forEach(requiredType -> {
                if (!topicsByType.containsKey(requiredType)) {
                    log.info("  - Type '{}': 0 topics in input (but {} required)", requiredType, requiredTypeCounts.get(requiredType));
                }
            });
        } else {
            log.warn("DiversityFilter: requiredTypeCounts is null, cannot report on required types not present in input.");
        }

        // Now, handle the case where requiredTypeCounts itself is null or empty, after initial logging.
        if (requiredTypeCounts == null || requiredTypeCounts.isEmpty()) {
            log.warn("DiversityFilter: Required type counts map is null or empty. Performing standard smart filtering without specific type count enforcement.");
            return smartFilter(topics, enhancementDataMap, globalMinReuseIntervalDays); // Fallback
        }

        List<Topic> result = new ArrayList<>();

        // 对每种需要的题型单独处理
        for (Map.Entry<String, Integer> entry : requiredTypeCounts.entrySet()) {
            String type = entry.getKey(); // This 'type' is the loop variable for required types
            Integer requiredCount = entry.getValue();

            if (requiredCount == null || requiredCount <= 0) {
                log.debug("DiversityFilter: Skipping type '{}' as required count is {} or less.", type, requiredCount);
                continue;
            }

            List<Topic> typeTopics = topicsByType.getOrDefault(type, Collections.emptyList());
            log.info("DiversityFilter: Processing type '{}' - required: {}, available in input: {}",
                    type, requiredCount, typeTopics.size());

            if (typeTopics.isEmpty()) {
                log.warn("DiversityFilter: No topics available in input for required type '{}'", type);
                // Depending on policy, might add a placeholder or throw an error if strict adherence is needed
                continue;
            }

            // 对该题型的题目应用多样性过滤，但确保保留足够数量
            List<Topic> filteredTypeTopics = smartFilterForType(typeTopics, enhancementDataMap,
                                                               globalMinReuseIntervalDays, requiredCount);

            result.addAll(filteredTypeTopics);
            log.info("DiversityFilter: Type '{}' contributed {} topics after smartFilterForType. Total in result now: {}",
                    type, filteredTypeTopics.size(), result.size());
        }

        log.info("DiversityFilter: Type-aware filtering completed. Final total topics selected: {}. Initial total topics was: {}",
                result.size(), topics.size());

        return result;
    }

    /**
     * 为特定题型应用智能过滤，确保保留足够数量的题目。
     */
    private List<Topic> smartFilterForType(List<Topic> typeTopics,
                                      Map<Integer, TopicEnhancementData> enhancementDataMap,
                                      Integer globalMinReuseIntervalDays,
                                      int requiredCount) {

    // 计算需要保留的最小数量（考虑一定的冗余）
    int minToKeep = Math.max(requiredCount * 2, 10); // 至少保留需求量的2倍或10个

    if (typeTopics.size() <= minToKeep) {
        // 如果题目数量不多，不足以进行有效的多样性过滤并保证后续选择池大小，则保留所有题目。
        // 这意味着对于此题型，多样性规则（如重用间隔）可能未被严格执行。
        log.debug("DiversityFilter: Type has {} topics (required: {}, minToKeep: {}). " +
                  "Skipping diversity filtering for this type as topic count ({}) is not significantly larger than minToKeep ({}). " +
                  "All these topics will be candidates.",
                 typeTopics.size(), requiredCount, minToKeep, typeTopics.size(), minToKeep);
        return typeTopics;
    }

    // 应用标准的多样性过滤
    List<Topic> filtered;
    if (knowledgePointLevelEnabled) {
        filtered = filterByKnowledgePoint(typeTopics, enhancementDataMap,
                                        knowledgePointMinReuseIntervalDays, maxTopicsPerKnowledgePoint);
    } else {
        filtered = filter(typeTopics, enhancementDataMap, globalMinReuseIntervalDays);
    }

    // 如果过滤后的数量不足，补充一些题目
    if (filtered.size() < minToKeep) {
        log.warn("DiversityFilter: Type filtering initially resulted in {} topics, but minToKeep is {}. Attempting to add more topics.",
                filtered.size(), minToKeep);

        // 从原始列表中补充题目（按优先级排序）
        List<Topic> remaining = new ArrayList<>(typeTopics);
        remaining.removeAll(filtered); // Get topics that were filtered out by diversity rules

        if (!remaining.isEmpty()) {
            // 按优先级排序剩余题目 (less used, older usage preferred)
            remaining = prioritizeTopicsByUsageAndTime(remaining, enhancementDataMap);

            int needed = minToKeep - filtered.size();
            int canAdd = Math.min(needed, remaining.size());
            
            if (canAdd > 0) {
                filtered.addAll(remaining.subList(0, canAdd));
                log.info("DiversityFilter: Added {} additional topics (from {} available in remainder) for type to reach {} (minToKeep).", 
                         canAdd, remaining.size(), filtered.size());
            } else {
                log.warn("DiversityFilter: No more topics available to supplement for this type to reach minToKeep of {}. Current count: {}", 
                         minToKeep, filtered.size());
            }
        } else {
            log.warn("DiversityFilter: Filtered list size is {} (less than minToKeep {}), but no remaining topics to supplement from.", 
                     filtered.size(), minToKeep);
        }
    }

    return filtered;
}
}