package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.extern.slf4j.Slf4j;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import java.util.ArrayList;
import java.util.BitSet;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 可行解检测器
 * 负责检测染色体是否满足所有硬性约束，用于提前终止判断
 */
@Component
@Slf4j
public class FeasibilityChecker {

    @Value("${algorithm.genetic.repair.greedy-threshold:0.1}")
    private double scoreToleranceRatio;

    /**
     * 检查染色体是否为可行解
     *
     * @param chromosome 待检查的染色体
     * @param availableQuestions 可用题目列表
     * @param typeTargetCounts 目标题型数量
     * @param targetScore 目标分数
     * @param typeScores 题型分数映射
     * @param knowledgePointConfigs 知识点配置（用于检查简答题要求）
     * @return 可行性检查结果
     */
    public FeasibilityResult checkFeasibility(GeneticSolver.Chromosome chromosome,
                                            List<Topic> availableQuestions,
                                            Map<String, Integer> typeTargetCounts,
                                            int targetScore,
                                            Map<String, Integer> typeScores,
                                            List<KnowledgePointConfig> knowledgePointConfigs) {
        log.debug("Checking feasibility for chromosome. TargetScore: {}, TargetTypeCounts: {}, KPConfigs: {}", targetScore, typeTargetCounts, knowledgePointConfigs);

        if (chromosome == null || availableQuestions == null) {
            return new FeasibilityResult(false, "Invalid input parameters");
        }

        // 1. 检查题型数量约束
        FeasibilityResult typeResult = checkTypeConstraints(chromosome, availableQuestions, typeTargetCounts);
        if (!typeResult.isFeasible()) {
            log.debug("Feasibility check failed at TypeConstraints: {}", typeResult.getMessage());
            return typeResult;
        }

        // 2. 检查分数约束
        FeasibilityResult scoreResult = checkScoreConstraints(chromosome, availableQuestions, targetScore, typeScores);
        if (!scoreResult.isFeasible()) {
            log.debug("Feasibility check failed at ScoreConstraints: {}", scoreResult.getMessage());
            return scoreResult;
        }

        // 3. 检查知识点简答题约束
        FeasibilityResult kpResult = checkKnowledgePointConstraints(chromosome, availableQuestions, knowledgePointConfigs);
        if (!kpResult.isFeasible()) {
            log.debug("Feasibility check failed at KnowledgePointConstraints: {}", kpResult.getMessage());
            return kpResult;
        }

        return new FeasibilityResult(true, "All constraints satisfied");
    }

    /**
     * 检查题型数量约束
     */
    private FeasibilityResult checkTypeConstraints(GeneticSolver.Chromosome chromosome,
                                                 List<Topic> availableQuestions,
                                                 Map<String, Integer> typeTargetCounts) {
        log.debug("Checking TypeConstraints. TargetCounts: {}", typeTargetCounts);
        if (typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            return new FeasibilityResult(true, "No type constraints");
        }

        // 统计当前选中题目的题型分布
        Map<String, Long> currentTypeCounts = getSelectedTopics(chromosome, availableQuestions)
            .stream()
            .collect(Collectors.groupingBy(
                topic -> TopicTypeMapper.toDbFormat(topic.getType()),
                Collectors.counting()
            ));

        // 检查每种题型是否满足要求
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int required = entry.getValue();
            long actual = currentTypeCounts.getOrDefault(type, 0L);

            if (actual != required) {
                String message = String.format("Type constraint violation: %s required=%d, actual=%d", type, required, actual);
                log.debug(message);
                return new FeasibilityResult(false,
                    String.format("Type constraint violation: %s required=%d, actual=%d", type, required, actual));
            }
        }

        return new FeasibilityResult(true, "Type constraints satisfied");
    }

    /**
     * 检查分数约束
     */
    private FeasibilityResult checkScoreConstraints(GeneticSolver.Chromosome chromosome,
                                                  List<Topic> availableQuestions,
                                                  int targetScore,
                                                  Map<String, Integer> typeScores) {
        log.debug("Checking ScoreConstraints. TargetScore: {}, ToleranceRatio: {}", targetScore, scoreToleranceRatio);
        if (targetScore <= 0) {
            return new FeasibilityResult(true, "No score constraints");
        }

        List<Topic> selectedTopics = getSelectedTopics(chromosome, availableQuestions);

        // 计算当前总分
        int currentScore = 0;
        for (Topic topic : selectedTopics) {
            currentScore += getTopicScore(topic, typeScores);
        }

        // 检查分数是否在容忍范围内
        double tolerance = targetScore * scoreToleranceRatio;

        if (Math.abs(currentScore - targetScore) > tolerance) {
            String message = String.format("Score constraint violation: currentScore=%d, targetScore=%d, tolerance=%.2f", currentScore, targetScore, tolerance);
            log.debug(message);
            return new FeasibilityResult(false,
                String.format("Score constraint violation: target=%d, actual=%d, tolerance=%.1f",
                             targetScore, currentScore, tolerance));
        }

        return new FeasibilityResult(true, "Score constraints satisfied");
    }

    /**
     * 检查知识点简答题约束
     */
    private FeasibilityResult checkKnowledgePointConstraints(GeneticSolver.Chromosome chromosome,
                                                           List<Topic> availableQuestions,
                                                           List<KnowledgePointConfig> knowledgePointConfigs) {
        log.debug("Checking KnowledgePointConstraints. Configs: {}", knowledgePointConfigs);
        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return new FeasibilityResult(true, "No knowledge point constraints");
        }

        List<Topic> selectedTopics = getSelectedTopics(chromosome, availableQuestions);

        // 按知识点分组选中的题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = selectedTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        // 检查每个知识点的简答题要求
        for (KnowledgePointConfig config : knowledgePointConfigs) {
            if (config.isIncludeShortAnswer()) {
                List<Topic> kpTopics = topicsByKnowledgePoint.getOrDefault(config.getKnowledgeId(), Collections.emptyList());

                // 检查是否包含简答题
                boolean hasShortAnswer = kpTopics.stream()
                    .anyMatch(topic -> TopicTypeMapper.DB_SHORT_ANSWER.equals(TopicTypeMapper.toDbFormat(topic.getType())));

                if (!hasShortAnswer) {
                    String message = String.format("Knowledge point %d requires short answer questions but none found", config.getKnowledgeId());
                    log.debug("KP Constraint violation for KP ID {}: Short answer required but not found.", config.getKnowledgeId());
                    return new FeasibilityResult(false, message);
                }
            }
        }

        return new FeasibilityResult(true, "Knowledge point constraints satisfied");
    }

    /**
     * 获取染色体选中的题目列表.
     * Note: If chromosome.getGene() directly maps to indices in availableQuestions (e.g., BitSet or boolean[]),
     * iterating from 0 to gene.length() and picking availableQuestions.get(i) would be more efficient
     * than using availableQuestions.indexOf() in a stream filter.
     */
    private List<Topic> getSelectedTopics(GeneticSolver.Chromosome chromosome, List<Topic> availableQuestions) {
        List<Topic> selected = new ArrayList<>();
        BitSet gene = chromosome.getGene();
        for (int idx = gene.nextSetBit(0); idx >= 0 && idx < availableQuestions.size(); idx = gene.nextSetBit(idx + 1)) {
            selected.add(availableQuestions.get(idx));
        }
        return selected;
    }

    /**
     * 获取题目分数
     */
    private int getTopicScore(Topic topic, Map<String, Integer> typeScores) {
        if (typeScores != null) {
            String normalizedType = TopicTypeMapper.toDbFormat(topic.getType());
            Integer typeScore = typeScores.get(normalizedType);
            if (typeScore != null) {
                return typeScore;
            }
        }
        return topic.getScore() != null ? topic.getScore() : 0;
    }



    /**
     * 可行性检查结果
     */
    public static class FeasibilityResult {
        private final boolean feasible;
        private final String message;

        public FeasibilityResult(boolean feasible, String message) {
            this.feasible = feasible;
            this.message = message;
        }

        public boolean isFeasible() {
            return feasible;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("FeasibilityResult{feasible=%s, message='%s'}", feasible, message);
        }
    }

    /**
     * 知识点配置（简化版，用于可行性检查）
     */
    public static class KnowledgePointConfig {
        private final Integer knowledgeId;
        private final boolean includeShortAnswer;

        public KnowledgePointConfig(Integer knowledgeId, boolean includeShortAnswer) {
            this.knowledgeId = knowledgeId;
            this.includeShortAnswer = includeShortAnswer;
        }

        public Integer getKnowledgeId() {
            return knowledgeId;
        }

        public boolean isIncludeShortAnswer() {
            return includeShortAnswer;
        }
    }
}
