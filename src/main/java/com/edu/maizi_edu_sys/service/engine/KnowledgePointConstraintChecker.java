package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识点约束检查器
 * 负责检查和验证知识点级别的题目分配约束
 */
@Component
@Slf4j
public class KnowledgePointConstraintChecker {

    /**
     * 检查选中的题目是否满足知识点级别的约束
     *
     * @param selectedTopics 选中的题目列表
     * @param knowledgePointConfigs 知识点配置列表
     * @return 约束检查结果
     */
    public KnowledgePointConstraintResult checkConstraints(List<Topic> selectedTopics,
                                                          List<KnowledgePointConfigRequest> knowledgePointConfigs) {

        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return new KnowledgePointConstraintResult(false, "没有选中任何题目");
        }

        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return new KnowledgePointConstraintResult(true, "没有知识点约束");
        }

        // 按知识点分组题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = selectedTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        List<String> violations = new ArrayList<>();

        // 检查每个知识点的约束
        for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
            Integer knowledgeId = config.getKnowledgeId().intValue();
            List<Topic> kpTopics = topicsByKnowledgePoint.getOrDefault(knowledgeId, Collections.emptyList());

            // 检查总题目数量约束
            KnowledgePointConstraintResult totalCountResult = checkTotalQuestionCount(config, kpTopics);
            if (!totalCountResult.isValid()) {
                violations.add(String.format("知识点%d: %s", knowledgeId, totalCountResult.getMessage()));
            }

            // 检查简答题约束（如果有配置）
            if (config.hasShortAnswerConfiguration()) {
                KnowledgePointConstraintResult shortAnswerResult = checkShortAnswerConstraints(config, kpTopics);
                if (!shortAnswerResult.isValid()) {
                    violations.add(String.format("知识点%d: %s", knowledgeId, shortAnswerResult.getMessage()));
                }
            }
        }

        if (violations.isEmpty()) {
            return new KnowledgePointConstraintResult(true, "所有知识点约束都满足");
        } else {
            return new KnowledgePointConstraintResult(false, String.join("; ", violations));
        }
    }

    /**
     * 检查总题目数量约束
     */
    private KnowledgePointConstraintResult checkTotalQuestionCount(KnowledgePointConfigRequest config,
                                                                  List<Topic> kpTopics) {

        Integer expectedCount = config.getQuestionCount();
        if (expectedCount == null || expectedCount <= 0) {
            // 如果配置中未指定有效的题目数量，则认为此项约束通过（或不适用）
            return new KnowledgePointConstraintResult(true, "没有明确的总题目数量约束");
        }

        int actualCount = kpTopics.size();

        if (actualCount == expectedCount) {
            return new KnowledgePointConstraintResult(true,
                String.format("总题目数量准确（期望%d，实际%d）", expectedCount, actualCount));
        } else {
            return new KnowledgePointConstraintResult(false,
                String.format("总题目数量不匹配（期望%d，实际%d）", expectedCount, actualCount));
        }
    }

    /**
     * 检查简答题约束
     */
    private KnowledgePointConstraintResult checkShortAnswerConstraints(KnowledgePointConfigRequest config,
                                                                      List<Topic> kpTopics) {

        Integer expectedShortAnswer = config.getShortAnswerCount();
        // 如果配置中未指定有效的简答题数量，则认为此项约束通过（或不适用）
        if (expectedShortAnswer == null || expectedShortAnswer < 0) { 
            return new KnowledgePointConstraintResult(true, "没有明确的简答题数量约束");
        }

        long actualShortAnswer = kpTopics.stream()
            .filter(topic -> TopicTypeMapper.DB_SHORT_ANSWER.equals(TopicTypeMapper.toDbFormat(topic.getType())))
            .count();

        if (actualShortAnswer == expectedShortAnswer) {
            return new KnowledgePointConstraintResult(true,
                String.format("简答题数量准确（期望%d，实际%d）", expectedShortAnswer, actualShortAnswer));
        } else {
            return new KnowledgePointConstraintResult(false,
                String.format("简答题数量不匹配（期望%d，实际%d）", expectedShortAnswer, actualShortAnswer));
        }
    }

    /**
     * 生成知识点约束的修复建议
     */
    public List<KnowledgePointAdjustmentSuggestion> generateAdjustmentSuggestions(
            List<Topic> selectedTopics,
            List<KnowledgePointConfigRequest> knowledgePointConfigs,
            List<Topic> availableTopics) {

        List<KnowledgePointAdjustmentSuggestion> allSuggestions = new ArrayList<>();
        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return allSuggestions;
        }

        for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
            List<KnowledgePointAdjustmentSuggestion> kpSuggestions = generateSingleKpSuggestion(config, selectedTopics, availableTopics);
            if (kpSuggestions != null && !kpSuggestions.isEmpty()) {
                allSuggestions.addAll(kpSuggestions);
            }
        }
        return allSuggestions;
    }

    /**
     * 为单个知识点生成调整建议
     */
    private List<KnowledgePointAdjustmentSuggestion> generateSingleKpSuggestion(
            KnowledgePointConfigRequest config,
            List<Topic> allSelectedTopics,
            List<Topic> allAvailableTopics) {

        List<KnowledgePointAdjustmentSuggestion> suggestions = new ArrayList<>();
        Integer knowledgeId = config.getKnowledgeId().intValue();

        // 筛选出当前知识点的已选题目和可用题目
        List<Topic> kpSelectedTopics = allSelectedTopics.stream()
                .filter(t -> t.getKnowId() != null && t.getKnowId().equals(knowledgeId))
                .collect(Collectors.toList());

        List<Topic> kpAvailableTopics = allAvailableTopics.stream()
                .filter(t -> t.getKnowId() != null && t.getKnowId().equals(knowledgeId))
                .collect(Collectors.toList());

        // 检查并生成简答题调整建议
        if (config.hasShortAnswerConfiguration()) {
            KnowledgePointAdjustmentSuggestion shortAnswerSuggestion = generateShortAnswerSuggestion(
                    knowledgeId, config, kpSelectedTopics, kpAvailableTopics);
            if (shortAnswerSuggestion != null) {
                suggestions.add(shortAnswerSuggestion);
            }
        }

        // 检查并生成总题目数量调整建议 (如果QuestionCount有配置)
        if (config.getQuestionCount() != null && config.getQuestionCount() > 0) {
            KnowledgePointAdjustmentSuggestion countSuggestion = generateCountBasedSuggestion(
                    knowledgeId, config, kpSelectedTopics, kpAvailableTopics);
            // 避免重复添加仅因类型不同而产生的相同调整建议，例如简答题数量不足本身也导致总数不足
            // 这里简单处理：如果已经有简答题建议，并且总数建议与简答题建议调整方向一致（都是增加或都是减少），则可能不再添加总数建议
            // 一个更完善的逻辑是判断总数差距是否完全由简答题差距导致
            // 为简化，此处允许同时存在，由调用方或用户判断
            if (countSuggestion != null) {
                 boolean addCountSuggestion = true;
                 if (!suggestions.isEmpty() && suggestions.get(0).getAdjustmentType().contains("简答题")) {
                     if ((countSuggestion.getSuggestion().contains("增加") && suggestions.get(0).getSuggestion().contains("增加")) ||
                         (countSuggestion.getSuggestion().contains("减少") && suggestions.get(0).getSuggestion().contains("减少"))) {
                         // 如果总数差距和简答题差距方向一致，且总数差距等于简答题差距，则不重复提示
                         try {
                            int shortAnswerDiff =提取数量(suggestions.get(0).getSuggestion());
                            int totalDiff = 提取数量(countSuggestion.getSuggestion());
                            if (Math.abs(shortAnswerDiff) == Math.abs(totalDiff) && 
                                kpSelectedTopics.size() - config.getQuestionCount() == 
                                (kpSelectedTopics.stream().filter(t -> TopicTypeMapper.DB_SHORT_ANSWER.equals(TopicTypeMapper.toDbFormat(t.getType()))).count() - config.getShortAnswerCount())){
                                addCountSuggestion = false;
                            }
                         } catch (NumberFormatException e) { /* 忽略解析错误，继续添加 */ }
                     }
                 }
                 if(addCountSuggestion) {
                    suggestions.add(countSuggestion);
                 }
            }
        }
        return suggestions;
    }

    /**
     * 辅助方法，用于从建议字符串中提取第一个出现的数字。
     * 例如 "需要增加2道简答题" -> 2, "但该知识点下只有10道简答题可用" -> 10.
     * @param suggestion 包含数字的建议字符串
     * @return 提取到的数字，如果未找到则返回0或根据需要抛出异常
     * @throws NumberFormatException 如果找到的组不是有效的整数
     */
    private int 提取数量(String suggestion) throws NumberFormatException {
        // 正确转义正则表达式中的反斜杠
        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("(\\d+)").matcher(suggestion);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return 0; // 或根据业务逻辑抛出更具体的异常，例如 IllegalArgumentException("未在建议中找到数量: " + suggestion)
    }

    /**
     * 基于简答题配置生成建议
     */
    private KnowledgePointAdjustmentSuggestion generateShortAnswerSuggestion(
            Integer knowledgeId,
            KnowledgePointConfigRequest config,
            List<Topic> kpSelectedTopics, // Renamed for clarity, matches other methods
            List<Topic> kpAvailableTopics) { // Renamed for clarity

        // 如果配置中未指定有效的简答题数量，则不生成建议
        if (config.getShortAnswerCount() == null || config.getShortAnswerCount() < 0) {
            return null;
        }
        int expectedShortAnswer = config.getShortAnswerCount();
        String shortTypeName = TopicTypeMapper.getChineseName(TopicTypeMapper.DB_SHORT_ANSWER); // Get display name for short answer type

        long actualShortAnswer = kpSelectedTopics.stream()
            .filter(topic -> TopicTypeMapper.DB_SHORT_ANSWER.equals(TopicTypeMapper.toDbFormat(topic.getType())))
            .count();

        if (actualShortAnswer == expectedShortAnswer) {
            return null; // 简答题数量正确，无需调整
        }

        String adjustment;
        if (actualShortAnswer < expectedShortAnswer) {
            int needed = expectedShortAnswer - (int)actualShortAnswer;
            // 统计当前知识点下，可用的、未被选中的简答题数量
            long availableForKpShortAnswer = kpAvailableTopics.stream()
                .filter(t -> TopicTypeMapper.DB_SHORT_ANSWER.equals(TopicTypeMapper.toDbFormat(t.getType())))
                .count();

            if (availableForKpShortAnswer >= needed) {
                adjustment = String.format("需要增加%d道%s", needed, shortTypeName);
            } else {
                // Corrected the available count variable name in the message
                adjustment = String.format("需要增加%d道%s，但该知识点下只有%d道%s可用", needed, shortTypeName, availableForKpShortAnswer, shortTypeName);
            }
        } else {
            int excess = (int)actualShortAnswer - expectedShortAnswer;
            adjustment = String.format("需要减少%d道%s", excess, shortTypeName);
        }

        return new KnowledgePointAdjustmentSuggestion(knowledgeId,
            String.format("知识点%d-%s数量调整", knowledgeId, shortTypeName), // Using knowledgeId as per previous fixes
             adjustment);
    }

    /**
     * 基于总数量配置生成建议
     */
    private KnowledgePointAdjustmentSuggestion generateCountBasedSuggestion(
            Integer knowledgeId,
            KnowledgePointConfigRequest config,
            List<Topic> kpSelectedTopics, // Topics selected for this specific knowledge point
            List<Topic> kpAvailableTopics) { // Topics available for this specific knowledge point

        // 如果配置中未指定有效的总题目数量，则不生成建议
        if (config.getQuestionCount() == null || config.getQuestionCount() <= 0) {
            return null;
        }
        int expected = config.getQuestionCount();
        int actual = kpSelectedTopics.size();

        if (actual == expected) {
            return null; // 数量正确，无需调整
        }

        String adjustment;
        if (actual < expected) {
            int needed = expected - actual;
            // 统计当前知识点下，可用的、未被选中的题目总数
            long availableForKpTotal = kpAvailableTopics.stream()
                    // .filter(t -> !kpSelectedTopics.contains(t)) // 假设kpAvailableTopics已经是未包含kpSelectedTopics的
                    .count();

            if (availableForKpTotal >= needed) {
                adjustment = String.format("需要增加%d道题目", needed);
            } else {
                adjustment = String.format("需要增加%d道题目，但该知识点下总共只有%d道题目可用", needed, availableForKpTotal);
            }
        } else { // actual > expected
            int excess = actual - expected;
            adjustment = String.format("需要减少%d道题目", excess);
        }

        // KnowledgePointConfigRequest 似乎没有 getKnowledgePointName() 方法。
        // 如果需要在建议中使用知识点名称，请确保该方法可用或传递名称。
        // 目前回退到仅使用 knowledgeId。
        return new KnowledgePointAdjustmentSuggestion(knowledgeId,
            String.format("知识点%d-总题目数量调整", knowledgeId),
            adjustment);
    }



    /**
     * 获取题型的用户友好显示名称。
     * @param type 原始题型字符串
     * @return 用户友好的题型显示名称 (e.g., "单选题", "简答题")
     */
    private String getTypeDisplayName(String type) {
        return TopicTypeMapper.getDisplayName(type);
    }

    /**
     * 知识点约束检查结果
     */
    public static class KnowledgePointConstraintResult {
        private final boolean valid;
        private final String message;

        public KnowledgePointConstraintResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("KnowledgePointConstraintResult{valid=%s, message='%s'}", valid, message);
        }
    }

    /**
     * 知识点调整建议
     */
    public static class KnowledgePointAdjustmentSuggestion {
        private final Integer knowledgeId;
        private final String adjustmentType;
        private final String suggestion;

        public KnowledgePointAdjustmentSuggestion(Integer knowledgeId, String adjustmentType, String suggestion) {
            this.knowledgeId = knowledgeId;
            this.adjustmentType = adjustmentType;
            this.suggestion = suggestion;
        }

        public Integer getKnowledgeId() {
            return knowledgeId;
        }

        public String getAdjustmentType() {
            return adjustmentType;
        }

        public String getSuggestion() {
            return suggestion;
        }

        @Override
        public String toString() {
            return String.format("知识点%d - %s: %s", knowledgeId, adjustmentType, suggestion);
        }
    }
}
