package com.edu.maizi_edu_sys.service.engine;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 题目缓存管理器，用于缓存题目查询结果
 */
@Component
@Slf4j
public class TopicCacheManager {

    // 缓存key格式: knowId:type:difficulty
    private final Map<String, CacheEntry<List<Integer>>> topicIdsCache = new ConcurrentHashMap<>();
    
    // 缓存过期时间（分钟）
    private static final long CACHE_EXPIRY_MINUTES = 30;

    /**
     * 从缓存获取题目ID列表
     */
    public List<Integer> getTopicIdsFromCache(Integer knowId, String type, double difficulty) {
        String key = generateCacheKey(knowId, type, difficulty);
        CacheEntry<List<Integer>> entry = topicIdsCache.get(key);
        
        if (entry != null && !entry.isExpired()) {
            log.debug("Cache hit for key: {}", key);
            return entry.getValue();
        }
        
        log.debug("Cache miss for key: {}", key);
        return null;
    }

    /**
     * 将题目ID列表缓存
     */
    public void cacheTopicIds(Integer knowId, String type, double difficulty, List<Integer> topicIds) {
        if (topicIds == null || topicIds.isEmpty()) {
            return;
        }
        
        String key = generateCacheKey(knowId, type, difficulty);
        CacheEntry<List<Integer>> entry = new CacheEntry<>(topicIds, CACHE_EXPIRY_MINUTES);
        topicIdsCache.put(key, entry);
        log.debug("Cached {} topic IDs with key: {}", topicIds.size(), key);
    }

    /**
     * 清除所有缓存
     */
    public void clearCache() {
        topicIdsCache.clear();
        log.info("All topic IDs cache cleared");
    }

    /**
     * 生成缓存key
     */
    private String generateCacheKey(Integer knowId, String type, double difficulty) {
        return knowId + ":" + type + ":" + difficulty;
    }

    /**
     * 缓存项，包含值和过期时间
     */
    private static class CacheEntry<T> {
        private final T value;
        private final long expiryTime;

        public CacheEntry(T value, long expiryMinutes) {
            this.value = value;
            this.expiryTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(expiryMinutes);
        }

        public T getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expiryTime;
        }
    }
} 