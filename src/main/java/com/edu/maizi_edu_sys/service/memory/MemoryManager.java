package com.edu.maizi_edu_sys.service.memory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.BitSet;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 内存管理器
 * 负责BitSet对象池管理和内存监控
 */
@Component
@Slf4j
public class MemoryManager {
    
    @Value("${algorithm.memory.bitset-pool-size:100}")
    private int maxPoolSize;
    
    @Value("${algorithm.memory.bitset-size:10000}")
    private int bitSetSize;
    
    @Value("${algorithm.memory.gc-frequency:50}")
    private int gcFrequency;
    
    @Value("${algorithm.memory.memory-threshold:0.8}")
    private double memoryThreshold;
    
    private final ConcurrentLinkedQueue<BitSet> bitSetPool = new ConcurrentLinkedQueue<>();
    private final AtomicInteger poolSize = new AtomicInteger(0);
    private final AtomicInteger gcCounter = new AtomicInteger(0);
    private final ScheduledExecutorService memoryMonitor = Executors.newSingleThreadScheduledExecutor();
    
    @PostConstruct
    public void initialize() {
        // 预热BitSet池
        for (int i = 0; i < maxPoolSize / 2; i++) {
            bitSetPool.offer(new BitSet(bitSetSize));
            poolSize.incrementAndGet();
        }
        
        // 启动内存监控
        memoryMonitor.scheduleAtFixedRate(this::monitorMemory, 30, 30, TimeUnit.SECONDS);
        
        log.info("MemoryManager initialized with pool size: {}, bitset size: {}", maxPoolSize, bitSetSize);
    }
    
    @PreDestroy
    public void cleanup() {
        memoryMonitor.shutdown();
        try {
            if (!memoryMonitor.awaitTermination(5, TimeUnit.SECONDS)) {
                memoryMonitor.shutdownNow();
            }
        } catch (InterruptedException e) {
            memoryMonitor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        bitSetPool.clear();
        log.info("MemoryManager cleaned up");
    }
    
    /**
     * 从池中获取BitSet
     */
    public BitSet acquireBitSet() {
        BitSet bitSet = bitSetPool.poll();
        if (bitSet != null) {
            poolSize.decrementAndGet();
            bitSet.clear(); // 清空重用
            return bitSet;
        }
        
        // 池空时创建新对象
        log.debug("BitSet pool empty, creating new instance");
        return new BitSet(bitSetSize);
    }
    
    /**
     * 将BitSet归还到池中
     */
    public void releaseBitSet(BitSet bitSet) {
        if (bitSet != null && poolSize.get() < maxPoolSize) {
            bitSetPool.offer(bitSet);
            poolSize.incrementAndGet();
        }
    }
    
    /**
     * 检查是否需要执行垃圾回收
     */
    public void checkGarbageCollection() {
        int count = gcCounter.incrementAndGet();
        if (count >= gcFrequency) {
            gcCounter.set(0);
            System.gc();
            log.debug("Triggered garbage collection after {} operations", gcFrequency);
        }
    }
    
    /**
     * 获取内存使用情况
     */
    public MemoryInfo getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        return new MemoryInfo(totalMemory, freeMemory, usedMemory, maxMemory, poolSize.get());
    }
    
    /**
     * 检查内存使用是否超过阈值
     */
    public boolean isMemoryPressure() {
        MemoryInfo info = getMemoryInfo();
        double usageRatio = (double) info.getUsedMemory() / info.getTotalMemory();
        return usageRatio > memoryThreshold;
    }
    
    /**
     * 内存监控任务
     */
    private void monitorMemory() {
        try {
            MemoryInfo info = getMemoryInfo();
            double usageRatio = (double) info.getUsedMemory() / info.getTotalMemory();
            
            if (usageRatio > 0.9) {
                log.error("Critical memory usage: {}%, used: {}MB, total: {}MB", 
                         usageRatio * 100, info.getUsedMemory() / 1024 / 1024, 
                         info.getTotalMemory() / 1024 / 1024);
                
                // 强制垃圾回收
                System.gc();
                
                // 清理部分池对象
                int clearCount = poolSize.get() / 4;
                for (int i = 0; i < clearCount; i++) {
                    if (bitSetPool.poll() != null) {
                        poolSize.decrementAndGet();
                    }
                }
                
            } else if (usageRatio > memoryThreshold) {
                log.warn("High memory usage: {}%, used: {}MB, total: {}MB", 
                        usageRatio * 100, info.getUsedMemory() / 1024 / 1024, 
                        info.getTotalMemory() / 1024 / 1024);
            } else {
                log.debug("Memory usage: {}%, pool size: {}", usageRatio * 100, poolSize.get());
            }
            
        } catch (Exception e) {
            log.error("Error in memory monitoring: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 内存信息类
     */
    public static class MemoryInfo {
        private final long totalMemory;
        private final long freeMemory;
        private final long usedMemory;
        private final long maxMemory;
        private final int poolSize;
        
        public MemoryInfo(long totalMemory, long freeMemory, long usedMemory, long maxMemory, int poolSize) {
            this.totalMemory = totalMemory;
            this.freeMemory = freeMemory;
            this.usedMemory = usedMemory;
            this.maxMemory = maxMemory;
            this.poolSize = poolSize;
        }
        
        public long getTotalMemory() { return totalMemory; }
        public long getFreeMemory() { return freeMemory; }
        public long getUsedMemory() { return usedMemory; }
        public long getMaxMemory() { return maxMemory; }
        public int getPoolSize() { return poolSize; }
        
        public double getUsageRatio() {
            return (double) usedMemory / totalMemory;
        }
        
        @Override
        public String toString() {
            return String.format("Memory[used=%dMB, total=%dMB, usage=%.1f%%, pool=%d]",
                               usedMemory / 1024 / 1024, totalMemory / 1024 / 1024,
                               getUsageRatio() * 100, poolSize);
        }
    }
}
