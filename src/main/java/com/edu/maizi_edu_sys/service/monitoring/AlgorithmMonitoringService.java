package com.edu.maizi_edu_sys.service.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.DoubleAdder;

/**
 * 算法监控服务
 * 负责收集和分析算法执行的各种指标
 */
@Service
@Slf4j
public class AlgorithmMonitoringService {
    
    @Value("${algorithm.monitoring.enabled:true}")
    private boolean monitoringEnabled;
    
    @Value("${algorithm.monitoring.log-level:INFO}")
    private String logLevel;
    
    // 执行统计
    private final AtomicLong totalExecutions = new AtomicLong(0);
    private final AtomicLong successfulExecutions = new AtomicLong(0);
    private final AtomicLong failedExecutions = new AtomicLong(0);
    
    // 性能统计
    private final DoubleAdder totalExecutionTime = new DoubleAdder();
    private final DoubleAdder totalFitnessSum = new DoubleAdder();
    private final AtomicInteger totalGenerations = new AtomicInteger(0);
    
    // 修复算子统计
    private final AtomicLong repairAttempts = new AtomicLong(0);
    private final AtomicLong repairSuccesses = new AtomicLong(0);
    private final DoubleAdder totalRepairTime = new DoubleAdder();
    
    // 线程安全统计
    private final AtomicLong threadSafetyViolations = new AtomicLong(0);
    private final AtomicLong concurrentRequests = new AtomicLong(0);
    
    // 内存统计
    private final DoubleAdder peakMemoryUsage = new DoubleAdder();
    private final AtomicLong memoryWarnings = new AtomicLong(0);
    
    // 详细指标存储
    private final ConcurrentHashMap<String, Object> detailedMetrics = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        if (monitoringEnabled) {
            log.info("Algorithm monitoring service initialized with log level: {}", logLevel);
        }
    }
    
    /**
     * 记录算法执行开始
     */
    public ExecutionContext startExecution(String requestId) {
        if (!monitoringEnabled) {
            return new ExecutionContext(requestId, false);
        }
        
        totalExecutions.incrementAndGet();
        return new ExecutionContext(requestId, true);
    }
    
    /**
     * 记录算法执行成功
     */
    public void recordSuccess(ExecutionContext context, double bestFitness, int generations) {
        if (!context.isEnabled()) return;
        
        successfulExecutions.incrementAndGet();
        totalFitnessSum.add(bestFitness);
        totalGenerations.addAndGet(generations);
        
        long executionTime = context.getExecutionTime();
        totalExecutionTime.add(executionTime);
        
        if ("DEBUG".equals(logLevel)) {
            log.debug("Algorithm execution successful - ID: {}, Time: {}ms, Fitness: {:.3f}, Generations: {}", 
                     context.getRequestId(), executionTime, bestFitness, generations);
        } else if ("INFO".equals(logLevel)) {
            log.info("Algorithm completed successfully in {}ms with fitness {:.3f}", executionTime, bestFitness);
        }
    }
    
    /**
     * 记录算法执行失败
     */
    public void recordFailure(ExecutionContext context, String errorType, String errorMessage) {
        if (!context.isEnabled()) return;
        
        failedExecutions.incrementAndGet();
        
        long executionTime = context.getExecutionTime();
        totalExecutionTime.add(executionTime);
        
        log.error("Algorithm execution failed - ID: {}, Time: {}ms, Error: {}, Message: {}", 
                 context.getRequestId(), executionTime, errorType, errorMessage);
        
        // 记录错误类型统计
        String errorKey = "error_" + errorType;
        detailedMetrics.compute(errorKey, (k, v) -> v == null ? 1 : ((Integer) v) + 1);
    }
    
    /**
     * 记录修复算子使用
     */
    public void recordRepairAttempt(boolean success, long repairTime) {
        if (!monitoringEnabled) return;
        
        repairAttempts.incrementAndGet();
        if (success) {
            repairSuccesses.incrementAndGet();
        }
        totalRepairTime.add(repairTime);
        
        if ("DEBUG".equals(logLevel)) {
            log.debug("Repair operation: success={}, time={}ms", success, repairTime);
        }
    }
    
    /**
     * 记录线程安全违规
     */
    public void recordThreadSafetyViolation(String violationType) {
        if (!monitoringEnabled) return;
        
        threadSafetyViolations.incrementAndGet();
        log.warn("Thread safety violation detected: {}", violationType);
        
        String violationKey = "violation_" + violationType;
        detailedMetrics.compute(violationKey, (k, v) -> v == null ? 1 : ((Integer) v) + 1);
    }
    
    /**
     * 记录并发请求
     */
    public void recordConcurrentRequest() {
        if (!monitoringEnabled) return;
        
        concurrentRequests.incrementAndGet();
        log.warn("Concurrent request detected and rejected");
    }
    
    /**
     * 记录内存使用
     */
    public void recordMemoryUsage(double usageRatio) {
        if (!monitoringEnabled) return;
        
        peakMemoryUsage.add(usageRatio);
        
        if (usageRatio > 0.8) {
            memoryWarnings.incrementAndGet();
            if (usageRatio > 0.9) {
                log.error("Critical memory usage: {:.1f}%", usageRatio * 100);
            } else {
                log.warn("High memory usage: {:.1f}%", usageRatio * 100);
            }
        }
    }
    
    /**
     * 获取监控统计信息
     */
    public MonitoringStats getStats() {
        if (!monitoringEnabled) {
            return new MonitoringStats();
        }
        
        long total = totalExecutions.get();
        long successful = successfulExecutions.get();
        long failed = failedExecutions.get();
        
        double avgExecutionTime = total > 0 ? totalExecutionTime.sum() / total : 0;
        double avgFitness = successful > 0 ? totalFitnessSum.sum() / successful : 0;
        double avgGenerations = successful > 0 ? (double) totalGenerations.get() / successful : 0;
        
        double successRate = total > 0 ? (double) successful / total : 0;
        
        long repairTotal = repairAttempts.get();
        long repairSuccess = repairSuccesses.get();
        double repairSuccessRate = repairTotal > 0 ? (double) repairSuccess / repairTotal : 0;
        double avgRepairTime = repairTotal > 0 ? totalRepairTime.sum() / repairTotal : 0;
        
        return new MonitoringStats(
            total, successful, failed, successRate,
            avgExecutionTime, avgFitness, avgGenerations,
            repairTotal, repairSuccessRate, avgRepairTime,
            threadSafetyViolations.get(), concurrentRequests.get(),
            memoryWarnings.get(), new ConcurrentHashMap<>(detailedMetrics)
        );
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        if (!monitoringEnabled) return;
        
        totalExecutions.set(0);
        successfulExecutions.set(0);
        failedExecutions.set(0);
        totalExecutionTime.reset();
        totalFitnessSum.reset();
        totalGenerations.set(0);
        repairAttempts.set(0);
        repairSuccesses.set(0);
        totalRepairTime.reset();
        threadSafetyViolations.set(0);
        concurrentRequests.set(0);
        peakMemoryUsage.reset();
        memoryWarnings.set(0);
        detailedMetrics.clear();
        
        log.info("Monitoring statistics reset");
    }
    
    /**
     * 执行上下文类
     */
    public static class ExecutionContext {
        private final String requestId;
        private final boolean enabled;
        private final long startTime;
        
        public ExecutionContext(String requestId, boolean enabled) {
            this.requestId = requestId;
            this.enabled = enabled;
            this.startTime = System.currentTimeMillis();
        }
        
        public String getRequestId() { return requestId; }
        public boolean isEnabled() { return enabled; }
        public long getExecutionTime() { return System.currentTimeMillis() - startTime; }
    }
    
    /**
     * 监控统计信息类
     */
    public static class MonitoringStats {
        private final long totalExecutions;
        private final long successfulExecutions;
        private final long failedExecutions;
        private final double successRate;
        private final double avgExecutionTime;
        private final double avgFitness;
        private final double avgGenerations;
        private final long repairAttempts;
        private final double repairSuccessRate;
        private final double avgRepairTime;
        private final long threadSafetyViolations;
        private final long concurrentRequests;
        private final long memoryWarnings;
        private final ConcurrentHashMap<String, Object> detailedMetrics;
        
        public MonitoringStats() {
            this(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, new ConcurrentHashMap<>());
        }
        
        public MonitoringStats(long totalExecutions, long successfulExecutions, long failedExecutions,
                             double successRate, double avgExecutionTime, double avgFitness, double avgGenerations,
                             long repairAttempts, double repairSuccessRate, double avgRepairTime,
                             long threadSafetyViolations, long concurrentRequests, long memoryWarnings,
                             ConcurrentHashMap<String, Object> detailedMetrics) {
            this.totalExecutions = totalExecutions;
            this.successfulExecutions = successfulExecutions;
            this.failedExecutions = failedExecutions;
            this.successRate = successRate;
            this.avgExecutionTime = avgExecutionTime;
            this.avgFitness = avgFitness;
            this.avgGenerations = avgGenerations;
            this.repairAttempts = repairAttempts;
            this.repairSuccessRate = repairSuccessRate;
            this.avgRepairTime = avgRepairTime;
            this.threadSafetyViolations = threadSafetyViolations;
            this.concurrentRequests = concurrentRequests;
            this.memoryWarnings = memoryWarnings;
            this.detailedMetrics = detailedMetrics;
        }
        
        // Getters
        public long getTotalExecutions() { return totalExecutions; }
        public long getSuccessfulExecutions() { return successfulExecutions; }
        public long getFailedExecutions() { return failedExecutions; }
        public double getSuccessRate() { return successRate; }
        public double getAvgExecutionTime() { return avgExecutionTime; }
        public double getAvgFitness() { return avgFitness; }
        public double getAvgGenerations() { return avgGenerations; }
        public long getRepairAttempts() { return repairAttempts; }
        public double getRepairSuccessRate() { return repairSuccessRate; }
        public double getAvgRepairTime() { return avgRepairTime; }
        public long getThreadSafetyViolations() { return threadSafetyViolations; }
        public long getConcurrentRequests() { return concurrentRequests; }
        public long getMemoryWarnings() { return memoryWarnings; }
        public ConcurrentHashMap<String, Object> getDetailedMetrics() { return detailedMetrics; }
    }
}
