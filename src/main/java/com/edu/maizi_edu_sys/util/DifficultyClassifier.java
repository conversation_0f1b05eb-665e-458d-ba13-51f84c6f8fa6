package com.edu.maizi_edu_sys.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 统一的难度分类工具类
 * 确保系统中所有组件使用相同的难度分类标准
 */
@Component
public class DifficultyClassifier {

    @Value("${algorithm.genetic.difficulty.easy-max:0.4}")
    private double DIFF_EASY_MAX;

    @Value("${algorithm.genetic.difficulty.medium-max:0.7}")
    private double DIFF_MEDIUM_MAX;

    @Value("${algorithm.genetic.difficulty.hard-min:0.71}")
    private double DIFF_HARD_MIN;

    /**
     * 将数值难度值映射为难度名称
     * 
     * @param difficultyValue 数值型难度值
     * @return 难度名称："easy", "medium", "hard"
     */
    public String getDifficultyName(Double difficultyValue) {
        if (difficultyValue == null) {
            return "medium"; // 默认中等难度
        }

        if (difficultyValue <= DIFF_EASY_MAX) {
            return "easy";
        } else if (difficultyValue <= DIFF_MEDIUM_MAX) {
            return "medium";  
        } else {
            return "hard";
        }
    }

    /**
     * 获取简单题最大难度阈值
     */
    public double getEasyMaxThreshold() {
        return DIFF_EASY_MAX;
    }

    /**
     * 获取中等题最大难度阈值
     */
    public double getMediumMaxThreshold() {
        return DIFF_MEDIUM_MAX;
    }

    /**
     * 获取困难题最小难度阈值
     */
    public double getHardMinThreshold() {
        return DIFF_HARD_MIN;
    }

    /**
     * 判断是否为简单题
     */
    public boolean isEasy(Double difficultyValue) {
        return difficultyValue != null && difficultyValue <= DIFF_EASY_MAX;
    }

    /**
     * 判断是否为中等题
     */
    public boolean isMedium(Double difficultyValue) {
        return difficultyValue != null && difficultyValue > DIFF_EASY_MAX && difficultyValue <= DIFF_MEDIUM_MAX;
    }

    /**
     * 判断是否为困难题
     */
    public boolean isHard(Double difficultyValue) {
        return difficultyValue != null && difficultyValue >= DIFF_HARD_MIN;
    }
}