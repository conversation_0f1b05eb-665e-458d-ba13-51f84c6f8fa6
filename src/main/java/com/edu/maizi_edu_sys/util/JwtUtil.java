package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.entity.User;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Component
@Slf4j
public class JwtUtil {

    @Value("${jwt.secret}")
    private String secretKeyString;

    @Value("${jwt.expiration}")
    private Long expiration;

    @PostConstruct
    public void init() {
        log.info("JwtUtil initialized.");
        if (secretKeyString == null || secretKeyString.isEmpty()) {
            log.error("CRITICAL: JWT Secret Key (jwt.secret) IS NOT LOADED or IS EMPTY from properties! Token generation and validation will likely fail.");
        } else {
            log.info("Loaded JWT Secret Key (first 5 chars): '{}...', Length: {}", secretKeyString.substring(0, Math.min(secretKeyString.length(), 5)), secretKeyString.length());
        }
        if (expiration == null || expiration <= 0) {
            log.error("CRITICAL: JWT Expiration (jwt.expiration) IS NOT LOADED or IS INVALID from properties!");
        } else {
            log.info("Loaded JWT Expiration: {} ms", expiration);
        }
    }

    private SecretKey getSigningKey() {
        if (secretKeyString == null || secretKeyString.isEmpty()) {
            log.error("Attempting to get signing key, but secretKeyString is null or empty. This will cause an error.");
            throw new IllegalStateException("JWT secret key is not configured properly.");
        }
        byte[] keyBytes = secretKeyString.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String generateToken(User user) {
        Date now = new Date();
        if (expiration == null || expiration <= 0) {
            log.error("Cannot generate token: JWT expiration is not configured correctly.");
            throw new IllegalStateException("JWT expiration is not configured properly.");
        }
        Date expiryDate = new Date(now.getTime() + expiration);
        log.info("Generating token for user: {} with secret (first 5): '{}...'", user.getUsername(), secretKeyString.substring(0, Math.min(secretKeyString.length(), 5)));
        return Jwts.builder()
                .setSubject(user.getUsername())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey())
                .compact();
    }

    public String getUsernameFromToken(String token) {
        log.debug("Attempting to get username from token using secret (first 5): '{}...'", secretKeyString.substring(0, Math.min(secretKeyString.length(), 5)));
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getSubject();
    }

    public boolean validateToken(String token) {
        try {
            log.debug("Validating token using secret (first 5): '{}...'", secretKeyString.substring(0, Math.min(secretKeyString.length(), 5)));
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (io.jsonwebtoken.security.SignatureException se) {
            log.error("Token Signature Exception: {}. Token: [{}]", se.getMessage(), token);
        } catch (io.jsonwebtoken.MalformedJwtException mje) {
            log.error("Malformed JWT: {}. Token: [{}]", mje.getMessage(), token);
        } catch (io.jsonwebtoken.ExpiredJwtException eje) {
            log.error("Expired JWT: {}. Token: [{}]", eje.getMessage(), token);
        } catch (io.jsonwebtoken.UnsupportedJwtException uje) {
            log.error("Unsupported JWT: {}. Token: [{}]", uje.getMessage(), token);
        } catch (IllegalArgumentException iae) {
            log.error("JWT claims string is empty or null: {}. Token: [{}]", iae.getMessage(), token);
        } catch (Exception e) {
            log.error("Unexpected error validating token: {}. Token: [{}]", e.getMessage(), token, e);
        }
        return false;
    }
} 