package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 内存安全工具类
 * 提供内存检查和安全查询的工具方法
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
public class MemorySafetyUtil {
    
    private static final double DEFAULT_MEMORY_THRESHOLD = 0.8;
    private static final int DEFAULT_MAX_QUERY_LIMIT = 1000;
    
    /**
     * 检查当前内存使用情况
     * 
     * @return 内存使用率（0.0-1.0）
     */
    public static double getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        return (double) usedMemory / maxMemory;
    }
    
    /**
     * 检查内存是否安全
     * 
     * @return true如果内存使用率低于阈值
     */
    public static boolean isMemorySafe() {
        return isMemorySafe(DEFAULT_MEMORY_THRESHOLD);
    }
    
    /**
     * 检查内存是否安全
     * 
     * @param threshold 内存使用率阈值
     * @return true如果内存使用率低于阈值
     */
    public static boolean isMemorySafe(double threshold) {
        double usage = getMemoryUsage();
        if (usage > threshold) {
            log.warn("内存使用率过高: {:.2f}% (阈值: {:.2f}%)", usage * 100, threshold * 100);
            return false;
        }
        return true;
    }
    
    /**
     * 根据内存情况和数据量确定安全的查询限制
     * 
     * @param totalCount 总数据量
     * @return 安全的查询限制数量
     */
    public static int getSafeQueryLimit(long totalCount) {
        double memoryUsage = getMemoryUsage();
        
        if (memoryUsage > 0.9) {
            // 内存使用率超过90%，严格限制
            return Math.min(100, (int) totalCount);
        } else if (memoryUsage > 0.8) {
            // 内存使用率超过80%，中等限制
            return Math.min(500, (int) totalCount);
        } else if (totalCount > 10000) {
            // 数据量过大，预防性限制
            return DEFAULT_MAX_QUERY_LIMIT;
        } else {
            // 内存和数据量都安全
            return (int) totalCount;
        }
    }
    
    /**
     * 记录内存使用情况
     * 
     * @param operation 操作名称
     */
    public static void logMemoryUsage(String operation) {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double usage = (double) usedMemory / maxMemory;
        
        log.debug("内存使用情况 [{}]: 使用率={:.2f}%, 已用={}MB, 最大={}MB", 
            operation, usage * 100, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024);
    }
    
    /**
     * 强制垃圾回收（谨慎使用）
     */
    public static void forceGarbageCollection() {
        log.warn("强制执行垃圾回收");
        System.gc();
        
        // 等待一小段时间让GC完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        logMemoryUsage("GC后");
    }
    
    /**
     * 获取内存信息的详细报告
     * 
     * @return 内存信息字符串
     */
    public static String getMemoryReport() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;
        double usage = (double) usedMemory / maxMemory;
        
        return String.format(
            "内存报告: 使用率=%.2f%%, 已用=%dMB, 空闲=%dMB, 总计=%dMB, 最大=%dMB",
            usage * 100,
            usedMemory / 1024 / 1024,
            freeMemory / 1024 / 1024,
            totalMemory / 1024 / 1024,
            maxMemory / 1024 / 1024
        );
    }
}
