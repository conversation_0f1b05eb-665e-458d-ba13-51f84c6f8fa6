package com.edu.maizi_edu_sys.util;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationResponse<T> {
    private List<T> data;        // List of records for the current page
    private long totalRecords;   // Total number of records available
    private long currentPage;    // Current page number
    private long totalPages;     // Total number of pages
    private long pageSize;       // Number of records per page
} 