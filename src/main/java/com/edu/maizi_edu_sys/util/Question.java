package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.File;
import java.io.IOException;
import java.util.List;

@Data
public class Question {

    @NotNull(message = "知识点ID不能为空")
    @JsonProperty("know_id")
    private Integer knowId;

    @NotBlank(message = "题型不能为空")
    @Pattern(regexp = "^(choice|multiple|judge|fill|short|subjective|group)$", message = "无效的题型")
    private String type;

    @NotBlank(message = "题目标题不能为空")
    private String title;

    private List<TopicDTO.OptionDTO> options;

    private String answer;

    private String parse;

    @NotBlank(message = "来源不能为空")
    private String source;



    @Data
    public static class OptionDTO {
        @NotBlank(message = "选项key不能为空")
        @Pattern(regexp = "^[A-Z]$", message = "选项key必须是大写字母")
        private String key;

        @NotBlank(message = "选项内容不能为空")
        private String name;
    }


    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            File file = new File("C:\\Users\\<USER>\\IdeaProjects\\maizi_edu_sys\\src\\main\\resources\\json\\test.json");

            // 如果 JSON 是数组，使用下面这行
            List<Question> qs = objectMapper.readValue(file, new TypeReference<List<Question>>() {});

            // 如果 JSON 是单个对象，使用下面这行
            // Question q = objectMapper.readValue(file, Question.class);

            for (Question q : qs) {
                System.out.println(q.getTitle());
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
