package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;
import javax.servlet.http.HttpServletRequest;

/**
 * Utility class for HTTP request related operations
 */
@Slf4j
public class RequestUtil {

    /**
     * Get client IP address from request
     * Checks various headers to handle proxies and load balancers
     *
     * @param request HTTP request
     * @return client IP address
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (isInvalidIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (isInvalidIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (isInvalidIp(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (isInvalidIp(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (isInvalidIp(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // If X-Forwarded-For contains multiple IPs (client, proxy1, proxy2,...), take the first one
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        log.debug("Client IP detected: {}", ip);
        return ip;
    }
    
    /**
     * Extract token from Authorization header
     * Removes 'Bearer ' prefix if present
     *
     * @param tokenHeader Authorization header value
     * @return extracted token or empty string if header is empty
     */
    public static String extractTokenFromHeader(String tokenHeader) {
        if (tokenHeader == null || tokenHeader.isEmpty()) {
            return "";
        }
        
        if (tokenHeader.toLowerCase().startsWith("bearer ")) {
            return tokenHeader.substring(7);
        }
        
        return tokenHeader;
    }
    
    /**
     * Mask token for secure logging
     * Shows only first and last 4 characters
     *
     * @param token the token to mask
     * @return masked token
     */
    public static String maskToken(String token) {
        if (token == null || token.length() < 10) {
            return "[MASKED_TOKEN]";
        }
        // Show only first 4 and last 4 characters
        return token.substring(0, 4) + "..." + token.substring(token.length() - 4);
    }
    
    private static boolean isInvalidIp(String ip) {
        return ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip);
    }
} 