package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户上下文工具类
 * 用于获取当前请求的用户信息
 */
@Slf4j
public class UserContextUtil {
    
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String USERNAME_HEADER = "X-Username";
    private static final String USER_ID_ATTRIBUTE = "userId";
    private static final String USERNAME_ATTRIBUTE = "username";
    
    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return null;
            }
            
            // 优先从请求属性获取（认证拦截器设置）
            Object userIdAttr = request.getAttribute(USER_ID_ATTRIBUTE);
            if (userIdAttr != null) {
                if (userIdAttr instanceof Long) {
                    return (Long) userIdAttr;
                } else if (userIdAttr instanceof String) {
                    try {
                        return Long.parseLong((String) userIdAttr);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析用户ID: {}", userIdAttr);
                    }
                }
            }
            
            // 从请求头获取
            String userIdHeader = request.getHeader(USER_ID_HEADER);
            if (userIdHeader != null && !userIdHeader.isEmpty()) {
                try {
                    return Long.parseLong(userIdHeader);
                } catch (NumberFormatException e) {
                    log.warn("无法解析请求头中的用户ID: {}", userIdHeader);
                }
            }
            
            return null;
        } catch (Exception e) {
            log.debug("获取当前用户ID失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return "未知用户";
            }
            
            // 优先从请求属性获取（认证拦截器设置）
            Object usernameAttr = request.getAttribute(USERNAME_ATTRIBUTE);
            if (usernameAttr != null) {
                return usernameAttr.toString();
            }
            
            // 从请求头获取
            String usernameHeader = request.getHeader(USERNAME_HEADER);
            if (usernameHeader != null && !usernameHeader.isEmpty()) {
                return usernameHeader;
            }
            
            return "当前用户";
        } catch (Exception e) {
            log.debug("获取当前用户名失败: {}", e.getMessage());
            return "未知用户";
        }
    }
    
    /**
     * 获取当前请求
     */
    private static HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.debug("获取当前请求失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 设置用户信息到请求属性（供认证拦截器使用）
     */
    public static void setUserInfo(HttpServletRequest request, Long userId, String username) {
        if (request != null) {
            if (userId != null) {
                request.setAttribute(USER_ID_ATTRIBUTE, userId);
            }
            if (username != null && !username.isEmpty()) {
                request.setAttribute(USERNAME_ATTRIBUTE, username);
            }
        }
    }
    
    /**
     * 清除用户信息
     */
    public static void clearUserInfo(HttpServletRequest request) {
        if (request != null) {
            request.removeAttribute(USER_ID_ATTRIBUTE);
            request.removeAttribute(USERNAME_ATTRIBUTE);
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    public static String getClientIpAddress() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return "unknown";
            }
            
            String xForwardedFor = request.getHeader("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                return xForwardedFor.split(",")[0].trim();
            }
            
            String xRealIp = request.getHeader("X-Real-IP");
            if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
                return xRealIp;
            }
            
            return request.getRemoteAddr();
        } catch (Exception e) {
            log.debug("获取客户端IP地址失败: {}", e.getMessage());
            return "unknown";
        }
    }
    
    /**
     * 获取用户代理
     */
    public static String getUserAgent() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return "unknown";
            }
            
            return request.getHeader("User-Agent");
        } catch (Exception e) {
            log.debug("获取用户代理失败: {}", e.getMessage());
            return "unknown";
        }
    }
}
