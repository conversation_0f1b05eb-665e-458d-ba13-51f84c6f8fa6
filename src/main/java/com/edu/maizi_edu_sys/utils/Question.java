package com.edu.maizi_edu_sys.utils;

import lombok.Data;

import java.util.List;
@Data
public class Question {
    private int know_id;
    private String type;
    private String title;
    private String tags;
    private List<Option> options; // 嵌套对象列表
    private String answer;
    private String source;
    private String parse;
    private double difficulty;
    
    // Getters and Setters (必须)
    // 省略getter/setter，实际代码需补充
}

