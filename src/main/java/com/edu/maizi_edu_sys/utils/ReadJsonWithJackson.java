package com.edu.maizi_edu_sys.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class ReadJsonWithJackson {
    public static void main(String[] args) {
        ObjectMapper mapper = new ObjectMapper();

        // 启用格式化输出（美化JSON）
        mapper.enable(SerializationFeature.INDENT_OUTPUT);

        try {
            // 1. 读取JSON文件
            List<Question> questions = mapper.readValue(
                    Paths.get("D:\\IdeaProject\\maizi_edu_sys\\src\\main\\resources\\json\\test.json").toFile(),
                    mapper.getTypeFactory().constructCollectionType(List.class, Question.class)
            );

            // 2. 遍历并修改数据（示例修改）
            for (Question q : questions) {
                if (!q.getTitle().contains("（    ）"))
                    q.setTitle(q.getTitle() + "（    ）");

            }

            // 3. 序列化回JSON格式
            // 方法1：序列化为字符串
//            String jsonOutput = mapper.writeValueAsString(questions);
//            System.out.println("Modified JSON:\n" + jsonOutput);

            // 方法2：直接写入新文件（推荐）
            mapper.writeValue(Paths.get("D:\\IdeaProject\\maizi_edu_sys\\src\\main\\resources\\json\\out.json").toFile(), questions);

            System.out.println("Modified data has been written to modified_data.json");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}