package com.edu.maizi_edu_sys.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class SqlGenerator {

    // ======================= CONFIGURATION (Modify these values) =======================

    // 1. MySQL Database Connection Details
    private static final String DB_URL = "*************************************************************************";
    private static final String DB_USER = "your_username";
    private static final String DB_PASSWORD = "your_password";

    // 2. Excel File Path
    private static final String EXCEL_FILE_PATH = "path/to/your/knowledge_data.xlsx";

    // 3. Output SQL File Name
    private static final String OUTPUT_SQL_FILE = "insert_knowledge.sql";

    // 4. Default values for the new records
    private static final int DEFAULT_IS_FREE = 1; // 0=不免费, 1=免费
    private static final int DEFAULT_SORT = 0;    // 排序值

    // ======================= MAIN LOGIC (No modification needed below) =======================

    public static void main(String[] args) {
        try {
            // 1. Fetch all existing knowledge IDs from the database
            System.out.println("Connecting to database to fetch existing IDs...");
            Set<Integer> existingIds = getExistingKnowledgeIds();
            System.out.printf("Found %d existing knowledge IDs in the database.%n", existingIds.size());

            // 2. Read Excel and prepare SQL statements
            System.out.printf("Reading Excel file: %s%n", EXCEL_FILE_PATH);
            List<String> sqlStatements = generateSqlFromExcel(existingIds);

            // 3. Write the generated SQL statements to a file
            if (sqlStatements.isEmpty()) {
                System.out.println("\nNo new knowledge points to insert. The database is up to date.");
            } else {
                writeToFile(sqlStatements);
                System.out.printf("\n==================== SUCCESS ====================%n");
                System.out.printf("Generated %d new INSERT statements.%n", sqlStatements.size());
                System.out.printf("SQL script saved to: %s%n", OUTPUT_SQL_FILE);
            }

        } catch (SQLException e) {
            System.err.println("Database Error: " + e.getMessage());
            e.printStackTrace();
        } catch (IOException e) {
            System.err.println("File Error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Connects to the DB and retrieves a set of all 'knowledge_id's.
     */
    private static Set<Integer> getExistingKnowledgeIds() throws SQLException {
        Set<Integer> ids = new HashSet<>();
        String query = "SELECT knowledge_id FROM wm_knowledge";

        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(query)) {

            while (rs.next()) {
                ids.add(rs.getInt("knowledge_id"));
            }
        }
        return ids;
    }

    /**
     * Reads the Excel file, compares with existing IDs, and generates INSERT statements.
     */
    private static List<String> generateSqlFromExcel(Set<Integer> existingIds) throws IOException {
        List<String> statements = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(EXCEL_FILE_PATH);
             Workbook workbook = new XSSFWorkbook(fis)) { // For .xlsx files

            Sheet sheet = workbook.getSheetAt(0); // Get the first sheet
            DataFormatter dataFormatter = new DataFormatter();

            // Iterate through rows, skipping the header (i=0)
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null || row.getCell(0) == null) {
                    continue; // Skip empty rows
                }

                try {
                    // Excel columns are 0-indexed: Col 1 -> 0, Col 2 -> 1, Col 4 -> 3
                    Cell idCell = row.getCell(0);
                    if (idCell.getCellType() != CellType.NUMERIC) {
                        System.err.printf("Warning: Skipping row %d due to non-numeric ID.%n", i + 1);
                        continue;
                    }
                    int knowledgeId = (int) idCell.getNumericCellValue();

                    // Check if the ID already exists in the DB or was just added from the Excel
                    if (existingIds.contains(knowledgeId)) {
                        continue;
                    }

                    String groupName = dataFormatter.formatCellValue(row.getCell(1));
                    String knowledgeName = dataFormatter.formatCellValue(row.getCell(3));

                    // Escape single quotes for SQL compatibility
                    String groupNameEscaped = groupName.replace("'", "''");
                    String knowledgeNameEscaped = knowledgeName.replace("'", "''");

                    // Build the INSERT statement
                    String sql = String.format(
                        "INSERT INTO wm_knowledge (knowledge_id, knowledge_name, group_name, is_free, sort) VALUES (%d, '%s', '%s', %d, %d);",
                        knowledgeId,
                        knowledgeNameEscaped,
                        groupNameEscaped,
                        DEFAULT_IS_FREE,
                        DEFAULT_SORT
                    );
                    statements.add(sql);

                    // Add the new ID to the set to prevent duplicate insertions from the same Excel file
                    existingIds.add(knowledgeId);

                } catch (Exception e) {
                    System.err.printf("Warning: Could not process row %d. Please check data format. Skipping. Error: %s%n", i + 1, e.getMessage());
                }
            }
        }
        return statements;
    }

    /**
     * Writes a list of strings to the output file.
     */
    private static void writeToFile(List<String> lines) throws IOException {
        try (FileWriter writer = new FileWriter(OUTPUT_SQL_FILE)) {
            for (String line : lines) {
                writer.write(line + System.lineSeparator());
            }
        }
    }
}