package com.edu.maizi_edu_sys.validation;

import com.edu.maizi_edu_sys.config.FitnessWeightsConfig;
import com.edu.maizi_edu_sys.dto.PaperGenerationRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 组卷请求验证器
 * 负责验证组卷请求的合法性和合理性
 */
@Component
@Slf4j
public class PaperGenerationValidator {

    @Autowired
    private FitnessWeightsConfig fitnessWeightsConfig;
    
    /**
     * 验证组卷请求
     */
    public ValidationResult validate(PaperGenerationRequest request) {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        Map<String, Object> suggestions = new HashMap<>();
        
        try {
            // 基础字段验证
            validateBasicFields(request, errors);
            
            // 权重配置验证 - 使用配置文件中的权重
            validateWeights(fitnessWeightsConfig.getWeightsMap(), errors, warnings);
            
            // 约束条件验证
            validateConstraints(request, errors, warnings);
            
            // 性能影响评估
            assessPerformanceImpact(request, warnings, suggestions);
            
        } catch (Exception e) {
            log.error("Validation failed with exception: {}", e.getMessage(), e);
            errors.add("验证过程中发生异常: " + e.getMessage());
        }
        
        return new ValidationResult(errors.isEmpty(), errors, warnings, suggestions);
    }
    
    private void validateBasicFields(PaperGenerationRequest request, List<String> errors) {
        if (request == null) {
            errors.add("组卷请求不能为空");
            return;
        }
        
        if (request.getTotalScore() <= 0) {
            errors.add("目标总分必须大于0");
        }
        
        if (request.getTopicTypeCounts() == null || request.getTopicTypeCounts().isEmpty()) {
            errors.add("题型配置不能为空");
        }
        
        // 验证题型数量 - 允许某些题型数量为0，但至少要有一种题型数量大于0
        if (request.getTopicTypeCounts() != null) {
            boolean hasValidType = false;
            for (Map.Entry<String, Integer> entry : request.getTopicTypeCounts().entrySet()) {
                if (entry.getValue() < 0) {
                    errors.add(String.format("题型%s的数量不能为负数", entry.getKey()));
                } else if (entry.getValue() > 0) {
                    hasValidType = true;
                }
            }

            if (!hasValidType) {
                errors.add("至少需要设置一种题型的数量大于0");
            }
        }
    }
    
    private void validateWeights(Map<String, Double> weights, List<String> errors, List<String> warnings) {
        if (weights == null || weights.isEmpty()) {
            errors.add("适应度权重配置不能为空");
            return;
        }
        
        // 检查权重总和
        double sum = weights.values().stream().mapToDouble(Double::doubleValue).sum();
        if (Math.abs(sum - 1.0) > 0.001) {
            errors.add(String.format("权重总和必须等于1.0，当前为%.3f", sum));
        }
        
        // 检查权重范围
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            double weight = entry.getValue();
            if (weight < 0 || weight > 1) {
                errors.add(String.format("权重%s的值%.3f超出范围[0,1]", entry.getKey(), weight));
            }
        }
        
        // 检查权重分布合理性
        if (weights.getOrDefault("score", 0.0) < 0.2) {
            warnings.add("分数权重过低可能导致分数匹配不准确");
        }
        
        if (weights.getOrDefault("quality", 0.0) > 0.5) {
            warnings.add("质量权重过高可能导致题目选择过于保守");
        }
    }
    
    private void validateConstraints(PaperGenerationRequest request, List<String> errors, List<String> warnings) {
        // 检查题型数量与分数的一致性 - 只检查数量大于0的题型
        Map<String, Integer> typeCounts = request.getTopicTypeCounts();
        Map<String, Integer> typeScores = request.getTypeScoreMap();

        if (typeCounts != null && typeScores != null) {
            for (Map.Entry<String, Integer> entry : typeCounts.entrySet()) {
                String type = entry.getKey();
                Integer count = entry.getValue();

                // 只对数量大于0的题型检查分值配置
                if (count != null && count > 0) {
                    if (!typeScores.containsKey(type)) {
                        errors.add(String.format("题型%s缺少对应的分值配置", type));
                    } else if (typeScores.get(type) <= 0) {
                        errors.add(String.format("题型%s的分值必须大于0", type));
                    }
                }
            }
        }
        
        // 检查难度分布合理性
        Map<String, Double> difficultyDist = request.getDifficultyCriteria();
        if (difficultyDist != null) {
            double sum = difficultyDist.values().stream().mapToDouble(Double::doubleValue).sum();
            if (Math.abs(sum - 1.0) > 0.001) {
                errors.add(String.format("难度分布总和必须等于1.0，当前为%.3f", sum));
            }
        }
    }
    
    private void assessPerformanceImpact(PaperGenerationRequest request, List<String> warnings, Map<String, Object> suggestions) {
        // 评估题目数量对性能的影响
        int totalTopics = request.getTopicTypeCounts().values().stream().mapToInt(Integer::intValue).sum();
        
        if (totalTopics > 200) {
            warnings.add("题目总数较多，可能影响组卷性能");
            suggestions.put("performance_optimization", "建议启用并行评估或调整算法参数");
        }
        
        // 评估约束复杂度
        int constraintCount = 0;
        if (request.getDifficultyCriteria() != null) constraintCount++;
        if (request.getCognitiveLevelCriteria() != null) constraintCount++;
        if (request.getKnowledgePointConfigs() != null) constraintCount += request.getKnowledgePointConfigs().size();
        
        if (constraintCount > 10) {
            warnings.add("约束条件较多，可能增加求解难度");
            suggestions.put("constraint_simplification", "建议简化部分约束条件");
        }
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        private final List<String> warnings;
        private final Map<String, Object> suggestions;
        
        public ValidationResult(boolean valid, List<String> errors, List<String> warnings, Map<String, Object> suggestions) {
            this.valid = valid;
            this.errors = errors != null ? new ArrayList<>(errors) : new ArrayList<>();
            this.warnings = warnings != null ? new ArrayList<>(warnings) : new ArrayList<>();
            this.suggestions = suggestions != null ? new HashMap<>(suggestions) : new HashMap<>();
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public List<String> getErrors() {
            return errors;
        }
        
        public List<String> getWarnings() {
            return warnings;
        }
        
        public Map<String, Object> getSuggestions() {
            return suggestions;
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, Collections.emptyList(), 
                                      Collections.emptyList(), Collections.emptyMap());
        }
        
        public static ValidationResult failure(String... errors) {
            return new ValidationResult(false, Arrays.asList(errors), 
                                      Collections.emptyList(), Collections.emptyMap());
        }
    }
}
