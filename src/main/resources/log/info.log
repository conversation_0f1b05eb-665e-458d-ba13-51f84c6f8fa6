2025-07-27 14:28:30.544 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 14:28:30.619 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 9192 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-27 14:28:30.626 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-27 14:28:31.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 14:28:31.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 14:28:31.961 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-27 14:28:31.962 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-07-27 14:28:32.590 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-27 14:28:32.599 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-27 14:28:32.599 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 14:28:32.600 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-27 14:28:32.871 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 14:28:32.872 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2200 ms
2025-07-27 14:28:33.098 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-27 14:28:33.100 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-27 14:28:33.100 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-27 14:28:33.103 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-27 14:28:33.134 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:28:33.246 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-27 14:28:33.618 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.622 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.627 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.644 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:28:33.647 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.665 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.669 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.797 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.798 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:35.672 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-27 14:28:35.676 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-27 14:28:36.799 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-27 14:28:36.943 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-27 14:28:37.080 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:37.314 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:28:37.315 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:28:37.443 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 5
2025-07-27 14:28:37.513 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 14:28:37.805 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 14:28:38.174 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-27 14:28:38.461 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-27 14:28:38.484 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-27 14:28:38.977 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 14:28:39.027 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-27 14:28:39.054 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-27 14:28:39.056 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-27 14:28:39.056 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@270b8c2a]]
2025-07-27 14:28:39.056 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-27 14:28:39.070 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 9.024 seconds (JVM running for 11.549)
2025-07-27 14:28:39.075 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 2 分钟，批大小 5
2025-07-27 14:28:39.115 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377401
2025-07-27 14:28:39.131 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:28:39.231 [RMI TCP Connection(1)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 14:28:39.231 [RMI TCP Connection(1)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 14:28:39.234 [RMI TCP Connection(1)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-27 14:29:11.332 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 259
2025-07-27 14:29:11.336 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 259
2025-07-27 14:29:11.337 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-27 14:29:11.378 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2329, 日期: 2025-07-27, 过期时间: 2025-09-25 14:29:11
2025-07-27 14:29:11.378 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2329
2025-07-27 14:29:11.381 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377406
2025-07-27 14:29:11.384 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377406的重试计数
2025-07-27 14:29:11.446 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:30:39.077 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377406
2025-07-27 14:30:39.079 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-27 14:31:03.564 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377411
2025-07-27 14:31:03.565 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377411的重试计数
2025-07-27 14:31:03.579 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:32:38.000 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-27 14:32:38.000 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@270b8c2a]]
2025-07-27 14:32:38.000 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-27 14:32:38.872 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-27 14:32:38.873 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-27 14:32:38.876 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 14:32:38.881 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 14:45:59.535 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 14:45:59.609 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 8868 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-27 14:45:59.613 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-27 14:46:00.865 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 14:46:00.868 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 14:46:00.932 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-27 14:46:00.933 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-07-27 14:46:01.551 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-27 14:46:01.560 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-27 14:46:01.561 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 14:46:01.561 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-27 14:46:01.818 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 14:46:01.818 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2159 ms
2025-07-27 14:46:01.978 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-27 14:46:01.980 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-27 14:46:01.981 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-27 14:46:01.983 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-27 14:46:02.008 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:46:02.117 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-27 14:46:02.441 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.442 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.445 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.457 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:46:02.459 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.473 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:04.354 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-27 14:46:04.357 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-27 14:46:05.301 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-27 14:46:05.419 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-27 14:46:05.505 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:05.606 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:46:05.609 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:46:05.683 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 5
2025-07-27 14:46:05.716 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 14:46:05.880 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 14:46:06.083 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userStatsController' defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\controller\UserStatsController.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.edu.maizi_edu_sys.service.UserStatsCacheService' available: expected single matching bean but found 2: simpleUserStatsCacheService,userStatsCacheServiceImpl
2025-07-27 14:46:06.084 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-27 14:46:06.086 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 14:46:06.098 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 14:46:06.103 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 14:46:06.117 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 14:46:06.135 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.edu.maizi_edu_sys.controller.UserStatsController required a single bean, but 2 were found:
	- simpleUserStatsCacheService: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\SimpleUserStatsCacheServiceImpl.class]
	- userStatsCacheServiceImpl: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\UserStatsCacheServiceImpl.class]


Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

2025-07-27 14:53:24.277 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 14:53:24.355 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 14644 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-27 14:53:24.360 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-27 14:53:25.660 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 14:53:25.662 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 14:53:25.723 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-27 14:53:25.724 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-07-27 14:53:26.371 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-27 14:53:26.378 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-27 14:53:26.378 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 14:53:26.379 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-27 14:53:26.611 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 14:53:26.611 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2202 ms
2025-07-27 14:53:26.778 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-27 14:53:26.780 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-27 14:53:26.780 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-27 14:53:26.783 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-27 14:53:26.807 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:53:26.915 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-27 14:53:27.231 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.231 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.235 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:53:27.248 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:53:27.250 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:53:27.262 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.266 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:53:27.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.360 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:29.040 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-27 14:53:29.043 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-27 14:53:29.968 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-27 14:53:30.059 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-27 14:53:30.135 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:30.245 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:53:30.247 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:53:30.303 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 5
2025-07-27 14:53:30.337 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 14:53:30.504 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 14:53:30.772 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-27 14:53:30.803 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-27 14:53:31.057 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-27 14:53:31.078 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-27 14:53:31.526 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 14:53:31.579 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-27 14:53:31.596 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-27 14:53:31.598 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-27 14:53:31.598 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-27 14:53:31.598 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-27 14:53:31.611 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 7.813 seconds (JVM running for 9.725)
2025-07-27 14:53:31.616 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 2 分钟，批大小 5
2025-07-27 14:53:31.635 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377411
2025-07-27 14:53:31.641 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:53:32.644 [RMI TCP Connection(2)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 14:53:32.645 [RMI TCP Connection(2)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 14:53:32.660 [RMI TCP Connection(2)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 15 ms
2025-07-27 14:53:40.299 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-27 14:53:41.107 [http-nio-8081-exec-5] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-25T03:28:10Z. Current time: 2025-07-27T06:53:41Z, a difference of 185131105 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMzMjc2OTAsImV4cCI6MTc1MzQxNDA5MH0.RZaU9iwug__AC2iGzjCCi_G_mdBcakWOfE-dzvN-npa764Vuyft9xgRLIkZqvT1V62ldWI2baEUCOcVGwp-rnA]
2025-07-27 14:53:41.108 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-27 14:53:41.124 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:41Z, a difference of 353712124 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:41.125 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/page
2025-07-27 14:53:41.295 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:41Z, a difference of 353712295 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:41.295 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/statistics
2025-07-27 14:53:41.332 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-27 14:53:41.400 [http-nio-8081-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-27 14:53:43.474 [http-nio-8081-exec-3] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:43Z, a difference of 353714473 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:43.474 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/log
2025-07-27 14:53:54.752 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Login attempt from IP: 0:0:0:0:0:0:0:1, username: admin
2025-07-27 14:53:59.183 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Login attempt from IP: 0:0:0:0:0:0:0:1, username: adminaa
2025-07-27 14:54:03.952 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Login attempt from IP: 0:0:0:0:0:0:0:1, username: sxq
2025-07-27 14:54:04.039 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: sxq with secret (first 5): 'F9A8C...'
2025-07-27 14:54:05.074 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-27 14:54:05.196 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:54:05Z, a difference of 353736196 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:54:05.197 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/page
2025-07-27 14:54:05.198 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/info called (redirecting to /current)
2025-07-27 14:54:05.198 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-27 14:54:05.201 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-27 14:54:05.205 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-27 14:54:05.330 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:54:05Z, a difference of 353736330 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:54:05.330 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/statistics
2025-07-27 14:54:08.001 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-27 14:54:08.003 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-27 14:54:08.006 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-27 14:54:08.033 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 14:54:08.037 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户统计数据, userId: 1920280447393230872
2025-07-27 14:54:08.064 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户 1920280447393230872 统计数据: 总数=0, 通过=0, 待审核=0, 拒绝=0
2025-07-27 14:54:08.071 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的上传趋势数据，类型: day, 天数: 7
2025-07-27 14:54:08.071 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户上传趋势, userId: 1920280447393230872, days: 7
2025-07-27 14:54:08.087 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 14:54:08.105 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传0, 已通过0, 待审核0
2025-07-27 14:54:13.043 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 661
2025-07-27 14:54:13.047 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 661
2025-07-27 14:54:13.047 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 2 项修正建议，其中 2 项有效
2025-07-27 14:54:13.062 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2330, 日期: 2025-07-27, 过期时间: 2025-09-25 14:54:13
2025-07-27 14:54:13.062 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 2 条修正建议待审核，审核ID: 2330
2025-07-27 14:54:13.064 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377416
2025-07-27 14:54:13.065 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377416的重试计数
2025-07-27 14:54:13.084 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:54:17.403 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Received topic upload request with 20 topics
2025-07-27 14:54:17.415 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 批量提交题目审核, userId: 1920280447393230872, count: 20
2025-07-27 14:54:17.461 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 成功批量插入20条审核记录
2025-07-27 14:54:17.461 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 批量题目提交审核完成，共20条
2025-07-27 14:54:17.465 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - User (role=1, userId=1920280447393230872) submitted 20 topics for audit
2025-07-27 14:54:17.466 [http-nio-8081-exec-6] INFO  org.springframework.scheduling.annotation.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [correctionExecutor, clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor]
2025-07-27 14:55:09.061 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 14:55:09.061 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户统计数据, userId: 1920280447393230872
2025-07-27 14:55:09.068 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户 1920280447393230872 统计数据: 总数=20, 通过=0, 待审核=20, 拒绝=0
2025-07-27 14:55:09.086 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 20
2025-07-27 14:55:09.086 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 14:55:09.097 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 14:55:31.629 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377416
2025-07-27 14:55:31.632 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:55:54.431 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-27 14:55:54.431 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-27 14:55:54.431 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-27 14:55:54.432 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-27 14:55:54.434 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377421
2025-07-27 14:55:54.435 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377421的重试计数
2025-07-27 14:55:54.446 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:56:09.048 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 14:56:09.048 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 14:56:09.057 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 14:57:09.052 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 14:57:09.052 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 14:57:09.059 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 14:57:31.637 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377421
2025-07-27 14:57:31.639 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:57:45.365 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-27 14:57:45.365 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-27 14:57:45.365 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-27 14:57:45.365 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-27 14:57:45.366 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377426
2025-07-27 14:57:45.368 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377426的重试计数
2025-07-27 14:57:45.378 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:58:09.062 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 14:58:09.062 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 14:58:09.068 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 14:59:09.056 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 14:59:09.056 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 14:59:09.063 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 14:59:31.654 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377426
2025-07-27 14:59:31.658 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:59:55.267 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 278
2025-07-27 14:59:55.267 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 278
2025-07-27 14:59:55.267 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-27 14:59:55.271 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2331, 日期: 2025-07-27, 过期时间: 2025-09-25 14:59:55
2025-07-27 14:59:55.272 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2331
2025-07-27 14:59:55.272 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377431
2025-07-27 14:59:55.273 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377431的重试计数
2025-07-27 14:59:55.285 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 15:00:10.054 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 15:00:10.054 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 15:00:10.061 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 15:01:11.051 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 15:01:11.051 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 15:01:11.058 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 15:01:31.656 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377431
2025-07-27 15:01:31.658 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 15:01:59.663 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 106
2025-07-27 15:01:59.663 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 106
2025-07-27 15:01:59.664 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-27 15:01:59.668 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2332, 日期: 2025-07-27, 过期时间: 2025-09-25 15:01:59
2025-07-27 15:01:59.668 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2332
2025-07-27 15:01:59.669 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377436
2025-07-27 15:01:59.670 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377436的重试计数
2025-07-27 15:01:59.681 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 15:02:12.062 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 15:02:12.062 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 15:02:12.077 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 15:03:13.064 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 15:03:13.065 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 15:03:13.072 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 15:03:31.662 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377436
2025-07-27 15:03:31.665 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 15:03:55.961 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-27 15:03:55.961 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-27 15:03:55.961 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-27 15:03:55.961 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-27 15:03:55.963 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377441
2025-07-27 15:03:55.964 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377441的重试计数
2025-07-27 15:03:55.975 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 15:04:14.064 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-27 15:04:14.065 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-27 15:04:14.080 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-27 15:05:31.677 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377441
2025-07-27 15:05:31.679 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 15:05:55.518 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 94
2025-07-27 15:05:55.519 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 94
2025-07-27 15:05:55.519 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-27 15:05:55.523 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2333, 日期: 2025-07-27, 过期时间: 2025-09-25 15:05:55
2025-07-27 15:05:55.523 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2333
2025-07-27 15:05:55.524 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377446
2025-07-27 15:05:55.525 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377446的重试计数
2025-07-27 15:05:55.536 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 15:07:26.494 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 15:07:26.579 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 19832 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-27 15:07:26.585 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-27 15:07:28.292 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 15:07:28.297 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 15:07:28.377 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-27 15:07:28.379 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-07-27 15:07:29.204 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-27 15:07:29.214 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-27 15:07:29.214 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 15:07:29.214 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-27 15:07:29.533 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 15:07:29.533 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2896 ms
2025-07-27 15:07:29.754 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-27 15:07:29.756 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-27 15:07:29.756 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-27 15:07:29.760 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-27 15:07:29.788 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:07:29.896 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-27 15:07:30.324 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.325 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.331 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:07:30.359 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:07:30.362 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:07:30.377 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.378 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.379 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.382 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:07:30.522 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.522 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.523 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.523 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.524 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.524 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.525 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.525 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:31.692 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377446
2025-07-27 15:07:31.694 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 15:07:32.660 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-27 15:07:32.665 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-27 15:07:33.902 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-27 15:07:34.013 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-27 15:07:34.108 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:34.283 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:07:34.284 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 15:07:34.367 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 5
2025-07-27 15:07:34.412 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 15:07:34.650 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 15:07:35.065 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-27 15:07:35.112 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-27 15:07:35.429 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-27 15:07:35.455 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-27 15:07:36.015 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 15:07:36.088 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-27 15:07:36.098 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-27 15:07:36.111 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-27 15:07:36.113 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-27 15:07:36.117 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 15:07:36.132 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 15:07:36.139 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8081"]
2025-07-27 15:07:36.139 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 15:07:36.144 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8081"]
2025-07-27 15:07:36.144 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8081"]
2025-07-27 15:07:36.163 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 15:07:36.188 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-27 15:08:02.742 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-27 15:08:02.742 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-27 15:08:02.742 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-27 15:08:02.742 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-27 15:08:02.743 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377451
2025-07-27 15:08:02.744 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377451的重试计数
2025-07-27 15:08:02.755 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
