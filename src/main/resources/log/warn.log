2025-07-27 14:28:33.134 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:28:33.618 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.622 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.627 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.644 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:28:33.647 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.665 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.669 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.797 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.798 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:37.080 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:37.314 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:28:37.315 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:46:02.008 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:46:02.441 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.442 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.445 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.457 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:46:02.459 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.473 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:05.505 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:05.606 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:46:05.609 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:46:06.083 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userStatsController' defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\controller\UserStatsController.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.edu.maizi_edu_sys.service.UserStatsCacheService' available: expected single matching bean but found 2: simpleUserStatsCacheService,userStatsCacheServiceImpl
2025-07-27 14:46:06.135 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.edu.maizi_edu_sys.controller.UserStatsController required a single bean, but 2 were found:
	- simpleUserStatsCacheService: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\SimpleUserStatsCacheServiceImpl.class]
	- userStatsCacheServiceImpl: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\UserStatsCacheServiceImpl.class]


Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

2025-07-27 14:53:26.807 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:53:27.231 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.231 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.235 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:53:27.248 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:53:27.250 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:53:27.262 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.266 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:53:27.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.360 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:30.135 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:30.245 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:53:30.247 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:53:41.107 [http-nio-8081-exec-5] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-25T03:28:10Z. Current time: 2025-07-27T06:53:41Z, a difference of 185131105 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.***************************************************************.RZaU9iwug__AC2iGzjCCi_G_mdBcakWOfE-dzvN-npa764Vuyft9xgRLIkZqvT1V62ldWI2baEUCOcVGwp-rnA]
2025-07-27 14:53:41.108 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-27 14:53:41.124 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:41Z, a difference of 353712124 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:41.125 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/page
2025-07-27 14:53:41.295 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:41Z, a difference of 353712295 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:41.295 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/statistics
2025-07-27 14:53:41.332 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-27 14:53:41.400 [http-nio-8081-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-27 14:53:43.474 [http-nio-8081-exec-3] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:43Z, a difference of 353714473 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:43.474 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/log
2025-07-27 14:54:05.196 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:54:05Z, a difference of 353736196 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:54:05.197 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/page
2025-07-27 14:54:05.330 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:54:05Z, a difference of 353736330 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:54:05.330 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/statistics
2025-07-27 14:55:09.086 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 20
2025-07-27 15:07:29.788 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:07:30.324 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.325 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.331 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:07:30.359 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:07:30.362 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:07:30.377 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.378 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.379 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.382 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:07:30.522 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.522 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.523 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.523 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.524 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.524 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.525 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.525 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:34.108 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:34.283 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:07:34.284 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 15:07:36.098 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-27 15:07:36.188 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

