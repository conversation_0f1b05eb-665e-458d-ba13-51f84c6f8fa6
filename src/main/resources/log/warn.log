2025-07-27 14:28:33.134 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:28:33.618 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.622 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.627 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.644 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:28:33.647 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.665 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.669 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.797 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.798 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:37.080 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:37.314 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:28:37.315 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:46:02.008 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:46:02.441 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.442 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.445 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.457 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:46:02.459 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.473 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:05.505 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:05.606 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:46:05.609 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:46:06.083 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userStatsController' defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\controller\UserStatsController.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.edu.maizi_edu_sys.service.UserStatsCacheService' available: expected single matching bean but found 2: simpleUserStatsCacheService,userStatsCacheServiceImpl
2025-07-27 14:46:06.135 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.edu.maizi_edu_sys.controller.UserStatsController required a single bean, but 2 were found:
	- simpleUserStatsCacheService: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\SimpleUserStatsCacheServiceImpl.class]
	- userStatsCacheServiceImpl: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\UserStatsCacheServiceImpl.class]


Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

2025-07-27 14:53:26.807 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:53:27.231 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.231 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.235 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:53:27.248 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:53:27.250 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:53:27.262 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.266 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:53:27.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:27.360 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:30.135 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:53:30.245 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:53:30.247 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:53:41.107 [http-nio-8081-exec-5] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-25T03:28:10Z. Current time: 2025-07-27T06:53:41Z, a difference of 185131105 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.***************************************************************.RZaU9iwug__AC2iGzjCCi_G_mdBcakWOfE-dzvN-npa764Vuyft9xgRLIkZqvT1V62ldWI2baEUCOcVGwp-rnA]
2025-07-27 14:53:41.108 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-27 14:53:41.124 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:41Z, a difference of 353712124 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:41.125 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/page
2025-07-27 14:53:41.295 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:41Z, a difference of 353712295 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:41.295 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/statistics
2025-07-27 14:53:41.332 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-27 14:53:41.400 [http-nio-8081-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-27 14:53:43.474 [http-nio-8081-exec-3] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:53:43Z, a difference of 353714473 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:53:43.474 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/log
2025-07-27 14:54:05.196 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:54:05Z, a difference of 353736196 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:54:05.197 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/page
2025-07-27 14:54:05.330 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-23T04:38:29Z. Current time: 2025-07-27T06:54:05Z, a difference of 353736330 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTMxNTkxMDksImV4cCI6MTc1MzI0NTUwOX0.53iHah2W83aXsWlkCNr6WNqeNrmT7KSufUbcAVj2RD7wpJPYiClhyz55yikDV2yZ2IY3HCUNpEKeneFZXDGrXw]
2025-07-27 14:54:05.330 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/activities/statistics
2025-07-27 14:55:09.086 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 20
2025-07-27 15:07:29.788 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:07:30.324 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.325 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.331 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:07:30.359 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:07:30.362 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:07:30.377 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.378 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.379 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.382 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:07:30.522 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.522 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.523 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.523 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.524 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.524 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.525 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:30.525 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:34.108 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:07:34.283 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:07:34.284 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 15:07:36.098 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-27 15:07:36.188 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-27 15:12:20.494 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:12:20.932 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:20.932 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:20.936 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:12:20.953 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:12:20.956 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:12:20.967 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:20.968 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:20.968 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:20.971 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:12:21.062 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.062 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.062 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.063 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.063 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.063 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.064 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:21.064 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:23.896 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:12:24.016 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:12:24.016 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 15:12:33.868 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 20
2025-07-27 15:15:06.050 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:06.050 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:06.052 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:06.052 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:06.053 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:06.053 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:11.043 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:11.045 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:11.045 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:11.045 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:11.046 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:11.046 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:16.047 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:16.047 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:16.048 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:16.048 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:16.048 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:16.049 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:21.040 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:21.041 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:21.041 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:21.042 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:21.042 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:21.042 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:26.048 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:26.048 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:26.048 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:26.049 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:26.049 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:26.050 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:31.043 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:31.044 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:31.044 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:31.044 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:31.044 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:31.044 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:36.055 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:36.055 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:36.056 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:36.056 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:36.056 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:36.057 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:37.444 [http-nio-8081-exec-1] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/preview: Request method 'POST' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1265) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1047) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
2025-07-27 15:15:38.518 [http-nio-8081-exec-8] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/preview: Request method 'POST' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1265) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1047) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
2025-07-27 15:15:40.041 [http-nio-8081-exec-7] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/preview: Request method 'POST' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1265) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1047) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
2025-07-27 15:15:41.053 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:41.053 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:41.053 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:41.054 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:41.054 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:41.056 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:41.195 [http-nio-8081-exec-3] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/preview: Request method 'POST' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1265) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1047) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
2025-07-27 15:15:42.719 [http-nio-8081-exec-4] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/preview: Request method 'POST' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1265) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1047) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
2025-07-27 15:15:45.115 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:15:45.115 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:15:45.160 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:15:45.160 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:15:46.041 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:46.042 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:46.042 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:46.044 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:46.044 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:46.044 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:51.053 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:51.053 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:51.053 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:51.054 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:51.054 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:51.055 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:15:56.055 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:15:56.056 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:15:56.056 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:15:56.057 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:15:56.057 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:15:56.058 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:01.055 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:01.055 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:01.055 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:01.056 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:01.056 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:01.058 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:05.776 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:05.776 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:05.819 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:05.819 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:06.048 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:06.048 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:06.048 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:06.049 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:06.050 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:06.053 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:11.049 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:11.049 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:11.050 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:11.050 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:11.050 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:11.051 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:16.057 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:16.057 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:16.058 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:16.059 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:16.060 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:16.060 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:21.044 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:21.044 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:21.045 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:21.045 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:21.046 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:21.046 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:23.636 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:23.636 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:23.677 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:23.677 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.util.TopicTypeMapper - 未识别的题型格式: 'hard', 返回默认单选题
2025-07-27 15:16:26.059 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:26.059 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:26.059 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:26.059 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:26.062 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:26.062 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:31.045 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:31.046 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:31.046 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:31.046 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:31.046 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:31.047 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:36.046 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:36.047 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:36.047 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:36.047 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:36.048 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:36.048 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:41.054 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:41.054 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:41.054 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:41.054 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:41.055 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:41.055 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:46.056 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:46.056 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:46.056 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:46.056 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:46.058 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:46.058 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:51.054 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:51.054 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:51.054 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:51.054 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:51.054 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:51.054 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:16:56.048 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:16:56.048 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:16:56.048 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:16:56.049 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:16:56.049 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:16:56.049 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:01.043 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:01.044 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:01.044 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:01.044 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:01.044 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:01.044 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:06.040 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:06.040 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:06.040 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:06.040 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:06.041 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:06.041 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:11.050 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:11.050 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:11.050 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:11.050 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:11.051 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:11.051 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:16.042 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:16.043 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:16.043 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:16.043 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:16.043 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:16.044 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:21.041 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:21.041 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:21.041 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:21.041 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:21.041 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:21.042 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:26.045 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:26.045 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:26.045 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:26.045 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:26.046 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:26.046 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:31.041 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:31.041 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:31.041 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:31.041 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:31.041 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:31.042 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:36.041 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:36.041 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:36.042 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:36.042 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:36.043 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:36.044 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:41.042 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:41.042 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:41.042 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:41.042 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:41.042 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:41.042 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:46.043 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:46.045 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:46.045 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:46.045 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:46.045 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:46.047 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:51.045 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:51.045 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:51.046 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:51.046 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:51.046 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:51.046 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:17:56.052 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:17:56.052 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:17:56.052 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:17:56.053 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:17:56.053 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:17:56.053 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:18:01.047 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:18:01.047 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:18:01.047 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:18:01.047 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:18:01.048 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:18:01.048 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:18:06.049 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:18:06.050 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:18:06.050 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:18:06.050 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:18:06.051 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:18:06.051 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:18:11.055 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:18:11.055 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:18:11.055 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:18:11.056 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:18:11.056 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:18:11.056 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:18:16.047 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:18:16.048 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:18:16.049 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:18:16.049 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:18:16.049 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:18:16.049 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:18:20.055 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-27 15:18:20.055 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:18:20.056 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-27 15:18:20.056 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-27 15:18:20.056 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-27 15:18:20.057 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-27 15:18:20.703 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor
2025-07-27 15:26:50.534 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-27 15:31:43.573 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:31:43.972 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:43.973 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:43.977 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:31:43.990 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:31:43.992 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:31:44.005 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.005 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.006 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.009 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:31:44.109 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.109 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.109 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.110 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.110 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.110 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.111 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:44.111 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:46.873 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:31:46.985 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:31:46.986 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 15:47:20.838 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:47:21.230 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.230 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.235 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:47:21.249 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:47:21.252 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:47:21.263 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.264 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.264 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.268 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:47:21.356 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.357 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.358 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:21.359 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:24.140 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:47:24.235 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:47:24.235 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 15:54:05.438 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 15:54:05.840 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.841 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.844 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:54:05.857 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 15:54:05.858 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 15:54:05.869 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.870 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.870 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.873 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 15:54:05.965 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.966 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.966 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.967 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.967 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.967 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.968 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:05.968 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:08.700 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 15:54:08.816 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 15:54:08.816 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
