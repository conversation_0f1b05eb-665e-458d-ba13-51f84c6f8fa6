<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <!-- 日志文件路径 -->
        <Property name="LOG_HOME">src/main/resources/log</Property>
        <!-- 日志格式 -->
        <Property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 文件输出 - 所有日志 -->
        <RollingFile name="RollingFile"
                     fileName="${LOG_HOME}/app.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/app-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>

        <!-- 错误日志文件 -->
        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/error-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- INFO 级别日志文件 -->
        <RollingFile name="InfoFile" fileName="${LOG_HOME}/info.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/info-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- DEBUG 级别日志文件 -->
        <RollingFile name="DebugFile" fileName="${LOG_HOME}/debug.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/debug-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- WARN 级别日志文件 -->
        <RollingFile name="WarnFile" fileName="${LOG_HOME}/warn.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/warn-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- 组卷系统 INFO 级别日志 -->
        <RollingFile name="PaperGenInfoFile" fileName="${LOG_HOME}/paper-info.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/paper-info-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- 组卷系统 DEBUG 级别日志 -->
        <RollingFile name="PaperGenDebugFile" fileName="${LOG_HOME}/paper-debug.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/paper-debug-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- 组卷系统 WARN 级别日志 -->
        <RollingFile name="PaperGenWarnFile" fileName="${LOG_HOME}/paper-warn.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM-dd}/paper-warn-%i.log.gz">
            <PatternLayout pattern="${PATTERN}"/>
            <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- Spring Boot 相关日志 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="WarnFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- Hibernate 相关日志 -->
        <Logger name="org.hibernate" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="WarnFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- MyBatis 相关日志 -->
        <Logger name="org.mybatis" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="WarnFile"/>
        </Logger>

        <!-- 应用程序日志 -->
        <Logger name="com.edu.maizi_edu_sys" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="DebugFile"/>
            <AppenderRef ref="WarnFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- 专用日志器：组卷日志 - PaperGenerationServiceImpl -->
        <Logger name="com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="PaperGenInfoFile"/>
            <AppenderRef ref="PaperGenDebugFile"/>
            <AppenderRef ref="PaperGenWarnFile"/>
        </Logger>

        <!-- 专用日志器：组卷引擎日志 -->
        <Logger name="com.edu.maizi_edu_sys.service.engine" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="PaperGenInfoFile"/>
            <AppenderRef ref="PaperGenDebugFile"/>
            <AppenderRef ref="PaperGenWarnFile"/>
        </Logger>

        <!-- 根日志器 -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="DebugFile"/>
            <AppenderRef ref="WarnFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
    </Loggers>
</Configuration>

