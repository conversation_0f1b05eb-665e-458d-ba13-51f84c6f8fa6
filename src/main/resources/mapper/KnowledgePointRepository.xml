<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.repository.KnowledgePointRepository">

    <!-- 获取有题目的知识点ID列表 -->
    <select id="findKnowledgePointsWithQuestions" resultType="java.lang.Long">
        SELECT DISTINCT kp.knowledge_id
        FROM wm_knowledge kp
        JOIN topic_bak t ON kp.knowledge_id = t.know_id
        WHERE kp.knowledge_id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = 0
    </select>

    <!-- 获取知识点题目数量统计 -->
    <select id="countQuestionsByKnowledgePoints" resultType="java.util.Map">
        SELECT
            kp.knowledge_id as knowledgeId,
            COUNT(t.id) as questionCount
        FROM
            wm_knowledge kp
        LEFT JOIN
            topic_bak t ON kp.knowledge_id = t.know_id
        WHERE
            kp.knowledge_id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = 0
        GROUP BY
            kp.knowledge_id
    </select>

    <!-- 获取有简答题的知识点ID列表 -->
    <select id="findKnowledgePointsWithShortAnswerQuestions" resultType="java.lang.Long">
        SELECT DISTINCT kp.knowledge_id
        FROM wm_knowledge kp
        JOIN topic_bak t ON kp.knowledge_id = t.know_id
        WHERE kp.knowledge_id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = 0
        AND t.type = 'short'
    </select>

    <!-- 获取知识点的题目类型统计 -->
    <select id="countQuestionTypesByKnowledgePoints" resultType="java.util.Map">
        SELECT
            kp.knowledge_id as knowledgeId,
            t.type as questionType,
            COUNT(t.id) as questionCount
        FROM
            wm_knowledge kp
        JOIN
            topic_bak t ON kp.knowledge_id = t.know_id
        WHERE
            kp.knowledge_id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND kp.is_deleted = 0
        GROUP BY
            kp.knowledge_id, t.type
    </select>

</mapper>
