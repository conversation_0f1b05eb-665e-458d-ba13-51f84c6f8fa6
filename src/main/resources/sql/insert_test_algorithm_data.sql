-- 插入测试算法执行数据
-- 用于测试遗传算法监控页面功能

-- 清理现有测试数据
DELETE FROM algorithm_generation_data WHERE execution_id IN (SELECT id FROM algorithm_execution WHERE paper_id LIKE 'test_%');
DELETE FROM fitness_dimension_data WHERE execution_id IN (SELECT id FROM algorithm_execution WHERE paper_id LIKE 'test_%');
DELETE FROM algorithm_execution WHERE paper_id LIKE 'test_%';

-- 插入测试算法执行记录
INSERT INTO algorithm_execution (
    paper_id, paper_name, algorithm_type, status, start_time, end_time,
    max_generations, target_fitness, population_size, mutation_rate, crossover_rate,
    selection_method, created_at, updated_at
) VALUES 
('test_paper_001', '数学期末考试 #001', 'genetic', 'completed', 
 DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR),
 100, 0.95, 50, 0.1, 0.8, 'tournament', NOW(), NOW()),

('test_paper_002', '物理模拟考试 #002', 'genetic', 'running', 
 DATE_SUB(NOW(), INTERVAL 30 MINUTE), NULL,
 150, 0.90, 80, 0.15, 0.75, 'roulette', NOW(), NOW()),

('test_paper_003', '化学单元测试 #003', 'genetic', 'completed', 
 DATE_SUB(NOW(), INTERVAL 4 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR),
 80, 0.85, 60, 0.12, 0.85, 'tournament', NOW(), NOW()),

('test_paper_004', '英语综合测试 #004', 'genetic', 'failed', 
 DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 5 HOUR),
 120, 0.92, 70, 0.08, 0.9, 'tournament', NOW(), NOW()),

('test_paper_005', '历史期中考试 #005', 'genetic', 'paused', 
 DATE_SUB(NOW(), INTERVAL 1 HOUR), NULL,
 200, 0.88, 100, 0.2, 0.7, 'roulette', NOW(), NOW());

-- 获取插入的执行记录ID
SET @exec_id_1 = (SELECT id FROM algorithm_execution WHERE paper_id = 'test_paper_001');
SET @exec_id_2 = (SELECT id FROM algorithm_execution WHERE paper_id = 'test_paper_002');
SET @exec_id_3 = (SELECT id FROM algorithm_execution WHERE paper_id = 'test_paper_003');
SET @exec_id_4 = (SELECT id FROM algorithm_execution WHERE paper_id = 'test_paper_004');
SET @exec_id_5 = (SELECT id FROM algorithm_execution WHERE paper_id = 'test_paper_005');

-- 为第一个试卷插入代数数据（已完成）
INSERT INTO algorithm_generation_data (
    execution_id, generation, best_fitness, avg_fitness, worst_fitness,
    diversity_index, convergence_speed, constraint_violations, fitness_std_dev, recorded_at
) VALUES 
(@exec_id_1, 1, 0.45, 0.32, 0.18, 0.95, 0.02, 5, 0.12, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
(@exec_id_1, 10, 0.62, 0.48, 0.25, 0.88, 0.018, 3, 0.15, DATE_SUB(NOW(), INTERVAL 110 MINUTE)),
(@exec_id_1, 25, 0.78, 0.65, 0.42, 0.75, 0.015, 2, 0.18, DATE_SUB(NOW(), INTERVAL 95 MINUTE)),
(@exec_id_1, 50, 0.89, 0.78, 0.58, 0.62, 0.012, 1, 0.16, DATE_SUB(NOW(), INTERVAL 80 MINUTE)),
(@exec_id_1, 75, 0.94, 0.86, 0.68, 0.45, 0.008, 0, 0.13, DATE_SUB(NOW(), INTERVAL 65 MINUTE)),
(@exec_id_1, 95, 0.96, 0.91, 0.78, 0.32, 0.005, 0, 0.09, DATE_SUB(NOW(), INTERVAL 62 MINUTE));

-- 为第二个试卷插入代数数据（运行中）
INSERT INTO algorithm_generation_data (
    execution_id, generation, best_fitness, avg_fitness, worst_fitness,
    diversity_index, convergence_speed, constraint_violations, fitness_std_dev, recorded_at
) VALUES 
(@exec_id_2, 1, 0.38, 0.28, 0.15, 0.92, 0.025, 8, 0.14, DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(@exec_id_2, 15, 0.58, 0.42, 0.22, 0.85, 0.02, 4, 0.17, DATE_SUB(NOW(), INTERVAL 25 MINUTE)),
(@exec_id_2, 35, 0.72, 0.58, 0.38, 0.78, 0.016, 2, 0.19, DATE_SUB(NOW(), INTERVAL 15 MINUTE)),
(@exec_id_2, 55, 0.84, 0.71, 0.52, 0.65, 0.013, 1, 0.16, DATE_SUB(NOW(), INTERVAL 8 MINUTE)),
(@exec_id_2, 68, 0.88, 0.76, 0.58, 0.58, 0.011, 1, 0.14, DATE_SUB(NOW(), INTERVAL 2 MINUTE));

-- 为第三个试卷插入代数数据（已完成）
INSERT INTO algorithm_generation_data (
    execution_id, generation, best_fitness, avg_fitness, worst_fitness,
    diversity_index, convergence_speed, constraint_violations, fitness_std_dev, recorded_at
) VALUES 
(@exec_id_3, 1, 0.52, 0.38, 0.22, 0.88, 0.03, 6, 0.16, DATE_SUB(NOW(), INTERVAL 4 HOUR)),
(@exec_id_3, 20, 0.71, 0.58, 0.35, 0.82, 0.022, 3, 0.18, DATE_SUB(NOW(), INTERVAL 220 MINUTE)),
(@exec_id_3, 45, 0.86, 0.74, 0.55, 0.68, 0.018, 1, 0.15, DATE_SUB(NOW(), INTERVAL 200 MINUTE)),
(@exec_id_3, 65, 0.92, 0.84, 0.68, 0.52, 0.014, 0, 0.12, DATE_SUB(NOW(), INTERVAL 185 MINUTE)),
(@exec_id_3, 78, 0.95, 0.89, 0.75, 0.38, 0.009, 0, 0.08, DATE_SUB(NOW(), INTERVAL 182 MINUTE));

-- 为第四个试卷插入少量代数数据（失败）
INSERT INTO algorithm_generation_data (
    execution_id, generation, best_fitness, avg_fitness, worst_fitness,
    diversity_index, convergence_speed, constraint_violations, fitness_std_dev, recorded_at
) VALUES 
(@exec_id_4, 1, 0.35, 0.25, 0.12, 0.95, 0.02, 12, 0.18, DATE_SUB(NOW(), INTERVAL 6 HOUR)),
(@exec_id_4, 8, 0.42, 0.31, 0.18, 0.92, 0.015, 8, 0.16, DATE_SUB(NOW(), INTERVAL 350 MINUTE)),
(@exec_id_4, 15, 0.38, 0.28, 0.15, 0.89, 0.01, 15, 0.14, DATE_SUB(NOW(), INTERVAL 340 MINUTE));

-- 为第五个试卷插入代数数据（暂停）
INSERT INTO algorithm_generation_data (
    execution_id, generation, best_fitness, avg_fitness, worst_fitness,
    diversity_index, convergence_speed, constraint_violations, fitness_std_dev, recorded_at
) VALUES 
(@exec_id_5, 1, 0.41, 0.29, 0.16, 0.94, 0.028, 7, 0.15, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(@exec_id_5, 12, 0.59, 0.44, 0.28, 0.87, 0.024, 4, 0.17, DATE_SUB(NOW(), INTERVAL 50 MINUTE)),
(@exec_id_5, 28, 0.74, 0.61, 0.42, 0.79, 0.019, 2, 0.18, DATE_SUB(NOW(), INTERVAL 35 MINUTE)),
(@exec_id_5, 42, 0.82, 0.69, 0.51, 0.71, 0.016, 1, 0.16, DATE_SUB(NOW(), INTERVAL 20 MINUTE));

-- 插入适应度维度数据
INSERT INTO fitness_dimension_data (
    execution_id, dimension_name, dimension_value, fitness_value, 
    generation, weight, recorded_at
) VALUES 
-- 试卷1的适应度维度数据
(@exec_id_1, '难度分布', 0.92, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '知识点覆盖', 0.88, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '题型平衡', 0.94, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '时间分配', 0.90, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '分值分布', 0.87, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '认知层次', 0.91, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '重复度控制', 0.95, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),
(@exec_id_1, '综合质量', 0.93, 0.96, 95, 1.0, DATE_SUB(NOW(), INTERVAL 62 MINUTE)),

-- 试卷2的适应度维度数据
(@exec_id_2, '难度分布', 0.85, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '知识点覆盖', 0.82, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '题型平衡', 0.89, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '时间分配', 0.87, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '分值分布', 0.84, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '认知层次', 0.86, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '重复度控制', 0.91, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(@exec_id_2, '综合质量', 0.88, 0.88, 68, 1.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE));

-- 显示插入结果
SELECT '插入完成！测试数据统计：' as message;
SELECT 
    COUNT(*) as execution_count,
    SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
    SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused_count
FROM algorithm_execution 
WHERE paper_id LIKE 'test_%';

SELECT 
    COUNT(*) as generation_data_count,
    COUNT(DISTINCT execution_id) as execution_with_data_count
FROM algorithm_generation_data 
WHERE execution_id IN (SELECT id FROM algorithm_execution WHERE paper_id LIKE 'test_%');

SELECT 
    COUNT(*) as fitness_dimension_count,
    COUNT(DISTINCT execution_id) as execution_with_fitness_count
FROM fitness_dimension_data 
WHERE execution_id IN (SELECT id FROM algorithm_execution WHERE paper_id LIKE 'test_%');
