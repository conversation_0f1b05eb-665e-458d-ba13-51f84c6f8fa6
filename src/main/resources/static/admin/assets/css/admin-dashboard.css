/* 管理员仪表板样式 */

/* 仪表板布局 */
.dashboard-container {
    padding: 1.5rem;
}

/* 统计卡片 */
.stats-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stats-card .icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.stats-card .icon.primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stats-card .icon.success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stats-card .icon.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stats-card .icon.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.stats-card .value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
    margin-bottom: 0.5rem;
}

.stats-card .label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 图表容器 */
.chart-container {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 0.5rem;
}

/* 表格样式 */
.data-table {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.data-table .table {
    margin-bottom: 0;
}

.data-table .table thead th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem 0.75rem;
}

.data-table .table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
}

.data-table .table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 状态徽章 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-btn.edit {
    background-color: #007bff;
    color: white;
}

.action-btn.delete {
    background-color: #dc3545;
    color: white;
}

.action-btn.view {
    background-color: #28a745;
    color: white;
}

/* 筛选器 */
.filters {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

/* 分页 */
.pagination-wrapper {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-top: 1px solid #e3e6f0;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .filters {
        padding: 1rem;
    }
}
