/* 遗传算法监控页面增强样式 */

/* 表格增强样式 */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
}

.table th.sortable:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.table th.sortable i {
    margin-left: 5px;
    opacity: 0.6;
}

.table th.sortable:hover i {
    opacity: 1;
}

/* 统计卡片样式 */
.stat-item {
    padding: 10px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 进度条样式 */
.progress {
    background-color: #e9ecef;
    border-radius: 0.375rem;
}

.progress-bar {
    background: linear-gradient(45deg, #007bff, #0056b3);
    transition: width 0.6s ease;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 模态框增强样式 */
.modal-xl {
    max-width: 1200px;
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 1.5rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 表格响应式样式 */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 筛选区域样式 */
.form-select, .form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 400px;
    padding: 1rem;
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 加载状态样式 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #212529;
    color: white;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 数据状态指示器 */
.data-status {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.data-status.complete {
    background-color: #28a745;
}

.data-status.partial {
    background-color: #ffc107;
}

.data-status.missing {
    background-color: #dc3545;
}

/* 表格紧凑模式 */
.table-sm th,
.table-sm td {
    padding: 0.3rem;
    font-size: 0.875rem;
}

/* 高亮行样式 */
.table tbody tr.highlight {
    background-color: rgba(255, 193, 7, 0.25);
    animation: highlight 2s ease-out;
}

@keyframes highlight {
    0% { background-color: rgba(255, 193, 7, 0.5); }
    100% { background-color: rgba(255, 193, 7, 0.25); }
}

/* 搜索高亮 */
.search-highlight {
    background-color: yellow;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.running::before {
    background-color: #007bff;
    animation: pulse 2s infinite;
}

.status-indicator.completed::before {
    background-color: #28a745;
}

.status-indicator.failed::before {
    background-color: #dc3545;
}

.status-indicator.paused::before {
    background-color: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
