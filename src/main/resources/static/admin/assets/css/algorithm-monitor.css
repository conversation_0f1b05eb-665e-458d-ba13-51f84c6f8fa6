/* 遗传算法监控页面样式 */

/* 页面布局 */
.algorithm-monitor {
    padding: 1.5rem;
}

/* 页面标题 */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1rem;
}

/* 概览卡片 */
.overview-cards {
    margin-bottom: 2rem;
}

.overview-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s, box-shadow 0.2s;
    height: 100%;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.overview-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: 1rem;
}

.overview-card .card-value {
    font-size: 1.75rem;
    font-weight: bold;
    color: #495057;
    margin-bottom: 0.25rem;
}

.overview-card .card-label {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.overview-card .card-change {
    font-size: 0.75rem;
    font-weight: 600;
}

.overview-card .card-change.positive {
    color: #28a745;
}

.overview-card .card-change.negative {
    color: #dc3545;
}

/* 控制面板 */
.control-panel {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 2rem;
}

.control-panel .panel-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
}

.control-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.control-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn:hover {
    transform: translateY(-1px);
}

.control-btn.primary {
    background-color: #007bff;
    color: white;
}

.control-btn.success {
    background-color: #28a745;
    color: white;
}

.control-btn.warning {
    background-color: #ffc107;
    color: #212529;
}

.control-btn.danger {
    background-color: #dc3545;
    color: white;
}

.control-btn.secondary {
    background-color: #6c757d;
    color: white;
}

/* 筛选器区域 */
.filters-section {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 2rem;
}

.filters-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 图表区域 */
.charts-section {
    margin-bottom: 2rem;
}

.chart-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    overflow: hidden;
    height: 100%;
}

.chart-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fa;
}

.chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-body {
    padding: 1rem;
}

.chart-container {
    width: 100%;
    height: 350px;
}

/* 表格区域 */
.table-section {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    overflow: hidden;
}

.table-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fa;
    display: flex;
    justify-content: between;
    align-items: center;
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

/* 实时监控指示器 */
.realtime-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background-color: #d4edda;
    color: #155724;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.realtime-indicator.active::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.realtime-indicator.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.realtime-indicator.inactive::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #dc3545;
    border-radius: 50%;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .algorithm-monitor {
        padding: 1rem;
    }
    
    .overview-cards .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .control-buttons {
        justify-content: center;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
