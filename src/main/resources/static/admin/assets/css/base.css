/* 管理员基础样式 */

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #007bff, #0056b3);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    color: white !important;
}

.brand-text {
    font-size: 1.25rem;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #343a40;
    min-height: calc(100vh - 56px);
    padding-top: 1rem;
}

.sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.25rem 0.5rem;
    transition: all 0.2s;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: #495057;
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 0.5rem;
}

/* 主内容区域 */
main {
    padding-top: 76px; /* 导航栏高度 + 间距 */
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 边框颜色样式 */
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}

.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}

.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 0.25rem solid #ffc107 !important;
}

/* 文本颜色 */
.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* 字体大小 */
.text-xs {
    font-size: 0.75rem;
}

/* 通知下拉菜单 */
.notification-dropdown {
    min-width: 280px;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.notification-item:last-child {
    border-bottom: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
    main {
        padding-top: 66px;
    }
    
    .sidebar {
        min-height: auto;
    }
}
