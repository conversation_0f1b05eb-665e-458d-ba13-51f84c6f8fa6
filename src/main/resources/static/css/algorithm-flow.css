/* 智能组卷流程介绍样式 */

.algorithm-flow-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 渐变背景 */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 流程介绍区域 */
.flow-intro {
    border-bottom: 3px solid #e9ecef;
}

.flow-intro h3 {
    color: #495057;
    font-weight: 600;
}

.flow-intro .lead {
    font-size: 1.1rem;
    color: #6c757d;
}

/* 流程步骤 */
.flow-steps {
    background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
}

.flow-step {
    position: relative;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.flow-step:nth-child(1) { animation-delay: 0.2s; }
.flow-step:nth-child(2) { animation-delay: 0.4s; }
.flow-step:nth-child(3) { animation-delay: 0.6s; }
.flow-step:nth-child(4) { animation-delay: 0.8s; }
.flow-step:nth-child(5) { animation-delay: 1.0s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 步骤编号和图标 */
.step-number {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.step-number .number {
    position: absolute;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    z-index: 2;
}

.step-number .step-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.3);
    font-size: 2rem;
    z-index: 1;
}

/* 步骤内容 */
.step-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.step-description {
    color: #6c757d;
    font-size: 1.05rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.step-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #495057;
    font-weight: 500;
}

.detail-item i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* 可视化卡片 */
.step-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.visual-card {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.visual-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
}

.visual-card .card-body {
    padding: 1.5rem;
}

/* 输入分析卡片 */
.input-analysis .analysis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.input-analysis .analysis-item:last-child {
    border-bottom: none;
}

.input-analysis .label {
    color: #6c757d;
    font-weight: 500;
}

.input-analysis .value {
    color: #495057;
    font-weight: 600;
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

/* 数据库搜索卡片 */
.database-search .progress-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.database-search .progress-item span:first-child {
    min-width: 80px;
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
}

.database-search .progress {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 10px;
}

.database-search .progress-bar {
    border-radius: 10px;
    transition: width 2s ease;
}

.database-search .count {
    min-width: 60px;
    text-align: right;
    color: #6c757d;
    font-weight: 600;
    font-size: 0.9rem;
}

/* AI优化卡片 */
.ai-optimization .round-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.ai-optimization .round {
    min-width: 60px;
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
}

.ai-optimization .score-bar {
    flex: 1;
    height: 25px;
    background: #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.ai-optimization .score {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    transition: width 1.5s ease;
    border-radius: 15px;
}

.ai-optimization .round-item.active .score {
    background: linear-gradient(90deg, #fd7e14 0%, #e83e8c 50%, #6f42c1 100%);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.ai-optimization .optimization-status {
    text-align: center;
    margin-top: 1rem;
    padding: 0.75rem;
    background: #d4edda;
    border-radius: 10px;
    color: #155724;
    font-weight: 600;
}

/* 质量检验卡片 */
.quality-check .check-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.quality-check .check-item:last-child {
    border-bottom: none;
}

.quality-check .check-item.passed i {
    color: #28a745;
}

.quality-check .check-item span:nth-child(2) {
    flex: 1;
    color: #495057;
    font-weight: 500;
}

.quality-check .check-item .status {
    color: #28a745;
    font-weight: 600;
    font-size: 0.9rem;
}

/* 最终输出卡片 */
.final-output .summary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.final-output .summary-item:last-child {
    border-bottom: none;
}

.final-output .summary-item i {
    font-size: 1.1rem;
}

.final-output .label {
    flex: 1;
    color: #6c757d;
    font-weight: 500;
}

.final-output .value {
    color: #495057;
    font-weight: 600;
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

/* 技术优势区域 */
.tech-advantages {
    border-top: 3px solid #e9ecef;
}

.advantage-card {
    text-align: center;
    padding: 1.5rem 1rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.advantage-card h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.advantage-card p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .step-number {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .step-number .number {
        font-size: 1.2rem;
    }

    .step-number .step-icon {
        font-size: 1.5rem;
    }

    .step-title {
        font-size: 1.3rem;
    }

    .step-description {
        font-size: 1rem;
    }

    .visual-card {
        margin-top: 2rem;
    }

    .advantage-card {
        margin-bottom: 1rem;
    }
}

/* 动画效果 */
.visual-card {
    animation: cardFloat 3s ease-in-out infinite;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.step-number {
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5); }
}

/* 演示动画类 */
.demo-active .database-search .progress-bar {
    animation: progressFill 2s ease-in-out;
}

@keyframes progressFill {
    0% { width: 0%; }
    100% { width: var(--target-width); }
}

.demo-active .ai-optimization .score {
    animation: scoreIncrease 1.5s ease-in-out;
}

@keyframes scoreIncrease {
    0% { width: 0%; }
    100% { width: var(--target-width); }
}

.demo-active .quality-check .check-item {
    animation: checkAppear 0.5s ease-in-out;
}

.demo-active .quality-check .check-item:nth-child(1) { animation-delay: 0.2s; }
.demo-active .quality-check .check-item:nth-child(2) { animation-delay: 0.4s; }
.demo-active .quality-check .check-item:nth-child(3) { animation-delay: 0.6s; }
.demo-active .quality-check .check-item:nth-child(4) { animation-delay: 0.8s; }

@keyframes checkAppear {
    0% { opacity: 0; transform: translateX(-20px); }
    100% { opacity: 1; transform: translateX(0); }
}

/* 高亮效果 */
.step-highlight {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 2rem;
    margin: 1rem 0;
    border: 2px solid rgba(102, 126, 234, 0.2);
    animation: highlightGlow 2s ease-in-out infinite;
}

@keyframes highlightGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.2); }
    50% { box-shadow: 0 0 30px rgba(102, 126, 234, 0.4); }
}
