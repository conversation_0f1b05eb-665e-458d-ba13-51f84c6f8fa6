/**
 * 书籍搜索与添加功能样式
 */

/* 搜索结果样式 */
.book-row {
    transition: background-color 0.2s ease;
}

.book-row:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 搜索结果中的按钮样式 */
.select-book-btn {
    min-width: 60px;
}

/* 书籍搜索模态框样式 */
.modal-body .table-responsive {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

/* 加载状态样式 */
.search-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 0;
}

/* 搜索消息样式 */
#bookSearchMessage {
    padding: 1.5rem;
    text-align: center;
    color: #6c757d;
}

/* 表单样式优化 */
.modal-body .form-label {
    font-weight: 500;
}

.modal-body .form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

/* 必填项标记 */
.required-field::after {
    content: "*";
    color: #dc3545;
    margin-left: 0.25rem;
}

/* 模态框头部样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* 模态框底部样式 */
.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* 保存按钮样式 */
#confirmAddBook {
    min-width: 80px;
}

/* 调试按钮样式 */
#debugFormBtn {
    position: absolute;
    right: 15px;
    bottom: 15px;
    opacity: 0.7;
    font-size: 0.8rem;
}

/* 响应式调整 */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .table-responsive {
        max-height: 300px;
    }
}

/* 表单验证样式 */
input:required:invalid,
textarea:required:invalid {
    border-color: #dc3545;
}

input:focus:invalid,
textarea:focus:invalid {
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* 工具提示样式优化 */
.tooltip {
    font-size: 0.875rem;
    opacity: 0.98 !important;
    z-index: 9999;
    pointer-events: none;
}

.tooltip-inner {
    max-width: 350px;
    padding: 0.75rem 1rem;
    background-color: #fff;
    color: #212529;
    border: 1px solid rgba(0,0,0,.2);
    box-shadow: 0 5px 15px rgba(0,0,0,.2);
    text-align: left;
    border-radius: 6px;
    transition: opacity 0.3s ease;
}

/* 书籍标题悬停样式 */
.book-row td:first-child {
    cursor: help;
    position: relative;
    transition: all 0.2s ease;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.book-row td:first-child:hover {
    background-color: rgba(13, 110, 253, 0.1);
    text-decoration: underline;
    color: #0d6efd;
}

/* 添加一个小图标提示有更多信息 */
.book-row td:first-child::after {
    content: "ℹ️";
    font-size: 0.75rem;
    opacity: 0.5;
    margin-left: 5px;
    vertical-align: super;
    transition: opacity 0.2s ease;
}

.book-row td:first-child:hover::after {
    opacity: 1;
}

/* 自定义Bootstrap tooltip箭头颜色 */
.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: rgba(0,0,0,.2);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: rgba(0,0,0,.2);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: rgba(0,0,0,.2);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: rgba(0,0,0,.2);
}

/* 确保tooltip内容可以滚动 */
.tooltip-inner div {
    max-height: 200px;
    overflow-y: auto;
}

/* 添加书籍行悬停效果 */
.book-row {
    position: relative;
    cursor: pointer;
}

.book-row:hover {
    background-color: rgba(13, 110, 253, 0.1);
}