/**
 * 聊天历史美化样式
 * 结合Tailwind CSS和DaisyUI的美学元素，打造和谐现代的UI
 */

/* 聊天侧边栏整体样式 */
.chat-sidebar {
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  width: 280px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 新建对话按钮 */
.new-chat-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.8rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
  transition: all 0.2s;
}

.new-chat-btn:hover {
  background: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.new-chat-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 5px rgba(79, 70, 229, 0.3);
}

/* 聊天项美化 */
.chat-item {
  margin: 6px 10px;
  padding: 12px 14px;
  border-radius: 10px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 3px solid transparent;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
}

.chat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(79, 70, 229, 0.08), transparent);
  opacity: 0;
  transition: opacity 0.25s ease;
  z-index: 0;
}

.chat-item:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  border-left-color: #4f46e5;
  transform: translateX(2px);
}

.chat-item:hover::before {
  opacity: 1;
}

.chat-item.active {
  background: rgba(79, 70, 229, 0.05);
  border-left-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.chat-item.active::before {
  opacity: 1;
  background: linear-gradient(to right, rgba(79, 70, 229, 0.15), transparent);
}

.chat-item-content {
  position: relative;
  z-index: 1;
}

.chat-item-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-item-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #64748b;
}

.chat-item-time i {
  font-size: 0.7rem;
}

/* 聊天项操作按钮 */
.chat-item-actions {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translate(10px, -50%);
  opacity: 0;
  display: flex;
  gap: 6px;
  transition: all 0.25s ease;
  z-index: 2;
}

.chat-item:hover .chat-item-actions {
  transform: translate(0, -50%);
  opacity: 1;
}

.chat-item-edit,
.chat-item-delete {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: white;
  border: none;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.chat-item-edit:hover {
  background: #4f46e5;
  color: white;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.chat-item-delete:hover {
  background: #ef4444;
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 模态框美化 */
.custom-modal .modal-content {
  border: none;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
}

.custom-modal .modal-header {
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.custom-modal .modal-title {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-modal .modal-body {
  padding: 24px;
}

.custom-modal .modal-footer {
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* 删除对话模态框 */
#deleteChatModal .modal-content {
  border-top: 4px solid #ef4444;
}

.delete-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(239, 68, 68, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
}

.delete-icon-container::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.05);
  z-index: 0;
  animation: pulse 2s infinite;
}

.delete-icon-container i {
  font-size: 2.5rem;
  color: #ef4444;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

#deleteChatTitle {
  font-weight: 600;
  color: #1e293b;
  padding: 0 4px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 4px;
}

/* 编辑标题模态框 */
#editTitleModal .modal-content {
  border-top: 4px solid #4f46e5;
}

#editTitleModal .input-group-text {
  background: rgba(79, 70, 229, 0.05);
  border: 1px solid #e2e8f0;
  border-right: none;
  color: #4f46e5;
}

#editTitleInput {
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

#editTitleInput:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* 动画效果 */
.modal.fade .modal-dialog {
  transform: scale(0.95) translateY(-10px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal.show .modal-dialog {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 按钮样式美化 */
.custom-modal .btn {
  padding: 0.5rem 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-modal .btn-light {
  background: white;
  border: 1px solid #e2e8f0;
  color: #475569;
}

.custom-modal .btn-light:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.custom-modal .btn-primary {
  background: #4f46e5;
  border: none;
  box-shadow: 0 2px 5px rgba(79, 70, 229, 0.3);
}

.custom-modal .btn-primary:hover {
  background: #4338ca;
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.4);
}

.custom-modal .btn-danger {
  background: #ef4444;
  border: none;
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.3);
}

.custom-modal .btn-danger:hover {
  background: #dc2626;
  box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4);
} 