.chat-container {
    display: flex;
    height: calc(100vh - var(--nav-height));
    margin-top: var(--nav-height);
}

/* 聊天侧边栏样式 */
.chat-sidebar {
    width: 280px;
    background: #f8fafc;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.new-chat-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.8rem;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    transition: all 0.2s;
}

.new-chat-btn:hover {
    background: #4338ca;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.new-chat-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 5px rgba(79, 70, 229, 0.3);
}

.chat-history {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.25s ease;
    background-color: rgba(255, 255, 255, 0.5);
    border-left: 3px solid transparent;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.chat-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
    border-left-color: rgba(79, 70, 229, 0.3);
    transform: translateX(2px);
}

.chat-item.active {
    background-color: rgba(79, 70, 229, 0.08);
    border-left-color: #4f46e5;
}

.chat-item-content {
    flex: 1;
    overflow: hidden;
    padding-right: 8px;
    position: relative;
    z-index: 1;
}

.chat-item-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
    color: #333;
}

.chat-item-time {
    font-size: 0.75rem;
    color: #888;
    display: flex;
    align-items: center;
    gap: 4px;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
}

.message {
    max-width: 80%;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
}

.message.user {
    margin-left: auto;
    align-items: flex-end;
}

.message.ai {
    margin-right: auto;
    align-items: flex-start;
}

.message-content {
    max-width: 100%;
    background: #f5f5f5;
    padding: 1rem;
    border-radius: 12px;
    border-top-left-radius: 2px;
    white-space: pre-wrap;
    word-break: break-word;
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-radius: 12px;
    border-top-right-radius: 2px;
}

.message.user .message-footer {
    flex-direction: row;
    justify-content: space-between;
}

.chat-input-area {
    border-top: 1px solid #e0e0e0;
    padding: 1rem;
    display: flex;
    gap: 1rem;
}

.chat-input-area textarea {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.8rem;
    resize: none;
    height: 60px;
}

.chat-input-area button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0 1.5rem;
    border-radius: 8px;
    cursor: pointer;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    pointer-events: auto !important;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    width: 500px;
    max-width: 90%;
    pointer-events: auto !important;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

.help-text {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.modal-footer button {
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
}

.cancel-btn {
    background: #f5f5f5;
    border: none;
}

.confirm-btn {
    background: var(--primary-color);
    color: white;
    border: none;
}

/* 聊天项目操作按钮样式 */
.chat-item-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-item:hover .chat-item-actions {
    opacity: 1;
}

.chat-item-edit, .chat-item-delete {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    padding: 0;
}

.chat-item-edit:hover {
    background-color: rgba(99, 102, 241, 0.1);
    color: #4f46e5;
}

.chat-item-delete:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.custom-url-input {
    margin-top: 0.5rem;
}

.hidden {
    display: none;
}

.message-time {
    color: #6c757d;
    margin-right: auto;
}

.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.thinking .message-content {
    padding: 0.5rem 1rem;
}

.typing-indicator {
    display: inline-flex;
    align-items: center;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background: #606060;
    border-radius: 50%;
    display: block;
    margin: 0 3px;
    opacity: 0.4;
    animation: typing 1s infinite;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.3s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes typing {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    50% {
        transform: translateY(-5px);
        opacity: 1;
    }
}

.message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    opacity: 0.7;
    transition: opacity 0.2s;
    font-size: 0.85rem;
    width: 100%;
}

.message:hover .message-footer {
    opacity: 1;
}

.copy-icon {
    display: inline-flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.85rem;
    cursor: pointer;
    padding: 2px 5px;
    border-radius: 4px;
    background: transparent;
    border: none;
    transition: all 0.2s;
    margin-left: 8px;
}

.copy-icon:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #007bff;
}

.copy-icon i {
    margin-right: 3px;
    font-size: 0.85em;
}

/* Add styles for the chat header */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.chat-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #333;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: #e9ecef;
    color: #212529;
}

/* Add styles for the chat configuration area */
.chat-config {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.config-title {
    font-size: 16px;
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.config-item input[readonly] {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.config-help {
    margin-top: 10px;
    color: #6c757d;
    font-size: 12px;
}

.config-item {
    margin-bottom: 10px;
}

.config-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.config-item input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out;
}

.config-item input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.book-url-input {
    display: flex;
    gap: 10px;
}

.book-url-input input {
    flex: 1;
}

/* Add styles for the book search button */
#bookSearchBtn {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    color: #495057;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

#bookSearchBtn:hover {
    background-color: #e9ecef;
}

/* Update toast container styles */
.toast-container {
    z-index: 1050;
}

/* Toast success styles */
.toast-success {
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-out {
    animation: fadeOut 0.5s ease forwards;
}

@keyframes fadeOut {
    from { opacity: 0.9; }
    to { opacity: 0; }
}

/* Book row tooltip styles */
.book-row {
    cursor: help;
    transition: background-color 0.2s;
}

.book-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Hide old copy button if it exists */
.copy-btn {
    display: none;
}

/* The error message style was removed, let's add it back */
.message.error .message-content {
    background: #fee2e2;
    color: #dc2626;
}

/* Error message styling */
.message.ai.error .message-content {
    background: #fee2e2;
    color: #dc2626;
    border-left: 3px solid #dc2626;
}

/* Retry button styling */
.retry-icon {
    display: inline-flex;
    align-items: center;
    color: #dc2626;
    font-size: 0.85rem;
    cursor: pointer;
    padding: 2px 5px;
    border-radius: 4px;
    background: transparent;
    border: none;
    transition: all 0.2s;
    margin-left: 8px;
}

.retry-icon:hover {
    background-color: rgba(220, 38, 38, 0.1);
}

.retry-icon i {
    margin-right: 3px;
    font-size: 0.85em;
}

/* Toast warning styles */
.toast.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

/* Network status indicator */
.network-status {
    position: fixed;
    bottom: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 5px;
}

.network-status.online {
    background-color: #d4edda;
    color: #155724;
}

.network-status.offline {
    background-color: #f8d7da;
    color: #721c24;
}

.network-status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.network-status.online .network-status-icon {
    background-color: #28a745;
}

.network-status.offline .network-status-icon {
    background-color: #dc3545;
}

/* 终止按钮样式 */
#stopResponse {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#stopResponse:hover {
    background-color: #c82333;
}

#stopResponse:disabled {
    background-color: #e4606d;
    cursor: not-allowed;
}

/* 系统消息样式 */
.message.system .message-content {
    background-color: #fff3cd;
    color: #856404;
    border-left: 3px solid #ffeeba;
}

/* 自定义模态框样式 */
.custom-modal .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.custom-modal .modal-header {
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.custom-modal .modal-body {
    padding: 24px;
}

.custom-modal .modal-footer {
    padding: 16px 24px;
    background-color: #f8f9fa;
}

.custom-modal .form-control {
    border-radius: 8px;
    padding: 10px 16px;
    border: 1px solid #ced4da;
    transition: all 0.25s ease;
}

.custom-modal .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-modal .input-group-text {
    background-color: #f8f9fa;
    border-radius: 8px 0 0 8px;
    border-right: none;
}

.custom-modal .form-control {
    border-radius: 0 8px 8px 0;
}

/* 删除对话模态框样式 */
.delete-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(220, 53, 69, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.delete-icon-container i {
    font-size: 36px;
}

#deleteChatModal .modal-content {
    border-top: 4px solid #dc3545;
}

/* 模态框动画效果 */
.modal.fade .modal-dialog {
    transform: scale(0.95);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}

/* 编辑标题模态框样式 */
#editTitleModal .modal-content {
    border-top: 4px solid #007bff;
}

.message.system {
    color: #888;
    font-style: italic;
    text-align: center;
    background: #f8f8f8;
    border: 1px dashed #ddd;
}

/* 现代化聊天输入区域样式 */
.chat-input-area-container {
    padding: 16px 24px;
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    margin-top: auto;
}

.chat-main-input-group {
    position: relative;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    display: flex;
    flex-direction: column;
}

.chat-main-input-group:focus-within {
    border-color: #a5b4fc;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

#messageInput {
    resize: none;
    border: none;
    background-color: transparent;
    padding: 14px 60px 14px 16px;
    font-size: 0.95rem;
    line-height: 1.5;
    color: #374151;
    width: 100%;
    border-radius: 10px;
    min-height: 56px;
    max-height: 200px;
    transition: height 0.1s ease;
    outline: none !important;
    box-shadow: none !important;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.chat-input-buttons {
    position: absolute;
    right: 10px;
    bottom: 8px;
    display: flex;
    gap: 8px;
}

.btn-send {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    background-color: #4d6bfe;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.btn-send:hover {
    background-color: #3a56d4;
}

.btn-send:disabled {
    background-color: #d1d5db;
    cursor: not-allowed;
    opacity: 0.6;
}

.btn-send:disabled:hover {
    background-color: #d1d5db;
}

.btn-send i, .btn-stop i {
    transition: all 0.3s ease;
}

.btn-stop {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    background-color: #ef4444;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.btn-stop:hover {
    background-color: #dc2626;
}

.btn-stop:disabled {
    background-color: #f87171;
    cursor: not-allowed;
    opacity: 0.6;
}

/* 添加字数计数器样式 */
.char-counter {
    position: absolute;
    bottom: 12px;
    left: 16px;
    font-size: 0.75rem;
    color: #9ca3af;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-main-input-group:focus-within .char-counter {
    opacity: 1;
}

/* 输入框底部提示信息 */
.input-help-text {
    margin-top: 8px;
    font-size: 0.75rem;
    color: #6b7280;
    text-align: center;
}

/* 适应深色模式 */
@media (prefers-color-scheme: dark) {
    .chat-input-area-container {
        background-color: #1f2937;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .chat-main-input-group {
        background-color: #374151;
        border-color: #4b5563;
    }
    
    #messageInput {
        color: #e5e7eb;
    }
    
    #messageInput::placeholder {
        color: #9ca3af;
    }
    
    .chat-main-input-group:focus-within {
        border-color: #818cf8;
    }
    
    .input-help-text {
        color: #9ca3af;
    }
}

/* 修复模态框遮罩层问题 */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1050 !important;
}

.modal-dialog {
    z-index: 1051 !important;
    pointer-events: auto !important;
}

.modal-content {
    pointer-events: auto !important;
}

/* 确保编辑和删除模态框有更高的z-index */
#editTitleModal, #deleteConfirmModal {
    z-index: 1060 !important;
}

/* 修复模态框可交互性 */
.modal {
    pointer-events: auto !important;
}

.modal-dialog {
    pointer-events: auto !important;
}

.modal-content {
    pointer-events: auto !important;
}

.modal-backdrop.show {
    opacity: 0.5;
    pointer-events: auto;
}

#newTitleInput:focus {
    z-index: 1065;
    position: relative;
}

/* 确保按钮可点击 */
.modal .btn {
    position: relative;
    z-index: 1070;
}