/**
 * 知识点搜索功能样式
 * 为知识点搜索栏和相关UI组件提供美观的样式
 */

/* 搜索高亮样式 */
.search-highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: 500;
    border: 1px solid #ffeaa7;
}

/* 搜索容器样式 */
.search-container {
    position: relative;
}

/* 主页面搜索栏样式（在卡片头部） */
.card-header .search-container .input-group-text {
    border-right: none;
    background-color: white;
    border: none;
}

.card-header .search-container .form-control {
    border-left: none;
    border-right: none;
    border: none;
    background-color: white;
}

.card-header .search-container .input-group-append .btn {
    border-left: none;
    border: 1px solid #dee2e6;
}

/* 模态框搜索栏样式 */
.modal-body .search-container .input-group-text {
    border-right: none;
    background-color: #f8f9fa;
}

.modal-body .search-container .form-control {
    border-left: none;
    border-right: none;
}

.modal-body .search-container .input-group-append .btn {
    border-left: none;
}

/* 搜索提示样式 */
.search-tips {
    animation: fadeIn 0.3s ease-in-out;
}

.search-results-info {
    animation: slideDown 0.3s ease-in-out;
}

/* 搜索统计信息样式 */
.search-stats {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

/* 搜索帮助按钮激活状态 */
#showSearchTips.active {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

/* 搜索无结果提示样式 */
#noSearchResults,
#noSearchResultsCustom {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

#noSearchResults .fa-search,
#noSearchResultsCustom .fa-search {
    color: #6c757d;
}

/* 知识点卡片搜索状态样式 */
.card.search-match {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
    transition: all 0.2s ease-in-out;
}

/* 搜索输入框焦点样式 */
#knowledgePointSearchMain:focus,
#knowledgePointSearchInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 清空搜索按钮样式 */
#clearSearchMain,
#clearSearchCustom {
    transition: all 0.2s ease-in-out;
}

#clearSearchMain:hover,
#clearSearchCustom:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

/* 搜索帮助提示框样式 */
#searchTipsAlert {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

#searchTipsAlert ul {
    padding-left: 1.2rem;
}

#searchTipsAlert li {
    margin-bottom: 0.3rem;
    color: #0c5460;
}

/* 全选可见按钮样式 */
#selectAllVisible {
    transition: all 0.2s ease-in-out;
}

#selectAllVisible:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

/* 搜索结果计数样式 */
#searchResultCount,
#searchResultCountCustom {
    font-weight: 600;
    color: #007bff;
}

#selectedCountInSearch {
    font-weight: 600;
    color: #28a745;
}

/* 响应式设计 */
@media (max-width: 768px) {
    /* 主页面卡片头部搜索栏在小屏幕上的布局 */
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .search-container {
        width: 100% !important;
        margin-top: 0.5rem;
    }

    /* 模态框搜索栏 */
    .modal-body .search-container .input-group {
        flex-direction: column;
    }

    .modal-body .search-container .input-group-prepend,
    .modal-body .search-container .input-group-append {
        width: 100%;
    }

    .modal-body .search-container .input-group-text {
        border-radius: 0.25rem 0.25rem 0 0;
        border-right: 1px solid #ced4da;
    }

    .modal-body .search-container .form-control {
        border-left: 1px solid #ced4da;
        border-right: 1px solid #ced4da;
        border-radius: 0;
    }

    .modal-body .search-container .input-group-append .btn {
        border-radius: 0 0 0.25rem 0.25rem;
        border-left: 1px solid #ced4da;
    }

    /* 移动端搜索按钮组 */
    .btn-group.w-100 {
        flex-direction: column;
    }

    .btn-group.w-100 .btn {
        border-radius: 0.25rem !important;
        margin-bottom: 0.5rem;
    }

    .btn-group.w-100 .btn:last-child {
        margin-bottom: 0;
    }
}

/* 超小屏幕设备 */
@media (max-width: 576px) {
    .card-header .search-container {
        width: 100% !important;
    }

    .card-header h5 {
        font-size: 1rem;
    }

    .search-info {
        margin-top: 0.5rem !important;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 100px;
        transform: translateY(0);
    }
}

/* 搜索加载状态 */
.search-loading {
    position: relative;
}

.search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* 知识点列表项搜索匹配样式 */
.list-group-item-action.search-matched {
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
}

.list-group-item-action.search-matched:hover {
    background-color: #e9ecef;
}

/* 搜索状态栏样式 */
.search-status-bar {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 8px 12px;
    backdrop-filter: blur(10px);
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.2rem;
}

/* 搜索结果容器样式 */
.search-results-container {
    animation: fadeIn 0.3s ease-in-out;
}

.search-results-header {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #007bff;
}

.search-summary h6 {
    color: #495057;
    font-weight: 600;
}

.search-group {
    margin-bottom: 1.5rem;
}

.search-group-header h6 {
    font-weight: 600;
    color: #6c757d;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

/* 知识点项样式 */
.knowledge-point-item {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0 !important;
    background-color: #fff;
    cursor: pointer;
}

.knowledge-point-item:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.knowledge-point-item.bg-light {
    background-color: #f8f9fa !important;
    border-color: #28a745 !important;
}

.knowledge-point-item .knowledge-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    line-height: 1.3;
}

.knowledge-point-item .knowledge-meta .badge {
    font-size: 0.75rem;
    padding: 0.25em 0.5em;
}

.knowledge-point-item .form-check-input {
    transform: scale(1.1);
    margin-top: 0.1rem;
}

/* 搜索结果卡片样式（保留以兼容其他地方） */
.search-result-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.search-result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    border-color: #007bff;
}

.search-result-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
}

.search-result-card .card-meta .badge {
    font-size: 0.7rem;
}

.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}

.badge-outline-secondary {
    color: #6c757d;
    border: 1px solid #6c757d;
    background-color: transparent;
}

/* 无搜索结果样式 */
.no-search-results {
    background-color: #f8f9fa;
    border-radius: 12px;
    margin: 2rem 0;
    border: 2px dashed #dee2e6;
}

.no-search-results .fa-search {
    opacity: 0.3;
}

.search-suggestions {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-suggestions ul li {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 搜索错误样式 */
.search-error .alert {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.15);
}

/* 搜索加载容器样式 */
.search-loading-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin: 1rem 0;
}

.search-loading-container .progress {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.search-loading-container .progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fadeInUp {
    animation: fadeInUp 0.5s ease-out forwards;
}

/* 搜索结果为空时的样式优化 */
.empty-search-result {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.empty-search-result .fa-search {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-search-result h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-search-result .btn {
    margin-top: 1rem;
}

/* 搜索建议样式 */
.search-suggestions {
    background-color: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.25rem 0.25rem;
    max-height: 200px;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-suggestion-item {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

.search-suggestion-item:hover {
    background-color: #f8f9fa;
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

/* 搜索历史样式 */
.search-history {
    font-size: 0.875rem;
    color: #6c757d;
}

.search-history-item {
    display: inline-block;
    background-color: #e9ecef;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-history-item:hover {
    background-color: #dee2e6;
}
