// 文件开头添加依赖检查
(function() {
    // 检查jQuery是否加载
    if (typeof $ === 'undefined') {
        console.error('jQuery未加载，书籍搜索功能将不可用');
        return;
    }

    // 立即确保关键函数可用
    ensureCriticalFunctionsAvailable();
    
    // 检查common.js中的关键函数是否可用
    $(document).ready(function() {
        // 再次确保关键函数可用
        ensureCriticalFunctionsAvailable();
        
        // 检查关键函数是否已定义
        const requiredFunctions = [
            { name: 'normalizeToken', global: true },
            { name: 'validateCurrentToken', global: true },
            { name: 'showToast', global: true }
        ];

        let missingFunctions = [];
        
        requiredFunctions.forEach(func => {
            if (func.global) {
                if (typeof window[func.name] !== 'function') {
                    missingFunctions.push(func.name);
                    console.warn(`${func.name}函数未定义，将尝试使用兼容实现`);
                }
            } else {
                if (typeof func.name !== 'function') {
                    missingFunctions.push(func.name);
                    console.warn(`${func.name}函数未定义，将尝试使用兼容实现`);
                }
            }
        });

        if (missingFunctions.length > 0) {
            console.warn(`检测到${missingFunctions.length}个缺失函数，已启用兼容模式:`, missingFunctions.join(', '));
        }
    });
})();

// 确保关键函数立即可用
function ensureCriticalFunctionsAvailable() {
    // 添加normalizeToken的兼容实现
    if (typeof window.normalizeToken !== 'function') {
        window.normalizeToken = function(token) {
            if (!token) return null;
            if (typeof token !== 'string') return null;
            
            // 移除可能存在的Bearer前缀
            if (token.toLowerCase().startsWith('bearer ')) {
                return token.substring(7);
            }
            return token;
        };
        console.log('添加全局normalizeToken兼容实现');
    }
    
    // 添加validateCurrentToken的兼容实现
    if (typeof window.validateCurrentToken !== 'function') {
        window.validateCurrentToken = async function() {
            try {
                const token = localStorage.getItem('token');
                if (!token) return false;
                
                const normalizedToken = window.normalizeToken(token);
                
                // 检查token长度是否合理
                if (!normalizedToken || normalizedToken.length < 10) {
                    console.warn('Token格式不正确或长度不足');
                    return false;
                }
                
                // 本地验证通过，假设token有效
                return true;
            } catch (error) {
                console.error('验证token时出错:', error);
                return false;
            }
        };
        console.log('添加全局validateCurrentToken兼容实现');
    }
    
    // 添加showToast的兼容实现
    if (typeof window.showToast !== 'function') {
        window.showToast = function(message, type) {
            console.log(`[${type || 'info'}] ${message}`);
            if (typeof window.alert === 'function') {
                window.alert(message);
            }
        };
        console.log('添加全局showToast兼容实现');
    }
}

$(document).ready(function() {
    console.log('书籍搜索模块初始化');

    // 检查当前认证状态
    checkAuthentication();

    // 检查用户是否已登录，未登录则显示登录按钮和提示
    verifyLoginStatus();

    // 确保模态框正确初始化
    try {
        // 检查是否已经初始化
        const bookSearchModal = new bootstrap.Modal(document.getElementById('bookSearchModal'));
    } catch (error) {
        console.error('模态框初始化失败:', error);
    }

    /**
     * 打开书籍搜索模态框
     * @param {string} source - 来源，'main'或'new-chat'
     * @returns {boolean} - 是否成功打开
     */
    function openBookSearchModal(source = 'main') {
        console.log('打开书籍搜索模态框，来源:', source);

        try {
            // 先清理所有已存在的模态框和遮罩层
            cleanupAllModals();

            // 获取模态框元素
            const modalElement = document.getElementById('bookSearchModal');
            if (!modalElement) {
                console.error('找不到书籍搜索模态框元素');
                showToast('界面元素错误，请刷新页面重试', 'error');
                return false;
            }

            // 存储来源
            modalElement.dataset.source = source;

            // 创建新实例
            try {
                currentModalInstance = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
            } catch (e) {
                console.error('创建模态框实例失败:', e);

                // 尝试备用方法
                try {
                    $(modalElement).modal({
                        backdrop: true,
                        keyboard: true,
                        focus: true
                    });
                } catch (e2) {
                    console.error('备用方法也失败:', e2);
                    showToast('打开搜索窗口失败，请刷新页面重试', 'error');
                    return false;
                }
            }

            // 显示模态框
            try {
                if (currentModalInstance) {
                    currentModalInstance.show();
                } else {
                    $(modalElement).modal('show');
                }
            } catch (e) {
                console.error('显示模态框失败:', e);
                showToast('打开搜索窗口失败，请刷新页面重试', 'error');
                return false;
            }

            // 清空搜索输入框并聚焦
            setTimeout(() => {
                try {
                    const $input = $('#bookSearchInput');
                    $input.val('');
                    $input.focus();

                    // 清空旧的搜索结果
                    $('#bookSearchResults').empty();
                    $('#bookSearchMessage').addClass('d-none');

                    // 清理所有tooltip
                    $('[data-bs-toggle="tooltip"]').tooltip('dispose');
                    $('.tooltip-container, .book-tooltip').remove();

                    // 显示默认消息
                    $('#bookSearchMessage').text('请输入搜索关键词查找书籍').removeClass('d-none');
                } catch (e) {
                    console.warn('设置搜索输入框失败:', e);
                }
            }, 300);

            return true;
        } catch (error) {
            console.error('打开模态框过程中发生错误:', error);
            showToast('打开搜索窗口失败，请刷新页面重试', 'error');

            // 确保清理
            try {
                cleanupAllModals();
            } catch (e) {
                console.warn('清理模态框失败:', e);
            }

            return false;
        }
    }

    // 主界面和模态框中的搜索按钮
    $('#bookSearchBtn, #modalBookSearchBtn').on('click', function() {
        // 首先验证登录状态
        if (!isAuthenticated()) {
            showLoginPrompt();
            return;
        }

        // 记录当前触发搜索的来源
        const source = $(this).attr('id') === 'bookSearchBtn' ? 'main' : 'new-chat';

        // 打开模态框
        openBookSearchModal(source);
    });

    // ===== 搜索功能 =====

    // 搜索按钮点击事件
    $('#bookSearchButton').on('click', function() {
        // 验证登录状态
        if (!isAuthenticated()) {
            showLoginPrompt();
            return;
        }

        // 获取搜索关键词
        const query = $('#bookSearchInput').val().trim();

        if (!query) {
            showMessage('请输入搜索关键词');
            return;
        }

        // 执行搜索
        searchBooks(query);
    });

    // 回车键触发搜索
    $('#bookSearchInput').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#bookSearchButton').click();
        }
    });

    // 执行搜索
    function searchBooks(keyword) {
        console.log('搜索书籍，关键词:', keyword);

        // 显示加载状态
        $('#bookSearchResults').html('');
        $('#bookSearchMessage').addClass('d-none');
        $('#bookSearchLoading').removeClass('d-none');

        // 移除任何现有的tooltip
        $('.book-tooltip').remove();

        // 构建URL
        const searchUrl = `/api/books/search?keyword=${encodeURIComponent(keyword)}`;

        // 获取认证令牌 - 书籍搜索API现在不需要认证，但如果有token我们仍然发送它
        const token = getAuthToken();

        // 设置请求超时
        const requestTimeout = 10000; // 10秒
        let requestTimer;

        // 准备请求配置
        const ajaxConfig = {
            url: searchUrl,
            method: 'GET',
            timeout: requestTimeout,
            beforeSend: function(xhr) {
                // 如果有token，添加到请求头
                if (token) {
                    xhr.setRequestHeader('Authorization', token);
                }

                // 设置超时处理
                requestTimer = setTimeout(function() {
                    xhr.abort();
                    handleSearchFailure('请求超时，尝试使用备用方法');
                    // 尝试备用方法 - 获取所有书籍然后在前端过滤
                    fallbackSearch(keyword);
                }, requestTimeout);
            },
            success: function(response) {
                clearTimeout(requestTimer);
                $('#bookSearchLoading').addClass('d-none');

                // 处理搜索结果
                const books = response.data || [];

                if (books.length === 0) {
                    showMessage('未找到相关书籍');
                    // 尝试备用方法
                    fallbackSearch(keyword);
                    return;
                }

                // 显示结果
                displaySearchResults(books);
            },
            error: function(error) {
                clearTimeout(requestTimer);
                $('#bookSearchLoading').addClass('d-none');
                console.error('搜索失败:', error.status, error.statusText);

                // 检查是否是认证问题
                if (error.status === 401) {
                    showLoginPrompt('您的登录已过期，请重新登录');
                    return;
                }

                handleSearchFailure('搜索失败，尝试备用方法');

                // 尝试备用方法 - 获取所有书籍然后在前端过滤
                fallbackSearch(keyword);
            },
            complete: function() {
                clearTimeout(requestTimer);
            }
        };

        // 发送API请求
        $.ajax(ajaxConfig);
    }

    // 搜索失败处理
    function handleSearchFailure(message) {
        showToast(message, 'warning');
        showMessage(message);
    }

    // 备用搜索方法 - 获取所有书籍然后在前端过滤
    function fallbackSearch(keyword) {
        console.log('使用备用方法搜索书籍，关键词:', keyword);
        $('#bookSearchLoading').removeClass('d-none');

        // 获取认证令牌 - 现在不需要认证，但如果有token我们仍然发送它
        const token = getAuthToken();

        // 准备请求配置
        const ajaxConfig = {
            url: '/api/books',
            method: 'GET',
            timeout: 10000, // 10秒超时
            beforeSend: function(xhr) {
                // 如果有token，添加到请求头
                if (token) {
                    xhr.setRequestHeader('Authorization', token);
                }
            },
            success: function(response) {
                $('#bookSearchLoading').addClass('d-none');
                console.log('获取所有书籍成功，开始前端过滤');

                const allBooks = response.data || [];
                if (allBooks.length === 0) {
                    console.log('系统中没有书籍数据');
                    showMessage('系统中没有书籍数据');
                    return;
                }

                console.log(`获取到 ${allBooks.length} 本书籍，开始前端过滤`);

                // 前端过滤
                const lowercaseKeyword = keyword.toLowerCase();
                const filteredBooks = allBooks.filter(book =>
                    (book.title && book.title.toLowerCase().includes(lowercaseKeyword)) ||
                    (book.type && book.type.toLowerCase().includes(lowercaseKeyword)) ||
                    (book.description && book.description.toLowerCase().includes(lowercaseKeyword))
                );

                console.log(`前端过滤结果: 找到 ${filteredBooks.length} 本书籍`);

                if (filteredBooks.length === 0) {
                    showMessage('未找到相关书籍');
                    return;
                }

                // 显示结果
                displaySearchResults(filteredBooks);
                showToast('已使用备用搜索方法', 'info');
            },
            error: function(error) {
                $('#bookSearchLoading').addClass('d-none');
                console.error('备用搜索失败:', error);
                console.error('错误详情:', {
                    status: error.status,
                    statusText: error.statusText,
                    responseText: error.responseText
                });

                // 检查是否是认证问题
                if (error.status === 401) {
                    showLoginPrompt('您的登录已过期，请重新登录');
                    return;
                }

                showToast('搜索失败，请稍后重试', 'error');
                showMessage('搜索失败，请重试');
            }
        };

        // 发送API请求
        $.ajax(ajaxConfig);
    }

    // 添加全局变量存储当前书籍数据
    window.currentSearchBooks = [];

    // 添加自定义tooltip样式，如果不存在
    if (!document.getElementById('book-tooltip-styles')) {
        const styleElement = document.createElement('style');
        styleElement.id = 'book-tooltip-styles';
        styleElement.textContent = `
            .tooltip-container {
                background-color: #fff;
                border: 1px solid rgba(0,0,0,.2);
                border-radius: .3rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
                padding: 0;
                max-width: 350px;
                width: 350px;
                z-index: 1070;
            }
            .book-tooltip {
                color: #212529;
                text-align: left;
                padding: 1rem;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            }
            .book-tooltip-title {
                font-weight: bold;
                color: #0d6efd;
                margin-bottom: 10px;
                border-bottom: 1px solid #e9ecef;
                padding-bottom: 10px;
                font-size: 1.1rem;
                line-height: 1.4;
            }
            .book-tooltip-type {
                font-style: italic;
                color: #6c757d;
                margin-bottom: 8px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
            }
            .book-tooltip-type:before {
                content: '';
                display: inline-block;
                width: 8px;
                height: 8px;
                background-color: #6c757d;
                border-radius: 50%;
                margin-right: 8px;
            }
            .book-tooltip-chapter {
                color: #dc3545;
                font-weight: bold;
                margin-bottom: 12px;
                font-size: 0.95rem;
                display: flex;
                align-items: center;
            }
            .book-tooltip-chapter:before {
                content: '';
                display: inline-block;
                width: 8px;
                height: 8px;
                background-color: #dc3545;
                border-radius: 50%;
                margin-right: 8px;
            }
            .book-tooltip-desc {
                color: #212529;
                max-height: 200px;
                overflow-y: auto;
                line-height: 1.6;
                padding-right: 5px;
                /* 自定义滚动条 */
                scrollbar-width: thin;
                scrollbar-color: #cdcdcd #f1f1f1;
            }
            .book-tooltip-desc::-webkit-scrollbar {
                width: 6px;
            }
            .book-tooltip-desc::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }
            .book-tooltip-desc::-webkit-scrollbar-thumb {
                background: #cdcdcd;
                border-radius: 3px;
            }
            .book-tooltip-desc::-webkit-scrollbar-thumb:hover {
                background: #ababab;
            }
            .book-tooltip-no-desc {
                color: #6c757d;
                font-style: italic;
                padding: 5px;
                background-color: #f9f9f9;
                border-radius: 4px;
                text-align: center;
            }
            .book-title-cell {
                position: relative;
                cursor: pointer;
            }
            .book-title-cell:hover {
                background-color: #f8f9fa;
            }
            .book-title {
                display: inline-block;
                max-width: calc(100% - 80px);
                vertical-align: middle;
            }
            .chapter-badge {
                display: inline-block;
                padding: 0.25em 0.5em;
                font-size: 75%;
                font-weight: 700;
                line-height: 1;
                text-align: center;
                white-space: nowrap;
                vertical-align: middle;
                border-radius: 0.25rem;
                color: #fff;
                background-color: #dc3545;
                margin-left: 0.5rem;
                box-shadow: 0 1px 2px rgba(0,0,0,.2);
            }
        `;
        document.head.appendChild(styleElement);
    }

    // 显示搜索结果
    function displaySearchResults(books) {
        const $results = $('#bookSearchResults');
        $results.html('');

        // 确保结果容器存在
        if ($results.length === 0) {
            console.error('搜索结果容器不存在!');
            showToast('无法显示搜索结果，请刷新页面重试', 'error');
            return;
        }

        // 清除任何已存在的tooltip
        $('.book-tooltip').remove();

        // 存储书籍数据供tooltip使用
        window.currentSearchBooks = books;

        // 遍历并显示每本书
        $.each(books, function(index, book) {
            // 提取章节信息（如果有）- 支持多种格式
            let chapterInfo = null;
            let chapterNumber = null;
            let chapterType = '';

            if (book.title) {
                // 中文章节格式: 第X章
                const cnMatch = book.title.match(/第(\d+)章/);
                if (cnMatch) {
                    chapterInfo = cnMatch[0];
                    chapterNumber = cnMatch[1];
                    chapterType = '章';
                } else {
                    // 英文章节格式: Chapter X
                    const enMatch = book.title.match(/Chapter\s+(\d+)/i);
                    if (enMatch) {
                        chapterInfo = enMatch[0];
                        chapterNumber = enMatch[1];
                        chapterType = 'Chapter';
                    } else {
                        // 节/部分格式: Section X
                        const sectionMatch = book.title.match(/Section\s+(\d+)/i);
                        if (sectionMatch) {
                            chapterInfo = sectionMatch[0];
                            chapterNumber = sectionMatch[1];
                            chapterType = 'Section';
                        } else {
                            // 部分格式: Part X
                            const partMatch = book.title.match(/Part\s+(\d+)/i);
                            if (partMatch) {
                                chapterInfo = partMatch[0];
                                chapterNumber = partMatch[1];
                                chapterType = 'Part';
                            }
                        }
                    }
                }
            }

            const hasChapter = chapterInfo !== null;

            // 准备tooltip内容 - 不使用数据属性存储，直接用ID关联
            const bookId = `book-${index}`;

            // 创建行元素 - 简化结构，不使用data属性
            const $row = $(`
                <tr class="book-row" id="${bookId}">
                    <td class="book-title-cell">
                        <div class="book-title">${escapeHtml(book.title || '')}</div>
                        ${hasChapter ?
                            `<span class="chapter-badge">${chapterType} ${chapterNumber}</span>` :
                            ''}
                    </td>
                    <td>${escapeHtml(book.type || '-')}</td>
                    <td>
                        <button class="btn btn-sm btn-primary select-book">选择</button>
                    </td>
                </tr>
            `);

            // 创建对应的tooltip HTML
            const tooltipContent = `
                <div class="book-tooltip-title">${escapeHtml(book.title || '')}</div>
                ${book.type ? `<div class="book-tooltip-type">类型: ${escapeHtml(book.type)}</div>` : ''}
                ${hasChapter ? `<div class="book-tooltip-chapter">章节: ${chapterInfo}</div>` : ''}
                ${book.description
                    ? `<div class="book-tooltip-desc">${escapeHtml(book.description)}</div>`
                    : '<div class="book-tooltip-no-desc">暂无简介</div>'}
            `;

            // 绑定选择事件
            $row.find('.select-book').on('click', function() {
                selectBook(book);
            });

            // 使用简单直接的方式处理悬停
            const $cell = $row.find('.book-title-cell');

            $cell.on('mouseenter', function(e) {
                // 移除所有已存在的tooltip
                $('.book-tooltip, .tooltip-container').remove();

                // 创建新的tooltip容器
                const $tooltipContainer = $('<div class="tooltip-container"></div>');
                const $tooltip = $('<div class="book-tooltip"></div>').html(tooltipContent);
                $tooltipContainer.append($tooltip);
                $('body').append($tooltipContainer);

                // 获取鼠标位置
                const mouseX = e.clientX;
                const mouseY = e.clientY;

                // 获取位置信息
                const cellOffset = $(this).offset();
                const cellWidth = $(this).outerWidth();
                const cellHeight = $(this).outerHeight();
                const tooltipWidth = 350; // 固定宽度

                // 计算tooltip位置 - 默认显示在鼠标右侧
                let left = mouseX + 15;
                let top = mouseY;

                // 检查是否超出窗口右侧边界
                if (left + tooltipWidth > $(window).width()) {
                    // 如果超出右侧边界，则显示在鼠标左侧
                    left = mouseX - tooltipWidth - 15;
                }

                // 设置tooltip位置
                $tooltipContainer.css({
                    top: top + 'px',
                    left: left + 'px',
                    display: 'block'
                });
            });

            $cell.on('mouseleave', function() {
                // 延迟隐藏tooltip，允许鼠标移动到tooltip上
                setTimeout(function() {
                    // 检查鼠标是否在tooltip上
                    if (!$('.tooltip-container:hover').length) {
                        $('.tooltip-container').fadeOut(100, function() {
                            $(this).remove();
                        });
                    }
                }, 100);
            });

            // 添加到结果容器
            $results.append($row);
        });

        // 添加tooltip容器的鼠标离开事件
        $(document).on('mouseleave', '.tooltip-container', function() {
            $(this).fadeOut(100, function() {
                $(this).remove();
            });
        });

        // 添加窗口滚动事件，移除所有tooltip
        $(window).on('scroll', function() {
            $('.tooltip-container').remove();
        });

        console.log(`加载了 ${books.length} 本书籍，设置了悬停提示`);
    }

    // 选择书籍
    function selectBook(book) {
        try {
            // 参数验证
            if (!book || typeof book !== 'object') {
                console.error('无效的书籍对象:', book);
                showToast('选择书籍失败：无效的书籍数据', 'error');
                return;
            }

            if (!book.url) {
                console.warn('书籍URL为空:', book);
                showToast('警告：所选书籍没有URL', 'warning');
                // 继续执行，因为可能是有意为之
            }

            // 获取当前模态框的来源
            const modalElement = document.getElementById('bookSearchModal');
            if (!modalElement) {
                console.error('找不到模态框元素');
                showToast('选择书籍失败：界面元素错误', 'error');
                return;
            }

            const context = modalElement.dataset.source === 'new-chat' ? 'new' : 'current';
            const bookTitle = book.title || '未命名';
            const bookUrl = book.url || '';

            console.log(`选择书籍: ${bookTitle}, URL: ${bookUrl}, 上下文: ${context}`);

            // 使用状态管理保存书籍URL
            let saveSuccess = false;
            
            // 先尝试使用ChatState
            if (typeof window.ChatState !== 'undefined' && typeof window.ChatState.saveBookUrl === 'function') {
                try {
                    window.ChatState.saveBookUrl(bookUrl, context);
                    saveSuccess = true;
                    console.log('使用ChatState保存URL成功');
                } catch (e) {
                    console.error('使用ChatState保存URL失败:', e);
                }
            } else {
                console.warn('ChatState不可用，使用DOM直接更新');
            }
            
            // 如果使用ChatState失败，直接更新DOM作为备选方案
            if (!saveSuccess) {
                try {
                    if (context === 'new') {
                        const inputEl = document.getElementById('newBookUrl');
                        if (inputEl) {
                            inputEl.value = bookUrl;
                            console.log('直接更新newBookUrl元素成功');
                            saveSuccess = true;
                        }
                    } else {
                        const inputEl = document.getElementById('currentBookUrl');
                        if (inputEl) {
                            inputEl.value = bookUrl;
                            console.log('直接更新currentBookUrl元素成功');
                            saveSuccess = true;
                        }
                    }
                } catch (e) {
                    console.error('直接更新DOM元素失败:', e);
                }
                
                // 如果直接更新DOM也失败，最后尝试jQuery
                if (!saveSuccess) {
                    try {
                        if (context === 'new') {
                            $('#newBookUrl').val(bookUrl);
                        } else {
                            $('#currentBookUrl').val(bookUrl);
                        }
                        console.log('使用jQuery更新输入框成功');
                        saveSuccess = true;
                    } catch (e) {
                        console.error('使用jQuery更新输入框失败:', e);
                    }
                }
            }

            // 无论上面的操作成功与否，都尝试关闭模态框
            closeBookSearchModal();

            // 显示成功提示
            showToast(`已选择书籍: ${bookTitle}`, 'success');

            // 返回成功状态，便于调用者判断
            return saveSuccess;
        } catch (error) {
            console.error('选择书籍过程中发生错误:', error);
            showToast('选择书籍失败，请重试', 'error');

            // 确保清理模态框
            try {
                closeBookSearchModal();
            } catch (e) {
                console.warn('清理模态框失败:', e);
            }

            // 返回失败状态
            return false;
        }
    }

    // 安全地关闭书籍搜索模态框
    function closeBookSearchModal() {
        try {
            const modalElement = document.getElementById('bookSearchModal');
            if (!modalElement) {
                console.warn('找不到书籍搜索模态框元素');
                return;
            }
            
            // 尝试使用Bootstrap API关闭
            try {
                const instance = bootstrap.Modal.getInstance(modalElement);
                if (instance) {
                    instance.hide();
                } else {
                    // 备用关闭方法
                    $(modalElement).modal('hide');
                }
            } catch (e) {
                console.warn('使用Bootstrap API关闭模态框失败:', e);
                
                // 备用关闭方法
                try {
                    $(modalElement).modal('hide');
                } catch (e2) {
                    console.error('备用关闭方法也失败:', e2);
                }
            }
            
            // 无论上面是否成功，都尝试使用cleanupAllModals进行彻底清理
            if (typeof cleanupAllModals === 'function') {
                setTimeout(cleanupAllModals, 100);
            }
        } catch (error) {
            console.error('关闭书籍搜索模态框失败:', error);
        }
    }

    // 显示消息
    function showMessage(message) {
        const $message = $('#bookSearchMessage');

        // 检查消息元素是否存在
        if ($message.length === 0) {
            console.error('消息显示元素不存在!');
            alert(message); // 使用alert作为备用
            return;
        }

        // 设置消息文本并显示
        $message.text(message).removeClass('d-none');

        // 确保加载指示器隐藏
        $('#bookSearchLoading').addClass('d-none');
    }

    // ===== 添加书籍功能 =====

    // 模态框显示时清空表单
    $('#addBookModal').on('shown.bs.modal', function() {
        clearBookForm();
        $('#bookTitle').focus();
    });

    // 书籍搜索模态框关闭时清理提示框
    $('#bookSearchModal').on('hidden.bs.modal', function() {
        console.log('模态框关闭事件触发，执行清理');

        // 清除所有自定义tooltip
        $('.book-tooltip').remove();
        $('.tooltip-container').remove();
        $('[data-bs-toggle="tooltip"]').tooltip('dispose');

        // 移除所有绑定的事件
        $(document).off('mouseleave', '.book-tooltip');
        $(document).off('click', '.book-tooltip');

        // 强制清理所有模态框和遮罩层
        cleanupAllModals();

        // 重置当前模态框实例
        currentModalInstance = null;
    });

    // 为所有模态框添加关闭事件处理
    $('.modal').on('hidden.bs.modal', function() {
        const modalId = $(this).attr('id');
        console.log('模态框关闭:', modalId);

        // 清理所有模态框和遮罩层
        cleanupAllModals();

        // 重置当前模态框实例
        currentModalInstance = null;
    });

    // 书籍搜索模态框打开时准备UI
    $('#bookSearchModal').on('shown.bs.modal', function() {
        // 清空搜索输入框并聚焦
        $('#bookSearchInput').val('').focus();

        // 确保搜索输入框获得焦点
        setTimeout(function() {
            $('#bookSearchInput').focus();
        }, 300);
    });

    // 保存按钮点击事件
    $('#saveBookBtn').on('click', function() {
        // 调用添加书籍功能
        addBook();
    });

    // 记录表单值
    function logFormValues() {
        // 获取表单并记录所有字段值
        const form = document.getElementById('simpleAddBookForm');

        if (form) {
            // 验证ID唯一性
            const idCounts = {
                'bookUrl': document.querySelectorAll('#bookUrl').length,
                'currentBookUrl': document.querySelectorAll('#currentBookUrl').length,
                'newBookUrl': document.querySelectorAll('#newBookUrl').length,
                'addBookUrl': document.querySelectorAll('#addBookUrl').length
            };

            if (Object.values(idCounts).some(count => count > 1)) {
                console.warn('检测到重复ID:', idCounts);
            }
        }
    }

    // 清空表单
    function clearBookForm() {
        $('#bookTitle, #bookType, #addBookUrl, #bookDescription').val('');
    }

    // 添加书籍
    function addBook() {
        // 检查用户是否已登录
        if (!isAuthenticated()) {
            showToast('您需要登录才能添加书籍', 'error');
            handleUnauthenticated();
            return;
        }

        // 读取表单值 - 使用新的ID
        const titleValue = $('#bookTitle').val().trim();
        const typeValue = $('#bookType').val().trim();
        const urlValue = $('#addBookUrl').val().trim(); // 使用新ID
        const descValue = $('#bookDescription').val().trim();

        // 用户输入的数据
        const bookData = {
            title: titleValue,
            type: typeValue,
            url: urlValue,
            description: descValue
        };

        // 基本验证
        if (!bookData.title) {
            showToast('请输入书籍标题', 'warning');
            return;
        }

        // URL验证 - 显示警告但不阻止
        if (!bookData.url) {
            showToast('URL为空，将使用空值提交', 'warning');
        }

        // 获取授权令牌
        const token = getAuthToken();
        if (!token) {
            showToast('您需要登录才能添加书籍', 'error');
            handleUnauthenticated();
            return;
        }

        // 禁用按钮，防止重复提交
        const $saveBtn = $('#saveBookBtn');
        $saveBtn.prop('disabled', true).text('保存中...');

        // 发送请求
        $.ajax({
            url: '/api/books',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
                'Authorization': token
            },
            data: JSON.stringify(bookData),
            success: function(response) {
                showToast('书籍添加成功', 'success');

                // 隐藏模态框并清空表单
                $('#addBookModal').modal('hide');

                // 使用通用函数清理模态框
                cleanupModal('addBookModal');

                clearBookForm();
            },
            error: function(error) {
                console.error('添加失败:', error.status, error.statusText);
                showToast(`添加失败: ${error.responseJSON?.message || '未知错误'}`, 'error');
            },
            complete: function() {
                // 恢复按钮状态
                $saveBtn.prop('disabled', false).text('保存');
            }
        });
    }

    // 获取授权令牌
    function getAuthToken() {
        // 使用common.js中的标准化token函数
        try {
            const token = localStorage.getItem('token');
            // 确保token不为空且不是"undefined"或"null"字符串
            if (!token || token === 'undefined' || token === 'null') {
                return null;
            }
            
            // 检查normalizeToken函数是否可用，不可用时自己处理
            if (typeof normalizeToken === 'function') {
                return normalizeToken(token);
            } else {
                // 内部实现token标准化
                if (token.toLowerCase().startsWith('bearer ')) {
                    return token.substring(7);
                }
                return token;
            }
        } catch (error) {
            console.error('获取认证令牌失败:', error);
            return null;
        }
    }

    // 格式化认证令牌为API适用的格式 - 根据API要求使用原始token
    function formatAuthHeader(token) {
        if (!token) return null;
        return token;
    }

    // 检查用户是否已登录
    function isAuthenticated() {
        return !!getAuthToken();
    }

    // 处理未授权状态
    function handleUnauthenticated() {
        showToast('请先登录系统', 'warning');

        // 保存当前URL，登录后可返回
        localStorage.setItem('redirect_after_login', window.location.href);

        // 提示后延迟跳转
        setTimeout(function() {
            window.location.href = '/auth/login';
        }, 1500);
    }

    // 验证用户登录状态并更新UI - 增强的函数实现
    function verifyLoginStatus() {
        console.log("正在验证登录状态...");
        
        // 先检查本地token是否存在
        const token = getAuthToken();
        if (!token) {
            console.warn("没有找到有效token");
            showLoginPrompt();
            return false;
        }

        // 使用增强的验证机制
        validateCurrentToken()
            .then(isValid => {
                if (!isValid) {
                    console.warn("Token验证失败，无效或已过期");
                    // 清除无效的token
                    localStorage.removeItem('token');
                    showLoginPrompt('您的登录已过期，请重新登录');
                    return false;
                }
                
                // Token有效，继续验证用户信息
                return fetch('/api/user/current', {
                    headers: {
                        'Authorization': token
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`验证请求失败: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    console.log("验证成功，响应:", response.code);
                    // 确保响应格式正确
                    if (response && response.code === 200 && response.data) {
                        localStorage.setItem('currentUser', JSON.stringify(response.data));
                        // 更新UI以显示已登录状态
                        updateUIForLoggedInUser();
                        return true;
                    }
                    throw new Error('响应格式错误');
        })
        .catch(error => {
                    console.error('验证用户信息失败:', error);
                    
                    if (error.message.includes('401')) {
                        // 清除无效的token
                        clearAuthTokens();
                        showLoginPrompt('您的登录已过期，请重新登录');
                    } else {
                        // 其他错误可能是网络问题，不清除token
                        showLoginPrompt('验证登录状态失败，请检查网络连接');
                    }
                    return false;
                });
            })
            .catch(error => {
                console.error('Token验证过程出错:', error);
                showLoginPrompt('验证登录状态时出错，请重试');
                return false;
            });
    }

    // 显示登录提示
    function showLoginPrompt(message) {
        const msg = message || '您需要登录才能使用此功能';

        // 显示提示消息
        showMessage(msg);
        showToast(msg, 'warning');

        // 添加登录按钮到搜索结果区域
        const $results = $('#bookSearchResults');
        if ($results.length > 0) {
            $results.html(`
                <div class="text-center my-4">
                    <p class="mb-3">${msg}</p>
                    <a href="/login" class="btn btn-primary">
                        <i class="fa fa-sign-in"></i> 登录系统
                    </a>
                </div>
            `);
        }

        return false;
    }

    // 更新UI以显示已登录状态
    function updateUIForLoggedInUser() {
        // 移除登录提示
        $('.auth-notice').remove();
    }

    // 清除认证令牌
    function clearAuthTokens() {
        console.log('清除认证令牌');
        
        try {
            // 清除localStorage中的token
            localStorage.removeItem('token');
            localStorage.removeItem('currentUser');

            // 清除cookie中的token (设置过期时间为过去)
            document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            
            console.log('令牌已清除');
        } catch (error) {
            console.error('清除令牌失败:', error);
        }
    }

    // 检查用户认证状态并显示相应UI
    function checkAuthentication() {
        console.log('检查认证状态');
        // 使用已有的verifyLoginStatus函数进行验证
        return verifyLoginStatus();
    }

    // HTML转义
    function escapeHtml(text) {
        if (!text) return '';
        return $('<div>').text(text).html();
    }

    // 全局变量，跟踪当前模态框实例
    let currentModalInstance = null;

    // 防抖函数 - 避免短时间内多次调用
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // 清理操作状态跟踪
    let isCleaningModals = false;
    let lastCleanupTime = 0;

    // 彻底清理所有模态框和遮罩层
    function cleanupAllModals() {
        // 防止重复调用，至少间隔100ms
        const now = Date.now();
        if (isCleaningModals || (now - lastCleanupTime < 100)) {
            console.log('清理操作正在进行中或刚刚执行过，跳过此次调用');
            return;
        }

        isCleaningModals = true;
        lastCleanupTime = now;

        console.log('执行全局模态框清理');

        try {
            // 销毁当前模态框实例
            if (currentModalInstance) {
                try {
                    currentModalInstance.dispose();
                } catch (e) {
                    console.warn('销毁模态框实例失败:', e);
                }
                currentModalInstance = null;
            }

            // 移除所有遮罩层 - 使用多种方法确保彻底清理
            try {
                // 方法1: jQuery
                $('.modal-backdrop').remove();

                // 方法2: 原生DOM
                document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                    try {
                        backdrop.parentNode.removeChild(backdrop);
                    } catch (e) {}
                });

                // 方法3: 直接修改HTML
                const backdrops = document.querySelectorAll('.modal-backdrop');
                for (let i = 0; i < backdrops.length; i++) {
                    backdrops[i].outerHTML = '';
                }
            } catch (e) {
                console.warn('移除遮罩层失败:', e);
            }

            // 重置body样式
            try {
                $('body').removeClass('modal-open').css({
                    'overflow': '',
                    'padding-right': '',
                    'height': ''
                });

                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('padding-right');
                document.body.style.removeProperty('overflow');
                document.body.style.removeProperty('height');
            } catch (e) {
                console.warn('重置body样式失败:', e);
            }

            // 确保所有模态框都已关闭
            try {
                $('.modal.show').removeClass('show').css('display', 'none');

                document.querySelectorAll('.modal.show').forEach(modal => {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                });
            } catch (e) {
                console.warn('关闭模态框失败:', e);
            }

            // 清理所有tooltip
            try {
                $('.tooltip-container, .book-tooltip').remove();
                
                // 只在有tooltip函数时调用
                if (typeof $.fn.tooltip === 'function') {
                    $('[data-bs-toggle="tooltip"]').tooltip('dispose');
                }
            } catch (e) {
                console.warn('清理tooltip失败:', e);
            }

            console.log('模态框清理完成');
        } catch (error) {
            console.error('清理模态框过程中发生错误:', error);
        } finally {
            // 无论成功失败，都重置清理状态
            isCleaningModals = false;
        }
    }

    // 创建防抖版本的清理函数，用于事件监听器
    const debouncedCleanup = debounce(cleanupAllModals, 100);

    // 强制清理模态框背景和相关样式的工具函数
    function cleanupModal(modalId) {
        console.log('执行彻底模态框清理:', modalId);

        // 分阶段清理，确保完全清除
        [0, 100, 300].forEach(delay => {
            setTimeout(() => {
                try {
                    // 1. 处理Bootstrap模态框实例
                    if (modalId) {
                        const modalElement = document.getElementById(modalId);
                        if (modalElement) {
                            try {
                                const instance = bootstrap.Modal.getInstance(modalElement);
                                if (instance) {
                                    instance.hide();
                                    try { instance.dispose(); } catch(e) {}
                                }
                            } catch(e) {
                                console.warn('处理模态框实例失败:', e);
                            }
                        }
                    }

                    // 2. 清理DOM元素
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                        try {
                            backdrop.parentNode.removeChild(backdrop);
                        } catch(e) {}
                    });

                    // 3. 重置body状态
                    document.body.classList.remove('modal-open');
                    document.body.style.removeProperty('padding-right');
                    document.body.style.removeProperty('overflow');
                    document.body.style.height = '';

                    // 4. 使用jQuery进行额外清理
                    if (typeof $ !== 'undefined') {
                        $('.modal-backdrop').remove();
                        $('body').removeClass('modal-open')
                                .removeClass('modal-showing')
                                .css({
                                    'overflow': '',
                                    'padding-right': '',
                                    'height': ''
                                });

                        // 重置所有模态框状态
                        $('.modal').removeClass('show')
                                 .css('display', 'none')
                                 .attr('aria-hidden', 'true')
                                 .removeAttr('aria-modal')
                                 .removeAttr('role');
                    }

                    // 5. 清理相关元素
                    $('.tooltip-container, .book-tooltip').remove();
                    $('[data-bs-toggle="tooltip"]').tooltip('dispose');

                } catch (e) {
                    console.error('模态框清理过程中发生错误:', e);
                    // 确保基本清理完成
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                }
            }, delay);
        });
    }

    // 为所有模态框的关闭按钮添加点击监听器
    document.addEventListener('click', function(e) {
        // 通用模态框关闭逻辑 - 捕获所有关闭按钮
        if (e.target && (
            e.target.classList.contains('btn-close') ||
            e.target.classList.contains('close') ||
            e.target.closest('.modal .btn-close') ||
            e.target.closest('.modal .close') ||
            e.target.hasAttribute('data-bs-dismiss') && e.target.getAttribute('data-bs-dismiss') === 'modal' ||
            e.target.hasAttribute('data-dismiss') && e.target.getAttribute('data-dismiss') === 'modal'
        )) {
            // 获取当前模态框
            const modal = e.target.closest('.modal');
            if (modal) {
                const modalId = modal.id;
                console.log('Modal close button clicked:', modalId);
                cleanupModal(modalId);
            }
        }
    }, true);

    // 添加validateCurrentToken函数的兼容实现，如果common.js中未定义时使用
    if (typeof validateCurrentToken !== 'function') {
        console.warn('未找到validateCurrentToken函数，使用兼容实现');
        
        // Token有效性检查的兼容实现
        async function validateCurrentToken() {
            try {
                const token = localStorage.getItem('token');
                if (!token) return false;
                
                // 实现一个简单的格式化函数
                const normalizeTokenLocal = (t) => {
                    if (!t) return null;
                    if (t.toLowerCase().startsWith('bearer ')) {
                        return t.substring(7);
                    }
                    return t;
                };
                
                const normalizedToken = normalizeTokenLocal(token);
                
                // 检查token长度是否合理
                if (!normalizedToken || normalizedToken.length < 10) {
                    console.warn('Token格式不正确或长度不足');
                    return false;
                }
                
                console.log('使用兼容实现进行token验证');
                
                // 调用验证接口
                const response = await fetch('/api/user/validate', {
                    headers: {
                        'Authorization': normalizedToken
                    }
                });
                
                if (!response.ok) {
                    console.warn('Token验证请求失败，状态码:', response.status);
                    return false;
                }
                
                const data = await response.json();
                return data && data.code === 200;
            } catch (error) {
                console.error('兼容实现验证token时出错:', error);
                return false;
            }
        }
        
        // 设置为全局函数
        window.validateCurrentToken = validateCurrentToken;
    }

    // 添加normalizeToken函数的兼容实现，如果common.js中未定义时使用
    if (typeof normalizeToken !== 'function') {
        console.warn('未找到normalizeToken函数，使用兼容实现');
        
        // Token标准化的兼容实现
        function normalizeToken(token) {
            if (!token) return null;
            if (typeof token !== 'string') return null;
            
            // 移除可能存在的Bearer前缀
            if (token.toLowerCase().startsWith('bearer ')) {
                return token.substring(7);
            }
            return token;
        }
        
        // 设置为全局函数
        window.normalizeToken = normalizeToken;
    }
}) // End of IIFE

// 全局showToast函数兼容性
if (typeof showToast !== 'function') {
    window.showToast = function(message, type) {
        console.log(`[${type || 'info'}] ${message}`);
        if (typeof window.alert === 'function') {
            window.alert(message);
        }
    };
}

// 添加全局紧急清理函数
window.emergencyModalCleanup = function() {
    console.log('执行紧急模态框清理');

    // 移除所有遮罩层
    $('.modal-backdrop').remove();
    document.querySelectorAll('.modal-backdrop').forEach(el => {
        try { el.parentNode.removeChild(el); } catch(e) {}
    });

    // 重置body样式
    $('body').removeClass('modal-open').css({
        'overflow': '',
        'padding-right': '',
        'height': ''
    });
    document.body.classList.remove('modal-open');
    document.body.style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');

    // 关闭所有模态框
    $('.modal').removeClass('show').css('display', 'none');
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
        modal.style.display = 'none';
    });

    // 移除所有tooltip
    $('.tooltip-container, .book-tooltip').remove();
    $('[data-bs-toggle="tooltip"]').tooltip('dispose');

    showToast('页面已重置，如有问题请刷新页面', 'success');
    return '页面已重置';
};

/**
 * 对话状态管理模块
 * 负责管理和同步对话相关的状态，包括书籍URL、知识点ID等
 */
const ChatState = {
    // 版本号，用于兼容性检查
    version: '1.1.0',

    // 主界面状态
    current: {
        bookUrl: '',
        knowId: '',
        title: '新对话',
        createdAt: null,
        updatedAt: null
    },

    // 新建对话状态
    newChat: {
        bookUrl: '',
        knowId: '',
        title: '新建对话',
        createdAt: null,
        updatedAt: null
    },

    // 历史记录
    histories: [],

    // 状态变更回调函数
    callbacks: {
        onStateChange: null,
        onCurrentChange: null,
        onNewChatChange: null
    },

    /**
     * 初始化状态管理模块
     * @param {Object} options - 配置选项
     * @param {Function} options.onStateChange - 状态变更回调
     * @param {Function} options.onCurrentChange - 当前对话状态变更回调
     * @param {Function} options.onNewChatChange - 新建对话状态变更回调
     * @returns {Object} - 当前实例，支持链式调用
     */
    init(options = {}) {
        console.log('初始化ChatState模块，版本:', this.version);

        // 设置回调函数
        if (options.onStateChange && typeof options.onStateChange === 'function') {
            this.callbacks.onStateChange = options.onStateChange;
        }

        if (options.onCurrentChange && typeof options.onCurrentChange === 'function') {
            this.callbacks.onCurrentChange = options.onCurrentChange;
        }

        if (options.onNewChatChange && typeof options.onNewChatChange === 'function') {
            this.callbacks.onNewChatChange = options.onNewChatChange;
        }

        try {
            // 加载保存的状态
            this.loadFromStorage();

            // 绑定事件监听
            this.bindEvents();

            // 更新UI
            this.updateUI('both');

            console.log('ChatState初始化完成', {
                current: this.current,
                newChat: this.newChat,
                historiesCount: this.histories.length
            });

            // 触发状态变更回调
            this._triggerCallback('onStateChange', {
                type: 'init',
                current: this.current,
                newChat: this.newChat
            });
        } catch (error) {
            console.error('ChatState初始化失败:', error);

            // 初始化失败时，使用默认值
            this._resetToDefaults();
        }

        return this;
    },

    /**
     * 保存书籍URL
     * @param {string} url - 书籍URL
     * @param {string} context - 上下文，'current'或'new'
     * @returns {boolean} - 是否保存成功
     */
    saveBookUrl(url, context = 'current') {
        try {
            console.log(`保存书籍URL: ${url || '空'}, 上下文: ${context}`);

            // 确保url是字符串
            const safeUrl = (url || '').toString();

            // 根据上下文更新状态
            if (context === 'new') {
                this.newChat.bookUrl = safeUrl;
                this.newChat.updatedAt = new Date().toISOString();

                // 触发新建对话状态变更回调
                this._triggerCallback('onNewChatChange', {
                    type: 'bookUrl',
                    value: safeUrl,
                    state: this.newChat
                });
            } else {
                this.current.bookUrl = safeUrl;
                this.current.updatedAt = new Date().toISOString();

                // 触发当前对话状态变更回调
                this._triggerCallback('onCurrentChange', {
                    type: 'bookUrl',
                    value: safeUrl,
                    state: this.current
                });
            }

            // 保存到存储
            this.saveToStorage();

            // 更新UI
            this.updateUI(context);

            return true;
        } catch (error) {
            console.error(`保存书籍URL失败:`, error);
            return false;
        }
    },

    /**
     * 保存知识点ID
     * @param {string} id - 知识点ID
     * @param {string} context - 上下文，'current'或'new'
     * @returns {boolean} - 是否保存成功
     */
    saveKnowId(id, context = 'current') {
        try {
            console.log(`保存知识点ID: ${id || '空'}, 上下文: ${context}`);

            // 确保id是字符串
            const safeId = (id || '').toString();

            // 根据上下文更新状态
            if (context === 'new') {
                this.newChat.knowId = safeId;
                this.newChat.updatedAt = new Date().toISOString();

                // 触发新建对话状态变更回调
                this._triggerCallback('onNewChatChange', {
                    type: 'knowId',
                    value: safeId,
                    state: this.newChat
                });
            } else {
                this.current.knowId = safeId;
                this.current.updatedAt = new Date().toISOString();

                // 触发当前对话状态变更回调
                this._triggerCallback('onCurrentChange', {
                    type: 'knowId',
                    value: safeId,
                    state: this.current
                });
            }

            // 保存到存储
            this.saveToStorage();

            // 更新UI
            this.updateUI(context);

            return true;
        } catch (error) {
            console.error(`保存知识点ID失败:`, error);
            return false;
        }
    },

    /**
     * 保存对话标题
     * @param {string} title - 对话标题
     * @param {string} context - 上下文，'current'或'new'
     * @returns {boolean} - 是否保存成功
     */
    saveTitle(title, context = 'current') {
        try {
            console.log(`保存对话标题: ${title || '空'}, 上下文: ${context}`);

            // 确保title是字符串
            const safeTitle = (title || '新对话').toString();

            // 根据上下文更新状态
            if (context === 'new') {
                this.newChat.title = safeTitle;
                this.newChat.updatedAt = new Date().toISOString();

                // 触发新建对话状态变更回调
                this._triggerCallback('onNewChatChange', {
                    type: 'title',
                    value: safeTitle,
                    state: this.newChat
                });
            } else {
                this.current.title = safeTitle;
                this.current.updatedAt = new Date().toISOString();

                // 触发当前对话状态变更回调
                this._triggerCallback('onCurrentChange', {
                    type: 'title',
                    value: safeTitle,
                    state: this.current
                });
            }

            // 保存到存储
            this.saveToStorage();

            // 更新UI
            this.updateUI(context);

            return true;
        } catch (error) {
            console.error(`保存对话标题失败:`, error);
            return false;
        }
    },

    /**
     * 保存状态到本地存储
     * @returns {boolean} - 是否保存成功
     */
    saveToStorage() {
        try {
            // 添加时间戳
            const timestamp = new Date().toISOString();

            // 确保时间戳存在
            if (!this.current.createdAt) {
                this.current.createdAt = timestamp;
            }
            this.current.updatedAt = timestamp;

            if (!this.newChat.createdAt) {
                this.newChat.createdAt = timestamp;
            }
            this.newChat.updatedAt = timestamp;

            // 保存到localStorage
            localStorage.setItem('currentChat', JSON.stringify(this.current));
            localStorage.setItem('newChat', JSON.stringify(this.newChat));
            localStorage.setItem('chatHistories', JSON.stringify(this.histories));

            console.log('状态已保存到存储', {
                current: this.current,
                newChat: this.newChat,
                historiesCount: this.histories.length
            });

            return true;
        } catch (error) {
            console.error('保存状态到存储失败:', error);
            return false;
        }
    },

    /**
     * 从本地存储加载状态
     * @returns {boolean} - 是否加载成功
     */
    loadFromStorage() {
        try {
            // 加载主界面状态
            const savedCurrent = localStorage.getItem('currentChat');
            if (savedCurrent) {
                const parsedCurrent = JSON.parse(savedCurrent);
                this.current = {...this.current, ...parsedCurrent};
            }

            // 加载新建对话状态
            const savedNew = localStorage.getItem('newChat');
            if (savedNew) {
                const parsedNew = JSON.parse(savedNew);
                this.newChat = {...this.newChat, ...parsedNew};
            }

            // 加载历史记录
            const savedHistories = localStorage.getItem('chatHistories');
            if (savedHistories) {
                this.histories = JSON.parse(savedHistories) || [];
            }

            console.log('从存储加载状态', {
                current: this.current,
                newChat: this.newChat,
                historiesCount: this.histories.length
            });

            return true;
        } catch (error) {
            console.error('加载保存的对话状态失败:', error);

            // 加载失败时，使用默认值
            this._resetToDefaults();

            return false;
        }
    },

    /**
     * 更新UI元素
     * @param {string} context - 上下文，'current'、'new'或'both'
     */
    updateUI(context = 'both') {
        try {
            console.log(`更新UI, 上下文: ${context}`);

            // 根据上下文更新UI
            if (context === 'current' || context === 'both') {
                // 更新当前对话UI
                const $currentBookUrl = $('#currentBookUrl');
                const $knowId = $('#knowId');
                const $currentChatTitle = $('#currentChatTitle');

                if ($currentBookUrl.length) {
                    $currentBookUrl.val(this.current.bookUrl || '');
                }

                if ($knowId.length) {
                    $knowId.val(this.current.knowId || '');
                }

                if ($currentChatTitle.length && this.current.title) {
                    $currentChatTitle.text(this.current.title);
                }
            }

            if (context === 'new' || context === 'both') {
                // 更新新建对话UI
                const $newBookUrl = $('#newBookUrl');
                const $newKnowId = $('#newKnowId');

                if ($newBookUrl.length) {
                    $newBookUrl.val(this.newChat.bookUrl || '');
                }

                if ($newKnowId.length) {
                    $newKnowId.val(this.newChat.knowId || '');
                }
            }
        } catch (error) {
            console.error('更新UI失败:', error);
        }
    },

    /**
     * 绑定事件监听
     */
    bindEvents() {
        try {
            // 监听主界面输入变化
            $('#currentBookUrl').off('input change').on('input change', (e) => {
                this.current.bookUrl = e.target.value || '';
                this.saveToStorage();
            });

            $('#knowId').off('input change').on('input change', (e) => {
                this.current.knowId = e.target.value || '';
                this.saveToStorage();
            });

            // 监听新建对话输入变化
            $('#newBookUrl').off('input change').on('input change', (e) => {
                this.newChat.bookUrl = e.target.value || '';
                this.saveToStorage();
            });

            $('#newKnowId').off('input change').on('input change', (e) => {
                this.newChat.knowId = e.target.value || '';
                this.saveToStorage();
            });

            // 定期保存状态（作为备份机制）
            this._setupAutoSave();

            console.log('事件监听已绑定');
        } catch (error) {
            console.error('绑定事件监听失败:', error);
        }
    },

    /**
     * 设置自动保存
     * @private
     */
    _setupAutoSave() {
        // 清除可能存在的旧定时器
        if (this._autoSaveTimer) {
            clearInterval(this._autoSaveTimer);
        }

        // 设置新定时器，每30秒保存一次
        this._autoSaveTimer = setInterval(() => {
            this.saveToStorage();
        }, 30000);
    },

    /**
     * 确认新建对话
     * @returns {boolean} - 是否确认成功
     */
    confirmNewChat() {
        try {
            console.log('确认新建对话，新建对话状态:', this.newChat);

            // 验证新建对话状态
            if (!this.newChat.bookUrl && !this.newChat.knowId) {
                console.warn('新建对话状态无效，书籍URL和知识点ID都为空');
            }

            // 将当前对话添加到历史记录
            if (this.current.bookUrl || this.current.knowId) {
                this._addToHistory(this.current);
            }

            // 将新建对话的设置复制到当前对话
            this.current.bookUrl = this.newChat.bookUrl || '';
            this.current.knowId = this.newChat.knowId || '';
            this.current.title = this.newChat.title || '新对话';
            this.current.createdAt = new Date().toISOString();
            this.current.updatedAt = new Date().toISOString();

            // 保存并更新UI
            this.saveToStorage();
            this.updateUI('both');

            // 重置新建对话状态
            this.newChat = {
                bookUrl: '',
                knowId: '',
                title: '新建对话',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // 触发状态变更回调
            this._triggerCallback('onStateChange', {
                type: 'confirmNewChat',
                current: this.current,
                newChat: this.newChat
            });

            console.log('已确认新建对话，当前状态:', this.current);

            return true;
        } catch (error) {
            console.error('确认新建对话失败:', error);
            return false;
        }
    },

    /**
     * 添加对话到历史记录
     * @param {Object} chat - 对话状态
     * @private
     */
    _addToHistory(chat) {
        if (!chat || (!chat.bookUrl && !chat.knowId)) {
            return;
        }

        // 创建历史记录条目
        const historyItem = {
            ...chat,
            id: this._generateId(),
            archivedAt: new Date().toISOString()
        };

        // 添加到历史记录
        this.histories.unshift(historyItem);

        // 限制历史记录数量
        if (this.histories.length > 50) {
            this.histories = this.histories.slice(0, 50);
        }

        // 保存到存储
        localStorage.setItem('chatHistories', JSON.stringify(this.histories));
    },

    /**
     * 生成唯一ID
     * @returns {string} - 唯一ID
     * @private
     */
    _generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    },

    /**
     * 触发回调函数
     * @param {string} callbackName - 回调函数名称
     * @param {Object} data - 回调数据
     * @private
     */
    _triggerCallback(callbackName, data) {
        try {
            const callback = this.callbacks[callbackName];
            if (callback && typeof callback === 'function') {
                callback(data);
            }
        } catch (error) {
            console.error(`触发回调${callbackName}失败:`, error);
        }
    },

    /**
     * 重置为默认值
     * @private
     */
    _resetToDefaults() {
        this.current = {
            bookUrl: '',
            knowId: '',
            title: '新对话',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.newChat = {
            bookUrl: '',
            knowId: '',
            title: '新建对话',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.histories = [];

        console.log('已重置为默认值');
    },

    /**
     * 获取指定上下文的状态
     * @param {string} context - 上下文，'current'或'new'
     * @returns {Object} - 状态对象
     */
    getState(context = 'current') {
        return context === 'new' ? {...this.newChat} : {...this.current};
    },

    /**
     * 获取历史记录
     * @param {number} limit - 限制数量
     * @returns {Array} - 历史记录数组
     */
    getHistories(limit = 0) {
        if (limit > 0) {
            return [...this.histories.slice(0, limit)];
        }
        return [...this.histories];
    },

    /**
     * 清除所有数据
     * @returns {boolean} - 是否清除成功
     */
    clearAll() {
        try {
            this._resetToDefaults();
            localStorage.removeItem('currentChat');
            localStorage.removeItem('newChat');
            localStorage.removeItem('chatHistories');

            this.updateUI('both');

            console.log('已清除所有数据');

            // 触发状态变更回调
            this._triggerCallback('onStateChange', {
                type: 'clearAll',
                current: this.current,
                newChat: this.newChat
            });

            return true;
        } catch (error) {
            console.error('清除所有数据失败:', error);
            return false;
        }
    }
};

/**
 * 书籍搜索模块初始化函数
 * 负责初始化书籍搜索相关功能
 */
function initBookSearchModule() {
    console.log('初始化书籍搜索模块...');

    try {
        // 添加紧急清理按钮（隐藏在页面底部，仅在需要时使用）
        addEmergencyButton();

        // 初始清理，确保页面加载时没有残留的遮罩层
        setTimeout(function() {
            if (typeof cleanupAllModals === 'function') {
                cleanupAllModals();
            } else {
                console.warn('cleanupAllModals函数不可用，跳过初始清理');
            }
        }, 500);

        // 初始化ChatState模块 - 先检查是否存在
        if (typeof ChatState !== 'undefined') {
            try {
                initChatState();
            } catch (error) {
                console.warn('ChatState模块初始化失败:', error);
            }
        } else {
            console.warn('ChatState模块未定义 - 将使用备用状态管理');
            // 在这里可以实现一个简单的备用状态管理
        }

        // 检查并验证认证状态 - 确保函数存在
        if (typeof verifyLoginStatus === 'function') {
            verifyLoginStatus();
        } else {
            console.warn('verifyLoginStatus函数未定义 - 将跳过认证检查');
            // 实现一个简单的备用验证函数
            fallbackAuthCheck();
        }

        // 添加键盘快捷键
        setupKeyboardShortcuts();

        // 添加页面可见性变化监听
        setupVisibilityChangeListener();

        // 添加网络状态监听
        setupNetworkStatusListener();

        console.log('书籍搜索模块初始化完成');
    } catch (error) {
        console.error('书籍搜索模块初始化失败:', error);
    }
}

/**
 * 备用的认证检查函数
 * 当verifyLoginStatus不可用时使用
 */
function fallbackAuthCheck() {
    console.log('使用备用认证检查');
    try {
        const token = getAuthToken();
        if (!token) {
            console.warn('没有有效token');
            return false;
        }
        
        console.log('检测到token，认为用户已登录');
        return true;
    } catch (error) {
        console.error('备用认证检查失败:', error);
        return false;
    }
}

/**
 * 添加紧急清理按钮
 */
function addEmergencyButton() {
    try {
        // 检查是否已存在
        if (document.getElementById('emergency-cleanup-btn')) {
            return;
        }

        const emergencyButton = document.createElement('button');
        emergencyButton.id = 'emergency-cleanup-btn';
        emergencyButton.textContent = '紧急修复';
        emergencyButton.title = '如果页面出现遮罩层问题，点击此按钮修复';
        emergencyButton.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            z-index: 9999;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            opacity: 0.7;
            cursor: pointer;
            transition: opacity 0.3s ease;
        `;

        // 添加悬停效果
        emergencyButton.addEventListener('mouseenter', function() {
            this.style.opacity = '1';
        });

        emergencyButton.addEventListener('mouseleave', function() {
            this.style.opacity = '0.7';
        });

        // 添加点击事件
        emergencyButton.addEventListener('click', function() {
            window.emergencyModalCleanup();
            showToast('页面已重置', 'success');
        });

        document.body.appendChild(emergencyButton);
        console.log('紧急修复按钮已添加');
    } catch (error) {
        console.error('添加紧急修复按钮失败:', error);
    }
}

/**
 * 初始化ChatState模块
 */
function initChatState() {
    if (typeof window.ChatState === 'undefined') {
        console.error('ChatState模块未定义!');
        
        // 创建一个简单的对象作为备用
        window.ChatState = {
            saveBookUrl: function(url, context) {
                console.log(`[备用ChatState] 保存书籍URL: ${url || ''}, 上下文: ${context || 'current'}`);
                try {
                    // 直接更新相关DOM元素作为备选方案
                    if (context === 'new') {
                        $('#newBookUrl').val(url || '');
                    } else {
                        $('#currentBookUrl').val(url || '');
                    }
                } catch (error) {
                    console.error('备用ChatState保存URL失败:', error);
                }
            },
            init: function() {
                console.log('[备用ChatState] 初始化');
                return this;
            }
        };
        
        return window.ChatState.init();
    }
    
    try {
        // 设置回调函数
        return ChatState.init({
            onStateChange: function(data) {
                console.log('ChatState状态变更:', data.type);
            },
            onCurrentChange: function(data) {
                console.log('当前对话状态变更:', data.type, data.value);
            },
            onNewChatChange: function(data) {
                console.log('新建对话状态变更:', data.type, data.value);
            }
        });
    } catch (error) {
        console.error('ChatState初始化失败:', error);
        
        // 出错时返回一个最小的可用对象
        return {
            saveBookUrl: function(url) {
                console.log(`[错误恢复] 保存书籍URL: ${url || ''}`);
            }
        };
    }
}

/**
 * 设置键盘快捷键
 */
function setupKeyboardShortcuts() {
    try {
        // 添加全局键盘快捷键
        document.addEventListener('keydown', function(e) {
            // 检查是否按下ESC键
            if (e.key === 'Escape' || e.keyCode === 27) {
                // 检查是否有可见的模态框
                if ($('.modal.show').length > 0) {
                    console.log('检测到ESC键，关闭模态框');
                    cleanupAllModals();
                }
            }

            // Ctrl+F 或 Command+F (Mac) 打开搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                // 检查是否在搜索模态框内
                if ($('#bookSearchModal').hasClass('show')) {
                    // 聚焦搜索输入框
                    e.preventDefault();
                    $('#bookSearchInput').focus();
                }
            }

            // Enter键在搜索输入框中触发搜索
            if (e.key === 'Enter' && document.activeElement === document.getElementById('bookSearchInput')) {
                e.preventDefault();
                $('#bookSearchButton').click();
            }
        });

        console.log('键盘快捷键已设置');
    } catch (error) {
        console.error('设置键盘快捷键失败:', error);
    }
}

/**
 * 设置页面可见性变化监听
 */
function setupVisibilityChangeListener() {
    try {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                console.log('页面变为可见，检查模态框状态');

                // 页面重新变为可见时，检查模态框状态
                if ($('.modal-backdrop').length > 0 && $('.modal.show').length === 0) {
                    console.log('检测到残留的遮罩层，执行清理');
                    cleanupAllModals();
                }
            }
        });

        console.log('页面可见性变化监听已设置');
    } catch (error) {
        console.error('设置页面可见性变化监听失败:', error);
    }
}

/**
 * 设置网络状态监听
 */
function setupNetworkStatusListener() {
    try {
        window.addEventListener('online', function() {
            console.log('网络已连接');
            showToast('网络已连接', 'success');

            // 网络恢复后，重新验证登录状态
            verifyLoginStatus();
        });

        window.addEventListener('offline', function() {
            console.log('网络已断开');
            showToast('网络已断开，部分功能可能不可用', 'warning');
        });

        console.log('网络状态监听已设置');
    } catch (error) {
        console.error('设置网络状态监听失败:', error);
    }
}

// 确保在DOM完全加载后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，初始化书籍搜索组件');

    // 使用requestIdleCallback或setTimeout延迟初始化，避免阻塞页面渲染
    if (window.requestIdleCallback) {
        requestIdleCallback(function() {
            initBookSearchModule();
        });
    } else {
        setTimeout(function() {
            initBookSearchModule();
        }, 100);
    }
});

    // ESC键监听已在setupKeyboardShortcuts函数中设置

    /**
     * 创建并显示书籍悬停提示
     * @param {HTMLElement} cell - 单元格元素
     * @param {MouseEvent} event - 鼠标事件
     */
    function showBookTooltip(cell, event) {
        try {
            // 如果正在显示此单元格的tooltip，则不重复创建
            if (cell.dataset.tooltipShown === 'true') return;

            // 获取行元素
            const row = cell.closest('tr');
            if (!row) return;

            // 获取书籍信息
            const title = row.querySelector('.book-title')?.textContent || '未知书籍';
            const type = row.cells[1]?.textContent || '未知类型';
            const hasChapter = row.querySelector('.chapter-badge') !== null;
            const chapterInfo = hasChapter ? row.querySelector('.chapter-badge').textContent : '';

            // 移除所有已存在的tooltip
            document.querySelectorAll('.tooltip-container').forEach(tooltip => {
                tooltip.remove();
            });

            // 尝试从book ID获取书籍数据
            let description = '暂无简介';
            let book = null;

            if (row.id && row.id.startsWith('book-')) {
                const index = parseInt(row.id.replace('book-', ''), 10);
                if (!isNaN(index) && window.currentSearchBooks && window.currentSearchBooks[index]) {
                    book = window.currentSearchBooks[index];
                    description = book.description || '暂无简介';
                }
            }

            // 创建tooltip内容
            const tooltipHTML = `
                <div class="book-tooltip">
                    <div class="book-tooltip-title">${escapeHtml(title)}</div>
                    <div class="book-tooltip-type">类型: ${escapeHtml(type)}</div>
                    ${hasChapter ? `<div class="book-tooltip-chapter">章节: ${escapeHtml(chapterInfo)}</div>` : ''}
                    <div class="${description === '暂无简介' ? 'book-tooltip-no-desc' : 'book-tooltip-desc'}">${escapeHtml(description)}</div>
                    ${book && book.url ? `<div class="book-tooltip-url">链接: ${escapeHtml(book.url)}</div>` : ''}
                </div>
            `;

            // 创建并添加tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip-container';
            tooltip.innerHTML = tooltipHTML;

            // 添加到body
            document.body.appendChild(tooltip);

            // 计算并设置位置
            positionTooltip(tooltip, cell, event);

            // 标记此单元格正在显示tooltip
            cell.dataset.tooltipShown = 'true';
            tooltip.dataset.forCell = `${row.rowIndex}-${cell.cellIndex}`;

            // 添加鼠标移出事件处理
            setupTooltipMouseEvents(tooltip, cell);

            // 添加点击事件 - 点击tooltip不会关闭它
            tooltip.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // 添加选择按钮点击事件
            if (book) {
                tooltip.querySelector('.book-tooltip').addEventListener('click', function(e) {
                    // 如果点击的是标题，选择此书籍
                    if (e.target.closest('.book-tooltip-title')) {
                        selectBook(book);
                    }
                });
            }
        } catch (error) {
            console.error('显示书籍提示失败:', error);
        }
    }

    /**
     * 设置tooltip的位置
     * @param {HTMLElement} tooltip - Tooltip元素
     * @param {HTMLElement} cell - 单元格元素
     * @param {MouseEvent} event - 鼠标事件
     */
    function positionTooltip(tooltip, cell, event) {
        try {
            // 获取单元格位置
            const cellRect = cell.getBoundingClientRect();

            // 获取视口尺寸
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 设置初始位置 - 优先在鼠标右侧显示
            let left = event.clientX + 15;
            let top = event.clientY;

            // 获取tooltip尺寸
            const tooltipWidth = 350; // 固定宽度
            const tooltipHeight = tooltip.offsetHeight || 200; // 预估高度

            // 检查是否会超出右侧边界
            if (left + tooltipWidth > viewportWidth) {
                // 如果超出右侧边界，则显示在鼠标左侧
                left = event.clientX - tooltipWidth - 15;

                // 如果左侧也放不下，则显示在单元格下方
                if (left < 0) {
                    left = Math.max(10, cellRect.left);
                    top = cellRect.bottom + 10;
                }
            }

            // 检查是否会超出底部边界
            if (top + tooltipHeight > viewportHeight) {
                // 如果超出底部边界，则向上调整
                top = Math.max(10, viewportHeight - tooltipHeight - 10);
            }

            // 设置tooltip位置
            tooltip.style.left = `${left}px`;
            tooltip.style.top = `${top}px`;

            // 添加淡入效果
            tooltip.style.opacity = '0';
            tooltip.style.transition = 'opacity 0.2s ease-in-out';

            // 强制重绘
            tooltip.offsetHeight;

            // 显示tooltip
            tooltip.style.opacity = '1';
        } catch (error) {
            console.error('设置tooltip位置失败:', error);

            // 使用备用定位方法
            tooltip.style.top = `${event.clientY + 10}px`;
            tooltip.style.left = `${event.clientX + 10}px`;
        }
    }

    /**
     * 设置tooltip的鼠标事件
     * @param {HTMLElement} tooltip - Tooltip元素
     * @param {HTMLElement} cell - 单元格元素
     */
    function setupTooltipMouseEvents(tooltip, cell) {
        try {
            // 使用防抖函数处理鼠标移动
            const handleMouseMove = debounce(function(event) {
                const tooltipRect = tooltip.getBoundingClientRect();

                // 检查鼠标是否在tooltip上
                const isOverTooltip = event.clientX >= tooltipRect.left &&
                                    event.clientX <= tooltipRect.right &&
                                    event.clientY >= tooltipRect.top &&
                                    event.clientY <= tooltipRect.bottom;

                // 检查鼠标是否在单元格上
                const cellRect = cell.getBoundingClientRect();
                const isOverCell = event.clientX >= cellRect.left &&
                                event.clientX <= cellRect.right &&
                                event.clientY >= cellRect.top &&
                                event.clientY <= cellRect.bottom;

                // 如果鼠标既不在tooltip上也不在单元格上，则移除tooltip
                if (!isOverTooltip && !isOverCell) {
                    tooltip.style.opacity = '0';

                    // 延迟移除，以便完成淡出动画
                    setTimeout(() => {
                        tooltip.remove();
                        cell.dataset.tooltipShown = 'false';
                        document.removeEventListener('mousemove', handleMouseMove);
                    }, 200);
                }
            }, 100);

            // 添加鼠标移动事件监听
            document.addEventListener('mousemove', handleMouseMove);

            // 添加点击文档事件 - 点击其他地方关闭tooltip
            document.addEventListener('click', function closeTooltip(e) {
                // 如果点击的不是tooltip和单元格
                if (!e.target.closest('.tooltip-container') && !e.target.closest('.book-title-cell')) {
                    tooltip.remove();
                    cell.dataset.tooltipShown = 'false';
                    document.removeEventListener('click', closeTooltip);
                    document.removeEventListener('mousemove', handleMouseMove);
                }
            });
        } catch (error) {
            console.error('设置tooltip鼠标事件失败:', error);
        }
    }

    // 添加全局事件处理 - 使用事件委托处理tooltip
    document.addEventListener('mouseover', function(e) {
        // 检查是否悬停在书籍标题单元格上
        const cell = e.target.closest('.book-title-cell');
        if (cell) {
            // 显示书籍悬停提示
            showBookTooltip(cell, e);
        }
    });

    // 添加全局滚动事件监听
    window.addEventListener('scroll', function() {
        // Remove all tooltips when scrolling
        document.querySelectorAll('.tooltip-box, .tooltip-container, .book-tooltip').forEach(tooltip => {
            tooltip.remove();
        });

        // Clear all tooltip shown flags
        document.querySelectorAll('[data-tooltip-shown="true"]').forEach(el => {
            el.dataset.tooltipShown = 'false';
        });
    }, { passive: true });

    // 添加窗口大小改变事件监听
    window.addEventListener('resize', function() {
        document.querySelectorAll('.tooltip-container').forEach(tooltip => {
            const cell = document.querySelector(`[data-tooltip-shown="true"]`);
            if (cell) {
                cell.dataset.tooltipShown = 'false';
            }
            tooltip.remove();
        });
    }, { passive: true });

    // 直接绑定搜索按钮事件
    const searchButton = document.getElementById('bookSearchButton');
    if (searchButton) {
        console.log('绑定搜索按钮事件');
        searchButton.addEventListener('click', function() {
            const input = document.getElementById('bookSearchInput');
            if (input && input.value.trim()) {
                // 调用jQuery版本的搜索函数
                console.log('执行搜索:', input.value.trim());

                // 搜索函数应该在jQuery ready中定义
                if (window.$ && typeof window.$.fn.jquery !== 'undefined') {
                    // 确定jQuery搜索函数是否可用
                    if (typeof searchBooks === 'function') {
                        searchBooks(input.value.trim());
                    } else {
                        console.error('jQuery搜索函数不存在!');
                        alert('搜索功能不可用，请刷新页面重试');
                    }
                } else {
                    console.error('jQuery未加载!');
                    alert('搜索组件未正确加载，请刷新页面重试');
                }
            } else {
                alert('请输入搜索关键词');
            }
        });
    } else {
        console.error('未找到搜索按钮!');
    }

// 添加书籍模态框相关清理
$('#addBookModal').on('hidden.bs.modal', function() {
    // 清理此模态框
    cleanupAllModals();
});

// 取消新建对话按钮点击事件
$('#cancelNewChat').on('click', function() {
    console.log('取消新建对话');

    // 关闭模态框
    $('#newChatModal').modal('hide');

    // 清理模态框
    cleanupAllModals();

    // 清空输入框
    $('#newKnowId').val('');
    $('#newBookUrl').val('');

    // 如果有ChatState，重置新建对话状态
    if (window.ChatState) {
        ChatState.newChat = {
            bookUrl: '',
            knowId: '',
            title: '新建对话'
        };
        ChatState.saveToStorage();
    }
});

// 新建对话确认按钮点击事件
$('#confirmNewChat').on('click', function() {
    // 获取输入值
    const knowId = $('#newKnowId').val().trim();
    const bookUrl = $('#newBookUrl').val().trim();

    console.log('确认新建对话:', { knowId, bookUrl });

    // 基本验证
    if (!knowId || !bookUrl) {
        showToast('请填写知识点ID和书籍链接', 'warning');
        return;
    }

    // 使用ChatState确认新建对话
    if (window.ChatState) {
        // 先保存当前输入的值
        ChatState.saveKnowId(knowId, 'new');
        ChatState.saveBookUrl(bookUrl, 'new');

        // 确认新建对话，将值同步到当前对话
        ChatState.confirmNewChat();

        console.log('新建对话已确认，值已同步到当前对话');
    } else {
        console.error('ChatState模块未定义，无法同步数据');

        // 直接设置值作为备用方案
        $('#knowId').val(knowId);
        $('#currentBookUrl').val(bookUrl);
    }

    // 关闭模态框
    $('#newChatModal').modal('hide');

    // 清理模态框
    cleanupAllModals();

    // 显示成功提示
    showToast('已创建新对话', 'success');
});

// Directly add simple tooltip style to head to ensure availability
/**
 * 添加tooltip样式到页面
 * 确保样式只添加一次
 */
(function addTooltipStyles() {
    const styleId = 'enhanced-tooltip-styles';
    if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* 悬停提示容器 */
            .tooltip-container {
                position: fixed;
                z-index: 9999; /* 提高z-index，确保显示在模态框上方 */
                background-color: #fff;
                border: 1px solid rgba(0,0,0,.15);
                border-radius: .5rem;
                box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.25);
                padding: 0;
                max-width: 350px;
                width: 350px;
                pointer-events: auto; /* 允许与tooltip交互 */
                opacity: 0;
                transition: opacity 0.2s ease-in-out;
                overflow: hidden; /* 确保内容不会溢出圆角 */
            }

            /* 书籍提示内容 */
            .book-tooltip {
                padding: 1rem;
                background: white;
                z-index: 9999; /* 确保内容也有高z-index */
                cursor: default;
            }

            /* 书籍标题 */
            .book-tooltip-title {
                font-weight: bold;
                color: #0d6efd;
                margin-bottom: 12px;
                border-bottom: 1px solid #e9ecef;
                padding-bottom: 10px;
                font-size: 1.1rem;
                line-height: 1.4;
                cursor: pointer; /* 指示可点击 */
                transition: color 0.2s ease;
            }

            /* 标题悬停效果 */
            .book-tooltip-title:hover {
                color: #0a58ca;
                text-decoration: underline;
            }

            /* 书籍类型 */
            .book-tooltip-type {
                font-style: italic;
                color: #6c757d;
                margin-bottom: 8px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
            }

            /* 章节信息 */
            .book-tooltip-chapter {
                color: #dc3545;
                font-weight: bold;
                margin-bottom: 12px;
                font-size: 0.95rem;
                display: inline-block;
                padding: 2px 6px;
                background-color: rgba(220, 53, 69, 0.1);
                border-radius: 4px;
            }

            /* 书籍描述 */
            .book-tooltip-desc {
                color: #212529;
                max-height: 200px;
                overflow-y: auto;
                line-height: 1.6;
                padding-right: 5px;
                margin-bottom: 10px;
                scrollbar-width: thin;
                scrollbar-color: #cdcdcd #f1f1f1;
                font-size: 0.95rem;
            }

            /* 无描述提示 */
            .book-tooltip-no-desc {
                color: #6c757d;
                font-style: italic;
                text-align: center;
                padding: 10px 0;
                background-color: #f8f9fa;
                border-radius: 4px;
                margin-bottom: 10px;
            }

            /* 书籍URL */
            .book-tooltip-url {
                color: #0d6efd;
                font-size: 0.85rem;
                word-break: break-all;
                background-color: #f8f9fa;
                padding: 6px 8px;
                border-radius: 4px;
                border-left: 3px solid #0d6efd;
                margin-top: 10px;
            }

            /* 书籍标题单元格 */
            .book-title-cell {
                cursor: pointer;
                position: relative;
                transition: background-color 0.2s ease;
            }

            /* 单元格悬停效果 */
            .book-title-cell:hover {
                background-color: #f0f7ff;
            }

            /* 自定义滚动条样式 */
            .book-tooltip-desc::-webkit-scrollbar {
                width: 6px;
            }

            .book-tooltip-desc::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .book-tooltip-desc::-webkit-scrollbar-thumb {
                background: #cdcdcd;
                border-radius: 3px;
            }

            .book-tooltip-desc::-webkit-scrollbar-thumb:hover {
                background: #aaaaaa;
            }

            /* 动画效果 */
            @keyframes tooltipFadeIn {
                from { opacity: 0; transform: translateY(5px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .tooltip-container {
                animation: tooltipFadeIn 0.2s ease-out forwards;
            }
        `;
        document.head.appendChild(style);
        console.log('增强型tooltip样式已添加');
    }
})();

// Simple global tooltip functions
window.showBookTooltip = function(element, book) {
    // Remove any existing tooltips
    hideAllTooltips();

    if (!element || !book) return;

    // Detect chapter info
    let chapterInfo = null;
    if (book.title) {
        // 中文章节格式: 第X章
        const cnMatch = book.title.match(/第(\d+)章/);
        if (cnMatch) {
            chapterInfo = cnMatch[0];
        } else {
            // 英文章节格式: Chapter X
            const enMatch = book.title.match(/Chapter\s+(\d+)/i);
            if (enMatch) {
                chapterInfo = enMatch[0];
            } else {
                // 节/部分格式: Section X
                const sectionMatch = book.title.match(/Section\s+(\d+)/i);
                if (sectionMatch) {
                    chapterInfo = sectionMatch[0];
                } else {
                    // 部分格式: Part X
                    const partMatch = book.title.match(/Part\s+(\d+)/i);
                    if (partMatch) {
                        chapterInfo = partMatch[0];
                    }
                }
            }
        }
    }

    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-box';
    tooltip.innerHTML = `
        <div class="tooltip-title">${book.title || 'Unknown Book'}</div>
        ${book.type ? `<div class="tooltip-type">类型: ${book.type}</div>` : ''}
        ${chapterInfo ? `<div class="tooltip-chapter">章节: ${chapterInfo}</div>` : ''}
        ${book.description
            ? `<div class="tooltip-description">${book.description}</div>`
            : '<div class="tooltip-no-desc">暂无简介</div>'}
    `;

    // Add to document
    document.body.appendChild(tooltip);

    // Position tooltip
    positionTooltip(tooltip, element);

    // Show tooltip
    tooltip.style.display = 'block';

    // Set up mouse leave event
    element.addEventListener('mouseleave', function onMouseLeave() {
        setTimeout(() => {
            // Check if mouse is over tooltip
            const rect = tooltip.getBoundingClientRect();
            const mouseX = event.clientX;
            const mouseY = event.clientY;

            if (!(mouseX >= rect.left && mouseX <= rect.right &&
                  mouseY >= rect.top && mouseY <= rect.bottom)) {
                tooltip.style.display = 'none';
                tooltip.remove();
            }
        }, 100);

        // Remove this event listener
        element.removeEventListener('mouseleave', onMouseLeave);
    });

    // Set up tooltip mouse leave
    tooltip.addEventListener('mouseleave', function() {
        tooltip.style.display = 'none';
        tooltip.remove();
    });
};

// Hide all tooltips
function hideAllTooltips() {
    document.querySelectorAll('.tooltip-box').forEach(tooltip => {
        tooltip.style.display = 'none';
        tooltip.remove();
    });
}

// Position tooltip relative to element
function positionTooltip(tooltip, element) {
    const rect = element.getBoundingClientRect();
    const tooltipWidth = 350; // Fixed width from CSS

    // Calculate position
    let left = rect.right + 10;
    let top = rect.top + window.scrollY;

    // Ensure tooltip doesn't go off-screen to the right
    if (left + tooltipWidth > window.innerWidth) {
        left = rect.left - tooltipWidth - 10;

        // If tooltip would go off-screen to the left, position below element
        if (left < 0) {
            left = rect.left;
            top = rect.bottom + window.scrollY + 10;
        }
    }

    // Set position using fixed positioning
    tooltip.style.position = 'fixed';
    tooltip.style.top = (rect.top - window.scrollY) + 'px';
    tooltip.style.left = left + 'px';
    tooltip.style.zIndex = '1070'; // Ensure tooltip appears above modal
}

// Add window scroll event to remove tooltips
window.addEventListener('scroll', function() {
    // Remove all tooltips when scrolling
    document.querySelectorAll('.tooltip-box, .tooltip-container, .book-tooltip').forEach(tooltip => {
        tooltip.remove();
    });

    // Clear all tooltip shown flags
    document.querySelectorAll('[data-tooltip-shown="true"]').forEach(el => {
        el.dataset.tooltipShown = 'false';
    });
});

// 确保全局可用
// window.cleanupAllModals = cleanupAllModals;

// Add a fallback getAuthToken function if not already defined
if (typeof window.getAuthToken !== 'function') {
    window.getAuthToken = function() {
        try {
            const token = localStorage.getItem('token');
            if (!token || token === 'undefined' || token === 'null') {
                return null;
            }
            
            // Use normalizeToken if available
            if (typeof normalizeToken === 'function') {
                return normalizeToken(token);
            } else {
                // Simple normalize implementation
                if (token.toLowerCase().startsWith('bearer ')) {
                    return token.substring(7);
                }
                return token;
            }
        } catch (error) {
            console.error('获取认证令牌失败:', error);
            return null;
        }
    };
    console.log('添加全局getAuthToken兼容实现');
}

// Add a simplified cleanupAllModals function if not already defined
if (typeof window.cleanupAllModals !== 'function') {
    window.cleanupAllModals = function() {
        console.log('执行简化版模态框清理');
        try {
            // 移除所有遮罩层
            $('.modal-backdrop').remove();
            document.querySelectorAll('.modal-backdrop').forEach(el => {
                try { el.parentNode.removeChild(el); } catch(e) {}
            });

            // 重置body样式
            $('body').removeClass('modal-open').css({
                'overflow': '',
                'padding-right': '',
                'height': ''
            });
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('padding-right');
            document.body.style.removeProperty('overflow');

            // 关闭所有模态框
            $('.modal').removeClass('show').css('display', 'none');
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('show');
                modal.style.display = 'none';
            });

            // 移除所有tooltip
            $('.tooltip-container, .book-tooltip, .tooltip-box').remove();
            
            console.log('简化版模态框清理完成');
        } catch (error) {
            console.error('清理模态框过程中发生错误:', error);
        }
        return true;
    };
    console.log('添加全局简化版cleanupAllModals函数');
}

$(document).ready(function() {
    // 全局showToast函数兼容性
    if (typeof showToast !== 'function') {
        window.showToast = function(message, type) {
            console.log(`[${type || 'info'}] ${message}`);
            if (typeof window.alert === 'function') {
                window.alert(message);
            }
        };
    }
});