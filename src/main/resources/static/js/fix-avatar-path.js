// This is a debugging tool to fix duplicate avatar paths
document.addEventListener('DOMContentLoaded', async function() {
    // Check if it's an admin account first
    const userInfo = await checkAuth();
    if (!userInfo || userInfo.role < 2) {
        console.error('Only administrators can use this tool');
        return;
    }

    // Create UI for the fixing tool
    const fixToolUI = document.createElement('div');
    fixToolUI.className = 'fix-tool';
    fixToolUI.innerHTML = `
        <h3>Avatar Path Debug Tool</h3>
        <div class="debug-info"></div>
        <button id="checkPath">Check Avatar Path</button>
        <button id="fixPath">Fix Avatar Path</button>
    `;

    document.body.appendChild(fixToolUI);

    // Style the tool
    const style = document.createElement('style');
    style.textContent = `
        .fix-tool {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #fff;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            z-index: 9999;
        }
        .fix-tool h3 {
            margin-top: 0;
        }
        .fix-tool button {
            margin: 5px;
            padding: 5px 10px;
        }
        .debug-info {
            margin-bottom: 10px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            background: #f5f5f5;
            padding: 5px;
        }
    `;
    document.head.appendChild(style);

    // Check button
    document.getElementById('checkPath').addEventListener('click', async function() {
        const debugInfo = document.querySelector('.debug-info');
        debugInfo.textContent = 'Checking avatar path...';
        
        try {
            const response = await fetch('/api/user/me', {
                headers: {
                    'Authorization': localStorage.getItem('token')
                }
            });
            
            const data = await response.json();
            
            if (data.code === 200 && data.data) {
                const user = data.data;
                
                // Check if path has duplicate "avatars/"
                const avatar = user.avatar;
                const hasDuplicate = avatar && avatar.includes('avatars/avatars/');
                
                debugInfo.textContent = `
Current avatar path: ${avatar}
Has duplicate 'avatars/': ${hasDuplicate}
Expected correct path: ${hasDuplicate ? avatar.replace('avatars/avatars/', 'avatars/') : avatar}
Current URL being used: /uploads/${avatar}
Expected correct URL: ${hasDuplicate ? 
    `/uploads/${avatar.replace('avatars/avatars/', 'avatars/')}` : 
    `/uploads/${avatar}`}
                `;
            } else {
                debugInfo.textContent = 'Failed to get user data';
            }
        } catch (error) {
            debugInfo.textContent = `Error: ${error.message}`;
        }
    });

    // Fix button
    document.getElementById('fixPath').addEventListener('click', async function() {
        const debugInfo = document.querySelector('.debug-info');
        debugInfo.textContent = 'Fixing avatar path...';
        
        try {
            // First, get current user data
            const response = await fetch('/api/user/me', {
                headers: {
                    'Authorization': localStorage.getItem('token')
                }
            });
            
            const data = await response.json();
            
            if (data.code === 200 && data.data) {
                const user = data.data;
                const avatar = user.avatar;
                
                if (avatar && avatar.includes('avatars/avatars/')) {
                    // Path has duplicate, we need to fix it
                    const correctedPath = avatar.replace('avatars/avatars/', 'avatars/');
                    
                    // Call API to update the path
                    const updateResponse = await fetch('/api/user/fix-avatar', {
                        method: 'POST',
                        headers: {
                            'Authorization': localStorage.getItem('token'),
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            userId: user.id,
                            oldPath: avatar,
                            newPath: correctedPath
                        })
                    });
                    
                    const updateData = await updateResponse.json();
                    
                    if (updateData.code === 200) {
                        debugInfo.textContent = `
Path fixed successfully!
Old path: ${avatar}
New path: ${correctedPath}

Refreshing page in 3 seconds...
                        `;
                        
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    } else {
                        debugInfo.textContent = `Failed to fix path: ${updateData.message}`;
                    }
                } else {
                    debugInfo.textContent = 'No duplicate found in avatar path. No fix needed.';
                }
            } else {
                debugInfo.textContent = 'Failed to get user data';
            }
        } catch (error) {
            debugInfo.textContent = `Error: ${error.message}`;
        }
    });
});

// This function checks if a path has duplicate prefix
function hasDuplicatePrefix(path, prefix) {
    if (!path) return false;
    
    const regex = new RegExp(`${prefix}/${prefix}/`);
    return regex.test(path);
}

// Attempt temporary fix on page load
document.addEventListener('DOMContentLoaded', function() {
    // Find all avatar images
    const avatarImages = document.querySelectorAll('img.avatar, img.large-avatar');
    
    avatarImages.forEach(img => {
        // Store the original src
        const originalSrc = img.src;
        
        // Check if src contains duplicate avatar path
        if (originalSrc.includes('/uploads/avatars/avatars/')) {
            // Replace the duplicate path
            const fixedSrc = originalSrc.replace('/uploads/avatars/avatars/', '/uploads/avatars/');
            
            // Set the fixed src
            img.src = fixedSrc;
            
            console.log('Fixed avatar path from', originalSrc, 'to', fixedSrc);
            
            // Handle the case where even the fixed path doesn't work
            img.onerror = function() {
                if (img.src !== '/images/default-avatar.png') {
                    console.error('Failed to load avatar from fixed path, using default');
                    img.src = '/images/default-avatar.png';
                }
            };
        }
    });
}); 