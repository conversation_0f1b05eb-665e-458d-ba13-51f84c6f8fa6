document.addEventListener('DOMContentLoaded', async function() {
    // 初始化用户信息
    const userInfo = await checkAuth();
    if (userInfo) {
        document.querySelector('.username').textContent = userInfo.username;
        const avatarUrl = getAvatarUrl(userInfo.avatar);
        const avatarImg = document.querySelector('.avatar');
        avatarImg.src = avatarUrl;
        avatarImg.onerror = function() {
            this.src = '/images/default-avatar.png';
            console.log('Avatar load failed, using default');
        };
    }

    // 用户下拉菜单
    const userInfoEl = document.querySelector('.user-info');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    userInfoEl.addEventListener('click', () => {
        dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', (e) => {
        if (!userInfoEl.contains(e.target)) {
            dropdownMenu.style.display = 'none';
        }
    });

    // 退出登录
    document.getElementById('logout').addEventListener('click', async (e) => {
        e.preventDefault();
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/auth/login';
            return;
        }

        try {
            const response = await fetch('/api/user/logout', {
                method: 'POST',
                headers: {
                    'Authorization': token,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                localStorage.removeItem('token');
                window.location.href = '/auth/login';
            } else {
                const data = await response.json();
                console.error('退出失败:', data.message);
            }
        } catch (error) {
            console.error('退出失败:', error);
        }
    });
});

// 添加获取头像URL的函数
function getAvatarUrl(path) {
    if (!path) return '/images/default-avatar.png';

    // Use the fixAvatarUrl helper if available
    if (window.fixAvatarUrl) {
        path = window.fixAvatarUrl(path);
    }
    
    // The server already gives us the correct path with 'avatars/' prefix
    // So we just need to prepend '/uploads/'
    return `/uploads/${path}?t=${new Date().getTime()}`;
}