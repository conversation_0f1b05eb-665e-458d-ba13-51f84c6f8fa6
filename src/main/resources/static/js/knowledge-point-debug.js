/**
 * 知识点调试工具
 * 用于诊断知识点名称显示问题
 */

// 调试函数：检查知识点数据完整性
function debugKnowledgePointData() {
    if (selectedKnowledgePoints.size === 0) {
        return;
    }

    selectedKnowledgePoints.forEach((pointData, pointId) => {
        // 检查名称是否为默认格式
        if (pointData.name && pointData.name.includes('#')) {
            // 名称可能不完整
        }

        // 检查knowId是否存在
        if (!pointData.knowId) {
            // 缺少knowId
        }
    });

    $('.knowledge-checkbox:checked').each(function() {
        const checkbox = $(this);
        const id = checkbox.val();
        const card = checkbox.closest('.card');
        const name = card.find('.card-title').text().trim();

        // 检查是否在全局状态中
        if (selectedKnowledgePoints.has(id)) {
            const globalData = selectedKnowledgePoints.get(id);
            if (globalData.name !== name) {
                // 名称不匹配
            }
        }
    });
}

// 调试函数：检查试卷配置生成过程
function debugPaperConfigGeneration() {
    if (selectedKnowledgePoints.size === 0) {
        return;
    }

    const knowledgeConfigs = [];
    const processedIds = new Set();

    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        const knowledgeId = parseInt(pointId);

        if (processedIds.has(knowledgeId)) {
            return;
        }
        processedIds.add(knowledgeId);

        const knowId = pointData.knowId || knowledgeId;

        knowledgeConfigs.push({
            knowledgeId: knowId,
            questionCount: 5,
            includeShortAnswer: false // 默认关闭简答题开关
        });
    });

    return knowledgeConfigs;
}

// 调试函数：模拟知识点配置渲染
function debugKnowledgePointConfigRendering(knowledgeConfigs) {
    if (!knowledgeConfigs) {
        knowledgeConfigs = debugPaperConfigGeneration();
    }

    if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
        return;
    }

    const knowledgeInfo = {};

    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        const info = {
            name: pointData.name,
            topicCount: 0,
            originalId: pointId,
            knowId: pointData.knowId || pointId
        };

        // 解析题目数量
        const topicCountText = pointData.topicCount || '';
        if (typeof topicCountText === 'string') {
            if (topicCountText.includes('题目:')) {
                info.topicCount = parseInt(topicCountText.replace('题目:', '').trim()) || 0;
            } else if (topicCountText.includes('题')) {
                info.topicCount = parseInt(topicCountText.replace('题', '').trim()) || 0;
            } else {
                info.topicCount = parseInt(topicCountText) || 0;
            }
        }

        knowledgeInfo[pointId] = info;
        knowledgeInfo[pointData.knowId || pointId] = info;
    });

    knowledgeConfigs.forEach(function(config, index) {
        const knowledgeId = config.knowledgeId;

        // 查找知识点信息
        let info = knowledgeInfo[knowledgeId];

        if (!info) {
            Object.keys(knowledgeInfo).forEach(key => {
                const keyInfo = knowledgeInfo[key];
                if (!info && (
                    key == knowledgeId ||
                    keyInfo.knowId == knowledgeId ||
                    keyInfo.originalId == knowledgeId
                )) {
                    info = keyInfo;
                }
            });
        }

        if (!info) {
            info = {
                name: `知识点 #${knowledgeId}`,
                topicCount: 0
            };
        }
    });
}

// 调试函数：检查知识点选择过程
function debugKnowledgePointSelection() {
    const checkboxes = $('.knowledge-checkbox');

    checkboxes.each(function(index) {
        const checkbox = $(this);
        const id = checkbox.val();
        const isChecked = checkbox.prop('checked');
        const card = checkbox.closest('.card');
        const name = card.find('.card-title').text().trim();

        if (isChecked) {
            const globalData = selectedKnowledgePoints.get(id);
            // 检查数据一致性
        }
    });
}

// 主调试函数
function runKnowledgePointDiagnostics() {
    debugKnowledgePointSelection();
    debugKnowledgePointData();

    const configs = debugPaperConfigGeneration();
    debugKnowledgePointConfigRendering(configs);

    if (selectedKnowledgePoints.size === 0) {
        return;
    }

    let hasNameIssues = false;
    selectedKnowledgePoints.forEach((pointData, pointId) => {
        if (!pointData.name || pointData.name.includes('#')) {
            hasNameIssues = true;
        }
    });

    const hasKnowIdIssues = Array.from(selectedKnowledgePoints.values()).some(data => !data.knowId);
}

// 修复函数：尝试修复知识点名称
function fixKnowledgePointNames() {
    let fixedCount = 0;

    selectedKnowledgePoints.forEach((pointData, pointId) => {
        if (!pointData.name || pointData.name.includes('#')) {
            // 尝试从页面获取正确的名称
            const checkbox = $(`#kp-${pointId}`);
            if (checkbox.length > 0) {
                const card = checkbox.closest('.card');
                const name = card.find('.card-title').text().trim();

                if (name && !name.includes('#')) {
                    pointData.name = name;
                    selectedKnowledgePoints.set(pointId, pointData);
                    fixedCount++;
                }
            }
        }
    });

    if (fixedCount > 0) {
        // 更新显示
        if (typeof updateSelectedKnowledgePointsDisplay === 'function') {
            updateSelectedKnowledgePointsDisplay();
        }
    }
}

// 导出调试函数
window.debugKnowledgePointData = debugKnowledgePointData;
window.debugPaperConfigGeneration = debugPaperConfigGeneration;
window.debugKnowledgePointConfigRendering = debugKnowledgePointConfigRendering;
window.debugKnowledgePointSelection = debugKnowledgePointSelection;
window.runKnowledgePointDiagnostics = runKnowledgePointDiagnostics;
window.fixKnowledgePointNames = fixKnowledgePointNames;
