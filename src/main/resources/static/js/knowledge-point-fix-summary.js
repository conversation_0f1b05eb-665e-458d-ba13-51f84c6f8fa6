/**
 * 知识点重复问题修复总结
 * 
 * 问题描述：
 * 用户选择2个知识点，但在试卷配置界面显示了4个知识点
 * 
 * 根本原因：
 * 1. 重复的事件绑定导致同一个知识点被多次添加
 * 2. 缺乏去重机制
 * 3. 全局状态管理不当
 * 
 * 修复方案：
 */

// 1. 修复后的全局事件监听器（带重复检查）
$(document).on('change', '.knowledge-checkbox', function() {
    const isChecked = $(this).prop('checked');
    const id = $(this).val();
    const card = $(this).closest('.card');
    const name = card.find('.card-title').text().trim();
    const topicCount = card.find('.badge-info').text().trim();
    const isFree = card.find('.badge-success').length > 0;

    console.log(`知识点选择变化: ID=${id}, 选中=${isChecked}, 名称=${name}`);

    // 更新全局选择状态
    if (isChecked) {
        // 检查是否已经存在，避免重复添加
        if (!selectedKnowledgePoints.has(id)) {
            selectedKnowledgePoints.set(id, {
                id: id,
                name: name,
                topicCount: topicCount,
                isFree: isFree
            });
            console.log(`添加知识点: ${name} (ID: ${id})`);
        } else {
            console.log(`知识点已存在，跳过添加: ${name} (ID: ${id})`);
        }
    } else {
        if (selectedKnowledgePoints.has(id)) {
            selectedKnowledgePoints.delete(id);
            console.log(`删除知识点: ${name} (ID: ${id})`);
        }
    }

    console.log(`当前选中的知识点数量: ${selectedKnowledgePoints.size}`);
    updateSelectionCounter();
});

// 2. 修复后的生成试卷配置（带去重处理）
function generatePaperWithDeduplication() {
    const knowledgeConfigs = [];
    const knowledgeNames = [];
    const processedIds = new Set(); // 用于防止重复处理

    selectedKnowledgePoints.forEach(function(pointData, pointId) {
        const knowledgeId = parseInt(pointId);
        
        // 检查是否已经处理过这个ID
        if (processedIds.has(knowledgeId)) {
            console.warn(`跳过重复的知识点ID: ${knowledgeId}`);
            return;
        }
        processedIds.add(knowledgeId);

        // 处理知识点配置...
        knowledgeConfigs.push({
            knowledgeId: knowledgeId,
            questionCount: defaultCount,
            includeShortAnswer: true
        });

        knowledgeNames.push(pointData.name);
    });

    console.log(`生成试卷：选中知识点数量 ${selectedKnowledgePoints.size}，配置数量 ${knowledgeConfigs.length}`);
}

// 3. 修复后的渲染知识点配置（带去重处理）
function renderKnowledgePointsConfigWithDeduplication(knowledgeConfigs) {
    // 去重处理：确保每个知识点ID只出现一次
    const uniqueConfigs = [];
    const seenIds = new Set();
    
    knowledgeConfigs.forEach(config => {
        if (!seenIds.has(config.knowledgeId)) {
            seenIds.add(config.knowledgeId);
            uniqueConfigs.push(config);
        } else {
            console.warn(`发现重复的知识点ID: ${config.knowledgeId}，已跳过`);
        }
    });

    console.log(`原始知识点配置数量: ${knowledgeConfigs.length}, 去重后数量: ${uniqueConfigs.length}`);

    // 使用去重后的配置
    knowledgeConfigs = uniqueConfigs;
    
    // 继续渲染...
}

// 4. 自动清理重复选择的功能
function cleanupDuplicateSelections() {
    console.log('开始清理重复选择...');
    const uniqueSelections = new Map();
    
    // 遍历当前选择，保留第一次出现的
    selectedKnowledgePoints.forEach((pointData, pointId) => {
        if (!uniqueSelections.has(pointId)) {
            uniqueSelections.set(pointId, pointData);
        } else {
            console.warn(`发现重复选择的知识点: ${pointId}, 名称: ${pointData.name}`);
        }
    });

    // 如果发现重复，更新全局状态
    if (uniqueSelections.size !== selectedKnowledgePoints.size) {
        console.log(`清理前: ${selectedKnowledgePoints.size} 个知识点，清理后: ${uniqueSelections.size} 个知识点`);
        selectedKnowledgePoints.clear();
        uniqueSelections.forEach((pointData, pointId) => {
            selectedKnowledgePoints.set(pointId, pointData);
        });
        
        // 更新显示
        updateSelectionCounter();
    }
}

// 5. 移除了重复的事件绑定
function renderKnowledgePointsFixed(points, groupName) {
    // 渲染知识点HTML...
    $('#knowledge-points-container').html(html);
    
    // 注意：不在这里绑定事件，因为已经有全局事件监听器了
    // 移除了这部分代码：
    // $('.knowledge-checkbox').on('change', function() { ... });
}

/**
 * 测试验证步骤：
 * 
 * 1. 打开浏览器开发者工具的控制台
 * 2. 选择知识点时观察控制台日志
 * 3. 确认每个知识点只被添加一次
 * 4. 生成试卷时检查配置数量是否正确
 * 5. 验证试卷配置界面显示的知识点数量是否与选择的一致
 * 
 * 预期结果：
 * - 选择2个知识点，配置界面应该显示2个知识点
 * - 控制台不应该出现重复添加的警告
 * - 知识点数量统计应该准确
 */
