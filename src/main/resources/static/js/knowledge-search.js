/**
 * 知识点搜索功能
 * 用于试卷生成页面的知识点搜索
 */

// 初始化主页面搜索功能
function initMainPageSearch() {
    // 初始化主页面搜索功能

    const searchInput = $('#knowledgePointSearchMain');
    const searchBtn = $('#searchBtnMain');
    const clearBtn = $('#clearSearchMain');
    const searchStatusBar = $('#searchStatusBar');
    const searchStatusText = $('#searchStatusText');
    const backToCategories = $('#backToCategories');

    if (searchInput.length === 0) {
        console.warn('⚠️ 搜索输入框未找到');
        return;
    }

    let searchTimeout;
    let isSearchMode = false;

    // 移除之前的事件绑定
    searchInput.off('.search');
    searchBtn.off('.search');
    clearBtn.off('.search');
    backToCategories.off('.search');

    // 执行搜索的函数
    function executeSearch() {
        const searchTerm = searchInput.val().trim();
        if (searchTerm) {
            console.log('📝 执行搜索:', searchTerm);
            performMainPageSearch(searchTerm);
        }
    }

    // 搜索按钮点击事件
    searchBtn.on('click.search', function() {
        executeSearch();
    });

    // 回车键搜索
    searchInput.on('keypress.search', function(e) {
        if (e.which === 13) { // Enter键
            e.preventDefault();
            executeSearch();
        }
    });

    // 输入变化事件
    searchInput.on('input.search', function() {
        const searchTerm = $(this).val().trim();

        // 显示/隐藏清空按钮
        if (searchTerm) {
            clearBtn.show();
        } else {
            clearBtn.hide();
            if (isSearchMode) {
                restoreDefaultKnowledgeView();
                hideSearchMode();
            }
        }
    });

    // 清空搜索
    clearBtn.on('click.search', function() {
        console.log('🧹 清空搜索');
        searchInput.val('');
        clearBtn.hide();
        if (isSearchMode) {
            restoreDefaultKnowledgeView();
            hideSearchMode();
        }
        searchInput.focus();
    });

    // 返回分类按钮
    backToCategories.on('click.search', function() {
        console.log('🔙 返回分类视图');
        searchInput.val('');
        clearBtn.hide();
        restoreDefaultKnowledgeView();
        hideSearchMode();
    });

    // 显示搜索模式
    function showSearchMode(searchTerm, resultCount) {
        isSearchMode = true;
        searchStatusText.text(`搜索 "${searchTerm}" 找到 ${resultCount} 个结果`);
        searchStatusBar.show();
        backToCategories.show();
    }

    // 隐藏搜索模式
    function hideSearchMode() {
        isSearchMode = false;
        searchStatusBar.hide();
        backToCategories.hide();
    }

    // 将函数暴露给全局，供其他函数调用
    window.showSearchMode = showSearchMode;
    window.hideSearchMode = hideSearchMode;

    // 主页面搜索功能初始化完成
}

/**
 * 执行主页面搜索
 */
function performMainPageSearch(searchTerm) {
    const knowledgeContainer = $('#knowledge-points-container');

    if (!searchTerm) {
        // 清空搜索，恢复默认状态
        restoreDefaultKnowledgeView();
        if (window.hideSearchMode) {
            window.hideSearchMode();
        }
        return;
    }

    // 显示加载状态
    knowledgeContainer.html(`
        <div class="search-loading-container">
            <div class="text-center py-5">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="sr-only">搜索中...</span>
                </div>
                <h5 class="text-muted">正在搜索知识点</h5>
                <p class="text-muted">搜索关键词: <strong>"${escapeHtml(searchTerm)}"</strong></p>
                <div class="progress mt-3" style="height: 4px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    `);

    // 调用后端API进行搜索
    $.ajax({
        url: '/api/knowledge/search',
        method: 'GET',
        data: {
            keyword: searchTerm
        },
        timeout: 10000, // 10秒超时
        success: function(response) {
            if (response && response.success && response.data) {
                const results = response.data;
                renderSearchResults(results, searchTerm);

                // 显示搜索模式状态栏
                if (window.showSearchMode) {
                    window.showSearchMode(searchTerm, results.length);
                }
            } else {
                showSearchError("搜索失败", response.message || "未能获取搜索结果", searchTerm);
            }
        },
        error: function(xhr, status, error) {
            let errorMessage = "服务器连接错误，请稍后重试";
            if (status === 'timeout') {
                errorMessage = "搜索超时，请检查网络连接或稍后重试";
            } else if (xhr.status === 404) {
                errorMessage = "搜索服务不可用，请联系管理员";
            }
            showSearchError("搜索错误", errorMessage, searchTerm);
        }
    });
}

/**
 * 渲染搜索结果
 */
function renderSearchResults(results, searchTerm) {
    const knowledgeContainer = $('#knowledge-points-container');

    if (!results || results.length === 0) {
        knowledgeContainer.html(`
            <div class="no-results">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">未找到匹配的知识点</h5>
                    <p class="text-muted">尝试其他关键词或浏览分类列表</p>
                    <div class="mt-3">
                        <button class="btn btn-outline-primary" onclick="$('#knowledgePointSearchMain').val('').focus()">
                            <i class="fas fa-edit mr-1"></i>修改搜索
                        </button>
                        <button class="btn btn-outline-secondary ml-2" onclick="$('#clearSearchMain').click()">
                            <i class="fas fa-times mr-1"></i>清空搜索
                        </button>
                    </div>
                </div>
            </div>
        `);
        return;
    }

    // 准备关键词数组用于高亮
    const keywords = searchTerm.split(/\s+/).filter(k => k.length > 0);

    // 构建搜索结果HTML - 使用与原来知识点卡片完全一致的结构
    let html = `
        <div class="search-results">
            <div class="alert alert-info mb-3">
                <i class="fas fa-search mr-2"></i>
                找到 <strong>${results.length}</strong> 个匹配的知识点
            </div>
    `;

    // 添加每个知识点 - 使用与原来完全一致的卡片结构
    results.forEach(item => {
        // 兼容不同的数据结构
        const knowledgeName = item.knowledgeName || item.name || '未命名知识点';
        const knowledgeId = item.knowledgeId || item.id;
        const groupName = item.groupName || '未分类';
        const topicCount = item.topicCount || 0;
        const isFree = item.isFree === 1 || item.isFree === true;

        // 检查是否已经选中（使用全局的selectedKnowledgePoints, which is an array of objects like {id: ..., name: ...})
        const isChecked = window.selectedKnowledgePoints && Array.isArray(window.selectedKnowledgePoints) && window.selectedKnowledgePoints.some(kp => kp.id.toString() === knowledgeId.toString());
        const checkedAttr = isChecked ? 'checked' : '';

        html += `
            <div class="card knowledge-point-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title" data-original-text="${escapeHtml(knowledgeName)}">${escapeHtml(knowledgeName)}</h5>
                        <div class="form-check">
                            <input class="form-check-input knowledge-checkbox" type="checkbox"
                                   value="${knowledgeId}"
                                   id="search-kp-${knowledgeId}"
                                   ${checkedAttr}>
                            <label class="form-check-label" for="search-kp-${knowledgeId}">
                                选择
                            </label>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <div>
                            <span class="badge badge-info">题目: ${topicCount}</span>
                            ${isFree ? '<span class="badge badge-success">免费</span>' : ''}
                            <span class="badge badge-primary">ID: ${knowledgeId}</span>
                            <span class="badge badge-secondary">知识点ID: ${knowledgeId}</span>
                            <span class="badge badge-light">${escapeHtml(groupName)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
        </div>
    `;

    knowledgeContainer.html(html);

    // 对结果中的关键词进行高亮处理
    if (keywords.length > 0) {
        $('.knowledge-point-card').each(function() {
            highlightSearchTerms($(this), keywords);
        });
    }

    // 确保搜索结果中的checkbox事件能正常工作
    // 由于paper-generate.js中已经使用了事件委托，这里不需要额外绑定
    // 但我们需要确保数据结构正确，以便事件处理函数能正确获取信息
}

/**
 * 显示搜索错误
 */
function showSearchError(title, message, searchTerm) {
    const knowledgeContainer = $('#knowledge-points-container');

    knowledgeContainer.html(`
        <div class="search-error">
            <div class="alert alert-danger">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mr-3"></i>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-2">${title}</h5>
                        <p class="mb-2">${message}</p>
                        ${searchTerm ? `<small class="text-muted">搜索关键词: "${escapeHtml(searchTerm)}"</small>` : ''}
                    </div>
                </div>
                <hr>
                <div class="d-flex justify-content-end">
                    <button class="btn btn-outline-danger btn-sm mr-2" onclick="performMainPageSearch('${escapeHtml(searchTerm || '')}')">
                        <i class="fas fa-redo mr-1"></i>重试搜索
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="$('#clearSearchMain').click()">
                        <i class="fas fa-times mr-1"></i>取消搜索
                    </button>
                </div>
            </div>
        </div>
    `);
}

/**
 * 高亮知识点项中的搜索关键词
 */
function highlightSearchTermsInKnowledgeItem(item, keywords) {
    const title = item.find('.knowledge-title');

    // 获取原始文本，确保不为空
    const originalTitle = title.attr('data-original-text') || title.text() || '';

    // 重置为原始文本
    title.html(originalTitle);

    // 添加新的高亮
    keywords.forEach(keyword => {
        if (keyword && keyword.length > 0) {
            const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi');

            // 高亮标题 - 确保内容不为空
            const titleContent = title.html() || '';
            if (titleContent) {
                const highlightedTitle = titleContent.replace(regex, '<mark class="search-highlight">$1</mark>');
                title.html(highlightedTitle);
            }
        }
    });
}

/**
 * 高亮标题中的搜索关键词（保留原函数以兼容其他地方的调用）
 */
function highlightSearchTerms(card, keywords) {
    const title = card.find('.card-title');
    const description = card.find('.card-text');

    // 获取原始文本，确保不为空
    const originalTitle = title.attr('data-original-text') || title.text() || '';
    const originalDescription = description.attr('data-original-text') || description.text() || '';

    // 重置为原始文本
    title.html(originalTitle);
    description.html(originalDescription);

    // 添加新的高亮
    keywords.forEach(keyword => {
        if (keyword && keyword.length > 0) {
            const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi');

            // 高亮标题 - 确保内容不为空
            const titleContent = title.html() || '';
            if (titleContent) {
                const highlightedTitle = titleContent.replace(regex, '<mark class="search-highlight">$1</mark>');
                title.html(highlightedTitle);
            }

            // 高亮描述 - 确保内容不为空
            const descriptionContent = description.html() || '';
            if (descriptionContent) {
                const highlightedDescription = descriptionContent.replace(regex, '<mark class="search-highlight">$1</mark>');
                description.html(highlightedDescription);
            }
        }
    });
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string) {
    if (!string) return '';
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 转义HTML特殊字符
 */
function escapeHtml(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * 测试搜索功能
 */
function testSearch() {
    // 设置测试关键词
    const testKeyword = '数学';
    $('#knowledgePointSearchMain').val(testKeyword);
    $('#clearSearchMain').show();

    // 执行搜索
    performMainPageSearch(testKeyword);
}