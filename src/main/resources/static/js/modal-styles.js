/**
 * Modal styles script
 * 
 * This script adds custom CSS styles to fix modal stacking issues
 */

(function() {
    console.log('Modal styles fix loaded');
    
    // Add custom CSS to handle modal stacking
    const modalStyles = `
    <style id="modal-stacking-fix">
        /* Base z-index for modals */
        .modal {
            z-index: 1050 !important;
        }
        
        /* Ensure book search modal appears on top of new chat modal */
        #bookSearchModal {
            z-index: 1060 !important;
        }
        
        /* Ensure add book modal appears on top of book search modal */
        #addBookModal {
            z-index: 1070 !important;
        }
        
        /* Handle backdrop z-indices */
        .modal-backdrop {
            z-index: 1040 !important;
        }
        
        /* Second backdrop should appear above first but below second modal */
        .modal-backdrop + .modal-backdrop {
            z-index: 1050 !important;
        }
        
        /* Third backdrop should appear above second but below third modal */
        .modal-backdrop + .modal-backdrop + .modal-backdrop {
            z-index: 1060 !important;
        }
        
        /* Fix for modals transitioning between each other */
        body.modal-transitioning .modal {
            transition: opacity 0.15s linear, z-index 0s linear 0.15s;
        }
        
        /* Fix backdrop visibility for multiple modals */
        body.modal-open .modal-backdrop {
            opacity: 0.5 !important;
        }
        
        /* Make sure modals don't disappear during transitions */
        .modal.fade {
            transition: opacity 0.15s linear;
        }
        
        /* Prevent content from shifting when modal opens */
        body.modal-open {
            overflow: hidden;
            padding-right: 17px; /* Scrollbar width */
        }
        
        /* Ensure tooltips appear above modals */
        .tooltip-container, .book-tooltip, .tooltip-box {
            z-index: 1080 !important;
        }
    </style>
    `;
    
    // Add styles to document
    $(document).ready(function() {
        // Remove existing styles first to avoid duplicates
        $('#modal-stacking-fix').remove();
        
        // Add new styles
        $('head').append(modalStyles);
        
        console.log('Modal stacking styles added');
    });
})(); 