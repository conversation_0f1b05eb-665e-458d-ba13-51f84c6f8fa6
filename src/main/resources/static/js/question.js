function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // 显示用户消息
    appendMessage(message, true);
    
    // 发送到服务器
    fetch('/api/chat/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': localStorage.getItem('token')
        },
        body: JSON.stringify({
            chatId: getCurrentChatId(),
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            appendMessage(data.data, false);
        } else {
            console.error('Error:', data.message);
        }
    })
    .catch(error => console.error('Error:', error));
    
    input.value = '';
}

function appendMessage(message, isUser) {
    const messagesDiv = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user' : 'ai'}`;
    messageDiv.textContent = message;
    messagesDiv.appendChild(messageDiv);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

function getCurrentChatId() {
    // 从 URL 或存储中获取当前会话 ID
    return new URLSearchParams(window.location.search).get('chatId');
} 