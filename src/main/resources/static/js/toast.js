/**
 * 通用 Toast 消息提示组件
 * 用于显示操作反馈和提示信息
 */

/**
 * 显示 Toast 提示消息
 * @param {string} message - 提示信息内容
 * @param {string} type - 提示类型: 'success', 'error', 'warning', 'info'
 * @param {number} duration - 显示时长(毫秒)，默认3000ms
 */
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;
    
    const toast = document.createElement('div');
    toast.className = `toast show`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // 设置样式颜色
    let bgClass = 'bg-info text-white';
    if (type === 'success') bgClass = 'bg-success text-white';
    if (type === 'error') bgClass = 'bg-danger text-white';
    if (type === 'warning') bgClass = 'bg-warning text-dark';
    
    toast.innerHTML = `
        <div class="toast-header ${bgClass}">
            <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // 自动关闭
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 500);
    }, duration);
    
    // 添加关闭按钮功能
    const closeBtn = toast.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => {
                toastContainer.removeChild(toast);
            }, 500);
        });
    }
}

/**
 * 创建 toast 容器
 * @returns {HTMLElement} 创建的容器元素
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1050';
    document.body.appendChild(container);
    return container;
}

/**
 * 获取 toast 类名
 * @param {string} type - 提示类型
 * @returns {string} 对应的类名
 */
function getToastClass(type) {
    switch(type) {
        case 'success': return 'bg-success';
        case 'error': return 'bg-danger';
        case 'warning': return 'bg-warning';
        case 'info': default: return 'bg-info';
    }
}

/**
 * 获取 toast 标题
 * @param {string} type - 提示类型
 * @returns {string} 对应的标题
 */
function getToastTitle(type) {
    switch(type) {
        case 'success': return '成功';
        case 'error': return '错误';
        case 'warning': return '警告';
        case 'info': default: return '提示';
    }
}

/**
 * 获取 toast 标题栏类名
 * @param {string} type - 提示类型
 * @returns {string} 对应的类名
 */
function getToastHeaderClass(type) {
    switch(type) {
        case 'success': return 'bg-success text-white';
        case 'error': return 'bg-danger text-white';
        case 'warning': return 'bg-warning text-white';
        case 'info': default: return 'bg-info text-white';
    }
} 