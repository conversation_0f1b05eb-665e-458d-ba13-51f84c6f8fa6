/**
 * Improved tooltip styles
 * 
 * This script adds optimized CSS styles for tooltips with:
 * - Smoother animations
 * - Better visibility
 * - Improved performance
 */

(function() {
    // Only add styles once
    if (document.getElementById('optimized-tooltip-styles')) return;
    
    // Create style element
    const styleEl = document.createElement('style');
    styleEl.id = 'optimized-tooltip-styles';
    
    // Define optimized styles
    styleEl.textContent = `
        /* Optimized tooltip container */
        .tooltip-container {
            position: fixed;
            z-index: 9999;
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 6px 16px rgba(0,0,0,0.15), 0 3px 6px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.12);
            max-width: 350px;
            width: 350px;
            pointer-events: auto;
            opacity: 0;
            transform: translateY(2px);
            animation: tooltipFadeIn 0.15s ease-out forwards;
            overflow: hidden;
            will-change: opacity, transform;
            transition: none;
        }

        /* Immediate transition for improved responsiveness */
        .tooltip-container.removing {
            opacity: 0 !important;
            transform: translateY(2px) !important;
            transition: opacity 0.15s ease-out, transform 0.15s ease-out !important;
        }

        /* Book tooltip content */
        .book-tooltip {
            padding: 12px;
            color: #212529;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Book title with better visibility */
        .book-tooltip-title {
            font-weight: 600;
            color: #0d6efd;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
            font-size: 16px;
            line-height: 1.3;
            word-break: break-word;
            cursor: pointer;
        }

        .book-tooltip-title:hover {
            color: #0a58ca;
            text-decoration: underline;
        }

        /* Book type display */
        .book-tooltip-type {
            font-style: italic;
            color: #6c757d;
            margin-bottom: 7px;
            font-size: 13px;
            display: flex;
            align-items: center;
        }

        /* Book description with custom scrollbar */
        .book-tooltip-desc {
            color: #212529;
            max-height: 150px;
            overflow-y: auto;
            line-height: 1.5;
            padding-right: 5px;
            margin-bottom: 8px;
            font-size: 14px;
            word-break: break-word;
            scrollbar-width: thin;
            scrollbar-color: #cdcdcd #f1f1f1;
        }

        /* Custom scrollbar */
        .book-tooltip-desc::-webkit-scrollbar {
            width: 6px;
        }

        .book-tooltip-desc::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .book-tooltip-desc::-webkit-scrollbar-thumb {
            background: #cdcdcd;
            border-radius: 3px;
        }

        .book-tooltip-desc::-webkit-scrollbar-thumb:hover {
            background: #ababab;
        }

        /* Empty description styling */
        .book-tooltip-no-desc {
            color: #6c757d;
            font-style: italic;
            padding: 6px 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            text-align: center;
            margin-bottom: 8px;
        }

        /* URL display */
        .book-tooltip-url {
            font-size: 13px;
            color: #0d6efd;
            background-color: #f8f9fa;
            padding: 6px 8px;
            border-radius: 4px;
            word-break: break-all;
            border-left: 3px solid #0d6efd;
        }

        /* Book title cell styling */
        .book-title-cell {
            position: relative;
            cursor: pointer;
            transition: background-color 0.1s ease;
        }

        .book-title-cell:hover {
            background-color: #f0f7ff;
        }

        /* Chapter badge */
        .chapter-badge {
            display: inline-block;
            padding: 0.2em 0.5em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
            color: #fff;
            background-color: #dc3545;
            margin-left: 8px;
        }

        /* Animation keyframes */
        @keyframes tooltipFadeIn {
            from {
                opacity: 0;
                transform: translateY(4px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    
    // Add to document head
    document.head.appendChild(styleEl);
    
    console.log('Optimized tooltip styles loaded');
})(); 