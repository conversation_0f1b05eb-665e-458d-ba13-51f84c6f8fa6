@charset "utf-8";
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td,hr,button,article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section {
    margin: 0;
    padding: 0;
}

article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section {
    display: block;
}

h1,h2,h3,h4,h5,h6 {
    font-size: 100%;
}

address,cite,dfn,em,i,u,var {
    font-style: normal;
}
strong,b {
	font-weight: normal;
}
code,kbd,pre,samp {
    font-family: courier new,courier,monospace;
}
ul,ol,li {
    list-style: none;
}

a {
    text-decoration: none;
    color: #333;
    cursor: pointer;
}
a:link,
a:visited,
a:hover,
a:active {
	text-decoration: none;
	color: #0f88eb;  /*----- 主色调颜色 -----*/
}
sup {
    vertical-align: text-top;
}

sub {
    vertical-align: text-bottom;
}

legend {
    color: #333;
}

fieldset,img {
    border: 0;
}

img,object {
    vertical-align: middle;
}

table {
    width: 100%;
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
}

body {
    color: #333;
    font-family: "Microsoft YaHei",PingFangSC-Regular,Verdana,Arial,Helvetica,sans-serif,"宋体";
    font-size: 12px;
}

/*----- 去掉虚框 -----*/
input:focus, select:focus {outline: 0 !important;} 
button::-moz-focus-inner,
input::-moz-focus-inner {border-color:transparent!important;}


/*----- mobile -----*/
html {-webkit-text-size-adjust: 100%;}
a,button,input,optgroup,select,textarea {
	-webkit-tap-highlight-color: rgba(0,0,0,0);
} 
/*去除系统默认appearance的样式,常用于IOS下移除原生样式*/
input[type="submit"], input[type="reset"], input[type="button"],input[type="text"],input[type="password"],textarea, button { 
	-webkit-appearance: none !important; 
}

/*----- 美化checkbox和radio -----*/
input[type="checkbox"],
input[type="radio"] {
	width: 20px;
	height: 20px;
	background-color:#fff;
	-webkit-appearance:none;
	border:1px solid #c9c9c9;
	border-radius: 2px;
	outline: none;
}
input[type="radio"] {border-radius: 50%;}
input[type="checkbox"]:checked,
input[type="radio"]:checked {
	background-color: #ff7800;
	border-color: #ff7800;
	background-clip: content-box;
	padding:2px;
}

/*a {
	text-decoration:none;
	word-wrap:break-word;
	outline:none;
	transition:all 0.2s ease-in-out;
	-webkit-transition:all 0.2s ease-in-out;
	-moz-transition:all 0.2s ease-in-out;
	-o-transition:all 0.2s ease-in-out;
	-ms-transition:all 0.2s ease-in-out;
}*/

/*----- 占位符样式 -----*/
/*除了Firefox是 ::[prefix]placeholder，其他浏览器都是使用 ::[prefix]input-placeholder*/
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	color: #c7c7c7;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {  /*----- IE10+ -----*/
	color: #c7c7c7;
}
input:-moz-placeholder,
textarea:-moz-placeholder {  /*----- Firefox4-18 -----*/
	color: #c7c7c7;
}
input::-moz-placeholder,
textarea::-moz-placeholder {  /*----- Firefox19+ -----*/
	color: #c7c7c7;
}
input:focus,
textarea:focus {
	border:1px solid #0f88eb;  /*----- 主色调颜色 -----*/
	/*box-shadow: 0 0 10px #ffdb81;*/
}