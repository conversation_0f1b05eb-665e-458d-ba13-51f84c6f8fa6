!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Sweetalert2=t()}(this,function(){"use strict";function V(e){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function r(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t,n){return(l=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var r=new(Function.bind.apply(e,o));return n&&u(r,n.prototype),r}).apply(null,arguments)}function d(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function p(e,t,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(n):r.value}})(e,t,n||e)}var t="SweetAlert2:",f=function(e){return Array.prototype.slice.call(e)},m=function(e){console.warn("".concat(t," ").concat(e))},R=function(e){console.error("".concat(t," ").concat(e))},n=[],h=function(e){-1===n.indexOf(e)&&(n.push(e),m(e))},M=function(e){return"function"==typeof e?e():e},H=function(e){return"object"===V(e)&&"function"==typeof e.then},e=Object.freeze({cancel:"cancel",backdrop:"overlay",close:"close",esc:"esc",timer:"timer"}),g=function(e){var t={};for(var n in e)t[e[n]]="swal2-"+e[n];return t},I=g(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","toast","toast-shown","toast-column","fade","show","hide","noanimation","close","title","header","content","actions","confirm","cancel","footer","icon","icon-text","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","validationerror","progresssteps","activeprogressstep","progresscircle","progressline","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen"]),b=g(["success","warning","info","question","error"]),y={previousBodyPadding:null},v=function(e,t){return e.classList.contains(t)},_=function(e){if(e.focus(),"file"!==e.type){var t=e.value;e.value="",e.value=t}},w=function(e,t,n){e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(function(t){e.forEach?e.forEach(function(e){n?e.classList.add(t):e.classList.remove(t)}):n?e.classList.add(t):e.classList.remove(t)}))},D=function(e,t){w(e,t,!0)},N=function(e,t){w(e,t,!1)},W=function(e,t){for(var n=0;n<e.childNodes.length;n++)if(v(e.childNodes[n],t))return e.childNodes[n]},z=function(e){e.style.opacity="",e.style.display=e.id===I.content?"block":"flex"},U=function(e){e.style.opacity="",e.style.display="none"},K=function(e){return e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C=function(){return document.body.querySelector("."+I.container)},k=function(e){var t=C();return t?t.querySelector("."+e):null},x=function(){return k(I.popup)},A=function(){var e=x();return f(e.querySelectorAll("."+I.icon))},B=function(){return k(I.title)},P=function(){return k(I.content)},S=function(){return k(I.image)},E=function(){return k(I.progresssteps)},O=function(){return k(I.confirm)},F=function(){return k(I.cancel)},Z=function(){return k(I.actions)},Q=function(){return k(I.footer)},Y=function(){return k(I.close)},$=function(){var e=f(x().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(function(e,t){return e=parseInt(e.getAttribute("tabindex")),(t=parseInt(t.getAttribute("tabindex")))<e?1:e<t?-1:0}),t=f(x().querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex="0"], [contenteditable], audio[controls], video[controls]')).filter(function(e){return"-1"!==e.getAttribute("tabindex")});return function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(e.concat(t)).filter(function(e){return K(e)})},L=function(){return!T()&&!document.body.classList.contains(I["no-backdrop"])},T=function(){return document.body.classList.contains(I["toast-shown"])},j=function(){return"undefined"==typeof window||"undefined"==typeof document},q='\n <div aria-labelledby="'.concat(I.title,'" aria-describedby="').concat(I.content,'" class="').concat(I.popup,'" tabindex="-1">\n   <div class="').concat(I.header,'">\n     <ul class="').concat(I.progresssteps,'"></ul>\n     <div class="').concat(I.icon," ").concat(b.error,'">\n       <span class="swal2-x-mark"><span class="swal2-x-mark-line-left"></span><span class="swal2-x-mark-line-right"></span></span>\n     </div>\n     <div class="').concat(I.icon," ").concat(b.question,'">\n       <span class="').concat(I["icon-text"],'">?</span>\n      </div>\n     <div class="').concat(I.icon," ").concat(b.warning,'">\n       <span class="').concat(I["icon-text"],'">!</span>\n      </div>\n     <div class="').concat(I.icon," ").concat(b.info,'">\n       <span class="').concat(I["icon-text"],'">i</span>\n      </div>\n     <div class="').concat(I.icon," ").concat(b.success,'">\n       <div class="swal2-success-circular-line-left"></div>\n       <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n       <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n       <div class="swal2-success-circular-line-right"></div>\n     </div>\n     <img class="').concat(I.image,'" />\n     <h2 class="').concat(I.title,'" id="').concat(I.title,'"></h2>\n     <button type="button" class="').concat(I.close,'">×</button>\n   </div>\n   <div class="').concat(I.content,'">\n     <div id="').concat(I.content,'"></div>\n     <input class="').concat(I.input,'" />\n     <input type="file" class="').concat(I.file,'" />\n     <div class="').concat(I.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(I.select,'"></select>\n     <div class="').concat(I.radio,'"></div>\n     <label for="').concat(I.checkbox,'" class="').concat(I.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(I.label,'"></span>\n     </label>\n     <textarea class="').concat(I.textarea,'"></textarea>\n     <div class="').concat(I.validationerror,'" id="').concat(I.validationerror,'"></div>\n   </div>\n   <div class="').concat(I.actions,'">\n     <button type="button" class="').concat(I.confirm,'">OK</button>\n     <button type="button" class="').concat(I.cancel,'">Cancel</button>\n   </div>\n   <div class="').concat(I.footer,'">\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),J=function(e){var t=C();if(t&&(t.parentNode.removeChild(t),N([document.documentElement,document.body],[I["no-backdrop"],I["toast-shown"],I["has-column"]])),!j()){var n=document.createElement("div");n.className=I.container,n.innerHTML=q,("string"==typeof e.target?document.querySelector(e.target):e.target).appendChild(n);var o,r=x(),i=P(),a=W(i,I.input),c=W(i,I.file),s=i.querySelector(".".concat(I.range," input")),u=i.querySelector(".".concat(I.range," output")),l=W(i,I.select),d=i.querySelector(".".concat(I.checkbox," input")),p=W(i,I.textarea);r.setAttribute("role",e.toast?"alert":"dialog"),r.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||r.setAttribute("aria-modal","true");var f=function(e){qe.isVisible()&&o!==e.target.value&&qe.resetValidationError(),o=e.target.value};return a.oninput=f,c.onchange=f,l.onchange=f,d.onchange=f,p.oninput=f,s.oninput=function(e){f(e),u.value=s.value},s.onchange=function(e){f(e),s.nextSibling.value=s.value},r}R("SweetAlert2 requires document to initialize")},X=function(e,t){if(!e)return U(t);if("object"===V(e))if(t.innerHTML="",0 in e)for(var n=0;n in e;n++)t.appendChild(e[n].cloneNode(!0));else t.appendChild(e.cloneNode(!0));else e&&(t.innerHTML=e);z(t)},G=function(){if(j())return!1;var e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(var n in t)if(t.hasOwnProperty(n)&&void 0!==e.style[n])return t[n];return!1}(),ee=function(e){var t,n,o=Z(),r=O(),i=F();if(e.showConfirmButton||e.showCancelButton?z(o):U(o),e.showCancelButton?i.style.display="inline-block":U(i),e.showConfirmButton?(n="display",(t=r).style.removeProperty?t.style.removeProperty(n):t.style.removeAttribute(n)):U(r),r.innerHTML=e.confirmButtonText,i.innerHTML=e.cancelButtonText,r.setAttribute("aria-label",e.confirmButtonAriaLabel),i.setAttribute("aria-label",e.cancelButtonAriaLabel),r.className=I.confirm,D(r,e.confirmButtonClass),i.className=I.cancel,D(i,e.cancelButtonClass),e.buttonsStyling){D([r,i],I.styled),e.confirmButtonColor&&(r.style.backgroundColor=e.confirmButtonColor),e.cancelButtonColor&&(i.style.backgroundColor=e.cancelButtonColor);var a=window.getComputedStyle(r).getPropertyValue("background-color");r.style.borderLeftColor=a,r.style.borderRightColor=a}else N([r,i],I.styled),r.style.backgroundColor=r.style.borderLeftColor=r.style.borderRightColor="",i.style.backgroundColor=i.style.borderLeftColor=i.style.borderRightColor=""},te=function(e){var t=P().querySelector("#"+I.content);e.html?X(e.html,t):e.text?(t.textContent=e.text,z(t)):U(t)},ne=function(e){for(var t=A(),n=0;n<t.length;n++)U(t[n]);if(e.type)if(-1!==Object.keys(b).indexOf(e.type)){var o=qe.getPopup().querySelector(".".concat(I.icon,".").concat(b[e.type]));z(o),e.animation&&D(o,"swal2-animate-".concat(e.type,"-icon"))}else R('Unknown type! Expected "success", "error", "warning", "info" or "question", got "'.concat(e.type,'"'))},oe=function(e){var t=S();e.imageUrl?(t.setAttribute("src",e.imageUrl),t.setAttribute("alt",e.imageAlt),z(t),e.imageWidth?t.setAttribute("width",e.imageWidth):t.removeAttribute("width"),e.imageHeight?t.setAttribute("height",e.imageHeight):t.removeAttribute("height"),t.className=I.image,e.imageClass&&D(t,e.imageClass)):U(t)},re=function(r){var i=E(),a=parseInt(null===r.currentProgressStep?qe.getQueueStep():r.currentProgressStep,10);r.progressSteps&&r.progressSteps.length?(z(i),i.innerHTML="",a>=r.progressSteps.length&&m("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),r.progressSteps.forEach(function(e,t){var n=document.createElement("li");if(D(n,I.progresscircle),n.innerHTML=e,t===a&&D(n,I.activeprogressstep),i.appendChild(n),t!==r.progressSteps.length-1){var o=document.createElement("li");D(o,I.progressline),r.progressStepsDistance&&(o.style.width=r.progressStepsDistance),i.appendChild(o)}})):U(i)},ie=function(e){var t=B();e.titleText?t.innerText=e.titleText:e.title&&("string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),X(e.title,t))},ae=function(){null===y.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(y.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=y.previousBodyPadding+function(){if("ontouchstart"in window||navigator.msMaxTouchPoints)return 0;var e=document.createElement("div");e.style.width="50px",e.style.height="50px",e.style.overflow="scroll",document.body.appendChild(e);var t=e.offsetWidth-e.clientWidth;return document.body.removeChild(e),t}()+"px")},ce={},se=function(e,n){var o=C(),t=x();if(t){null!==e&&"function"==typeof e&&e(t),N(t,I.show),D(t,I.hide);var r=function(){var e,t;T()||(e=window.scrollX,t=window.scrollY,ce.restoreFocusTimeout=setTimeout(function(){ce.previousActiveElement&&ce.previousActiveElement.focus?(ce.previousActiveElement.focus(),ce.previousActiveElement=null):document.body&&document.body.focus()},100),void 0!==e&&void 0!==t&&window.scrollTo(e,t),ce.keydownTarget.removeEventListener("keydown",ce.keydownHandler,{capture:ce.keydownListenerCapture}),ce.keydownHandlerAdded=!1),o.parentNode&&o.parentNode.removeChild(o),N([document.documentElement,document.body],[I.shown,I["height-auto"],I["no-backdrop"],I["toast-shown"],I["toast-column"]]),L()&&(null!==y.previousBodyPadding&&(document.body.style.paddingRight=y.previousBodyPadding,y.previousBodyPadding=null),function(){if(v(document.body,I.iosfix)){var e=parseInt(document.body.style.top,10);N(document.body,I.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}}(),f(document.body.children).forEach(function(e){e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")})),null!==n&&"function"==typeof n&&setTimeout(function(){n()})};G&&!v(t,I.noanimation)?t.addEventListener(G,function e(){t.removeEventListener(G,e),v(t,I.hide)&&r()}):r()}};function ue(e){var t=function e(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];if(!(this instanceof e))return l(e,n);Object.getPrototypeOf(e).apply(this,n)};return t.prototype=i(Object.create(e.prototype),{constructor:t}),"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e,t}var le={title:"",titleText:"",text:"",html:"",footer:"",type:null,toast:!1,customClass:"",target:"body",backdrop:!0,animation:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showCancelButton:!1,preConfirm:null,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:null,confirmButtonClass:null,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:null,cancelButtonClass:null,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusCancel:!1,showCloseButton:!1,closeButtonAriaLabel:"Close this dialog",showLoaderOnConfirm:!1,imageUrl:null,imageWidth:null,imageHeight:null,imageAlt:"",imageClass:null,timer:null,width:null,padding:null,background:null,input:null,inputPlaceholder:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputClass:null,inputAttributes:{},inputValidator:null,grow:!1,position:"center",progressSteps:[],currentProgressStep:null,progressStepsDistance:null,onBeforeOpen:null,onAfterClose:null,onOpen:null,onClose:null,useRejections:!1,expectRejections:!1},de=["useRejections","expectRejections"],pe=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusCancel","heightAuto","keydownListenerCapture"],fe=function(e){return le.hasOwnProperty(e)||"extraParams"===e},me=function(e){return-1!==de.indexOf(e)},he=function(e){for(var t in e)fe(t)||m('Unknown parameter "'.concat(t,'"')),e.toast&&-1!==pe.indexOf(t)&&m('The parameter "'.concat(t,'" is incompatible with toasts')),me(t)&&h('The parameter "'.concat(t,'" is deprecated and will be removed in the next major release.'))},ge='"setDefaults" & "resetDefaults" methods are deprecated in favor of "mixin" method and will be removed in the next major release. For new projects, use "mixin". For past projects already using "setDefaults", support will be provided through an additional package.',be={};var ye=[],ve=function(){var e=x();e||qe(""),e=x();var t=Z(),n=O(),o=F();z(t),z(n),D([e,t],I.loading),n.disabled=!0,o.disabled=!0,e.setAttribute("data-loading",!0),e.setAttribute("aria-busy",!0),e.focus()},we=Object.freeze({isValidParameter:fe,isDeprecatedParameter:me,argsToParams:function(n){var o={};switch(V(n[0])){case"object":i(o,n[0]);break;default:["title","html","type"].forEach(function(e,t){switch(V(n[t])){case"string":o[e]=n[t];break;case"undefined":break;default:R("Unexpected type of ".concat(e,'! Expected "string", got ').concat(V(n[t])))}})}return o},adaptInputValidator:function(n){return function(e,t){return n.call(this,e,t).then(function(){},function(e){return e})}},close:se,closePopup:se,closeModal:se,closeToast:se,isVisible:function(){return!!x()},clickConfirm:function(){return O().click()},clickCancel:function(){return F().click()},getContainer:C,getPopup:x,getTitle:B,getContent:P,getImage:S,getIcons:A,getCloseButton:Y,getButtonsWrapper:function(){return h("swal.getButtonsWrapper() is deprecated and will be removed in the next major release, use swal.getActions() instead"),k(I.actions)},getActions:Z,getConfirmButton:O,getCancelButton:F,getFooter:Q,getFocusableElements:$,isLoading:function(){return x().hasAttribute("data-loading")},fire:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l(this,t)},mixin:function(n){return ue(function(e){function t(){return c(this,t),d(this,s(t).apply(this,arguments))}return a(t,e),r(t,[{key:"_main",value:function(e){return p(s(t.prototype),"_main",this).call(this,i({},n,e))}}]),t}(this))},queue:function(e){var i=this;ye=e;var a=function(){ye=[],document.body.removeAttribute("data-swal2-queue-step")},c=[];return new Promise(function(r){!function t(n,o){n<ye.length?(document.body.setAttribute("data-swal2-queue-step",n),i(ye[n]).then(function(e){void 0!==e.value?(c.push(e.value),t(n+1,o)):(a(),r({dismiss:e.dismiss}))})):(a(),r({value:c}))}(0)})},getQueueStep:function(){return document.body.getAttribute("data-swal2-queue-step")},insertQueueStep:function(e,t){return t&&t<ye.length?ye.splice(t,0,e):ye.push(e)},deleteQueueStep:function(e){void 0!==ye[e]&&ye.splice(e,1)},showLoading:ve,enableLoading:ve,getTimerLeft:function(){return ce.timeout&&ce.timeout.getTimerLeft()}}),Ce="function"==typeof Symbol?Symbol:function(){var t=0;function e(e){return"__"+e+"_"+Math.floor(1e9*Math.random())+"_"+ ++t+"__"}return e.iterator=e("Symbol.iterator"),e}(),ke="function"==typeof WeakMap?WeakMap:function(n,o,t){function e(){o(this,n,{value:Ce("WeakMap")})}return e.prototype={delete:function(e){delete e[this[n]]},get:function(e){return e[this[n]]},has:function(e){return t.call(e,this[n])},set:function(e,t){o(e,this[n],{configurable:!0,value:t})}},e}(Ce("WeakMap"),Object.defineProperty,{}.hasOwnProperty),xe={promise:new ke,innerParams:new ke,domCache:new ke};function Ae(){var e=xe.innerParams.get(this),t=xe.domCache.get(this);e.showConfirmButton||(U(t.confirmButton),e.showCancelButton||U(t.actions)),N([t.popup,t.actions],I.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.cancelButton.disabled=!1}var Be=function e(t,n){var o,r,i;c(this,e);var a=n;this.start=function(){i=!0,r=new Date,o=setTimeout(t,a)},this.stop=function(){i=!1,clearTimeout(o),a-=new Date-r},this.getTimerLeft=function(){return i&&(this.stop(),this.start()),a},this.start()},Pe={email:function(e,t){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.reject(t&&t.validationMessage?t.validationMessage:"Invalid email address")},url:function(e,t){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)$/.test(e)?Promise.resolve():Promise.reject(t&&t.validationMessage?t.validationMessage:"Invalid URL")}};var Se=function(e){var t=C(),n=x();null!==e.onBeforeOpen&&"function"==typeof e.onBeforeOpen&&e.onBeforeOpen(n),e.animation?(D(n,I.show),D(t,I.fade),N(n,I.hide)):N(n,I.fade),z(n),t.style.overflowY="hidden",G&&!v(n,I.noanimation)?n.addEventListener(G,function e(){n.removeEventListener(G,e),t.style.overflowY="auto"}):t.style.overflowY="auto",D([document.documentElement,document.body,t],I.shown),e.heightAuto&&e.backdrop&&!e.toast&&D([document.documentElement,document.body],I["height-auto"]),L()&&(ae(),function(){if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&!v(document.body,I.iosfix)){var e=document.body.scrollTop;document.body.style.top=-1*e+"px",D(document.body,I.iosfix)}}(),f(document.body.children).forEach(function(e){e===C()||e.contains(C())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))})),T()||ce.previousActiveElement||(ce.previousActiveElement=document.activeElement),null!==e.onOpen&&"function"==typeof e.onOpen&&setTimeout(function(){e.onOpen(n)})};var Ee,Oe=Object.freeze({hideLoading:Ae,disableLoading:Ae,getInput:function(e){var t=xe.innerParams.get(this),n=xe.domCache.get(this);if(!(e=e||t.input))return null;switch(e){case"select":case"textarea":case"file":return W(n.content,I[e]);case"checkbox":return n.popup.querySelector(".".concat(I.checkbox," input"));case"radio":return n.popup.querySelector(".".concat(I.radio," input:checked"))||n.popup.querySelector(".".concat(I.radio," input:first-child"));case"range":return n.popup.querySelector(".".concat(I.range," input"));default:return W(n.content,I.input)}},enableButtons:function(){var e=xe.domCache.get(this);e.confirmButton.disabled=!1,e.cancelButton.disabled=!1},disableButtons:function(){var e=xe.domCache.get(this);e.confirmButton.disabled=!0,e.cancelButton.disabled=!0},enableConfirmButton:function(){xe.domCache.get(this).confirmButton.disabled=!1},disableConfirmButton:function(){xe.domCache.get(this).confirmButton.disabled=!0},enableInput:function(){var e=this.getInput();if(!e)return!1;if("radio"===e.type)for(var t=e.parentNode.parentNode.querySelectorAll("input"),n=0;n<t.length;n++)t[n].disabled=!1;else e.disabled=!1},disableInput:function(){var e=this.getInput();if(!e)return!1;if(e&&"radio"===e.type)for(var t=e.parentNode.parentNode.querySelectorAll("input"),n=0;n<t.length;n++)t[n].disabled=!0;else e.disabled=!0},showValidationError:function(e){var t=xe.domCache.get(this);t.validationError.innerHTML=e;var n=window.getComputedStyle(t.popup);t.validationError.style.marginLeft="-".concat(n.getPropertyValue("padding-left")),t.validationError.style.marginRight="-".concat(n.getPropertyValue("padding-right")),z(t.validationError);var o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedBy",I.validationerror),_(o),D(o,I.inputerror))},resetValidationError:function(){var e=xe.domCache.get(this);e.validationError&&U(e.validationError);var t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedBy"),N(t,I.inputerror))},getProgressSteps:function(){return xe.innerParams.get(this).progressSteps},setProgressSteps:function(e){var t=i({},xe.innerParams.get(this),{progressSteps:e});xe.innerParams.set(this,t),re(t)},showProgressSteps:function(){var e=xe.domCache.get(this);z(e.progressSteps)},hideProgressSteps:function(){var e=xe.domCache.get(this);U(e.progressSteps)},_main:function(e){var L=this;he(e);var T=i({},le,e);!function(t){var e;t.inputValidator||Object.keys(Pe).forEach(function(e){t.input===e&&(t.inputValidator=t.expectRejections?Pe[e]:qe.adaptInputValidator(Pe[e]))}),(!t.target||"string"==typeof t.target&&!document.querySelector(t.target)||"string"!=typeof t.target&&!t.target.appendChild)&&(m('Target parameter is not valid, defaulting to "body"'),t.target="body");var n=x(),o="string"==typeof t.target?document.querySelector(t.target):t.target;e=n&&o&&n.parentNode!==o.parentNode?J(t):n||J(t),t.width&&(e.style.width="number"==typeof t.width?t.width+"px":t.width),t.padding&&(e.style.padding="number"==typeof t.padding?t.padding+"px":t.padding),t.background&&(e.style.background=t.background);for(var r=window.getComputedStyle(e).getPropertyValue("background-color"),i=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),a=0;a<i.length;a++)i[a].style.backgroundColor=r;var c=C(),s=Y(),u=Q();if(ie(t),te(t),"string"==typeof t.backdrop?C().style.background=t.backdrop:t.backdrop||D([document.documentElement,document.body],I["no-backdrop"]),!t.backdrop&&t.allowOutsideClick&&m('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),t.position in I?D(c,I[t.position]):(m('The "position" parameter is not valid, defaulting to "center"'),D(c,I.center)),t.grow&&"string"==typeof t.grow){var l="grow-"+t.grow;l in I&&D(c,I[l])}"function"==typeof t.animation&&(t.animation=t.animation.call()),t.showCloseButton?(s.setAttribute("aria-label",t.closeButtonAriaLabel),z(s)):U(s),e.className=I.popup,t.toast?(D([document.documentElement,document.body],I["toast-shown"]),D(e,I.toast)):D(e,I.modal),t.customClass&&D(e,t.customClass),re(t),ne(t),oe(t),ee(t),X(t.footer,u),!0===t.animation?N(e,I.noanimation):D(e,I.noanimation),t.showLoaderOnConfirm&&!t.preConfirm&&m("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request")}(T),Object.freeze(T),xe.innerParams.set(this,T),ce.timeout&&(ce.timeout.stop(),delete ce.timeout),clearTimeout(ce.restoreFocusTimeout);var j={popup:x(),container:C(),content:P(),actions:Z(),confirmButton:O(),cancelButton:F(),closeButton:Y(),validationError:k(I.validationerror),progressSteps:E()};xe.domCache.set(this,j);var q=this.constructor;return new Promise(function(t,n){var o=function(e){q.closePopup(T.onClose,T.onAfterClose),T.useRejections?t(e):t({value:e})},s=function(e){q.closePopup(T.onClose,T.onAfterClose),T.useRejections?n(e):t({dismiss:e})},u=function(e){q.closePopup(T.onClose,T.onAfterClose),n(e)};T.timer&&(ce.timeout=new Be(function(){s("timer"),delete ce.timeout},T.timer)),T.input&&setTimeout(function(){var e=L.getInput();e&&_(e)},0);for(var l=function(t){if(T.showLoaderOnConfirm&&q.showLoading(),T.preConfirm){L.resetValidationError();var e=Promise.resolve().then(function(){return T.preConfirm(t,T.extraParams)});T.expectRejections?e.then(function(e){return o(e||t)},function(e){L.hideLoading(),e&&L.showValidationError(e)}):e.then(function(e){K(j.validationError)||!1===e?L.hideLoading():o(e||t)},function(e){return u(e)})}else o(t)},e=function(e){var t=e.target,n=j.confirmButton,o=j.cancelButton,r=n&&(n===t||n.contains(t)),i=o&&(o===t||o.contains(t));switch(e.type){case"click":if(r&&q.isVisible())if(L.disableButtons(),T.input){var a=function(){var e=L.getInput();if(!e)return null;switch(T.input){case"checkbox":return e.checked?1:0;case"radio":return e.checked?e.value:null;case"file":return e.files.length?e.files[0]:null;default:return T.inputAutoTrim?e.value.trim():e.value}}();if(T.inputValidator){L.disableInput();var c=Promise.resolve().then(function(){return T.inputValidator(a,T.extraParams)});T.expectRejections?c.then(function(){L.enableButtons(),L.enableInput(),l(a)},function(e){L.enableButtons(),L.enableInput(),e&&L.showValidationError(e)}):c.then(function(e){L.enableButtons(),L.enableInput(),e?L.showValidationError(e):l(a)},function(e){return u(e)})}else l(a)}else l(!0);else i&&q.isVisible()&&(L.disableButtons(),s(q.DismissReason.cancel))}},r=j.popup.querySelectorAll("button"),i=0;i<r.length;i++)r[i].onclick=e,r[i].onmouseover=e,r[i].onmouseout=e,r[i].onmousedown=e;if(j.closeButton.onclick=function(){s(q.DismissReason.close)},T.toast)j.popup.onclick=function(){T.showConfirmButton||T.showCancelButton||T.showCloseButton||T.input||s(q.DismissReason.close)};else{var a=!1;j.popup.onmousedown=function(){j.container.onmouseup=function(e){j.container.onmouseup=void 0,e.target===j.container&&(a=!0)}},j.container.onmousedown=function(){j.popup.onmouseup=function(e){j.popup.onmouseup=void 0,(e.target===j.popup||j.popup.contains(e.target))&&(a=!0)}},j.container.onclick=function(e){a?a=!1:e.target===j.container&&M(T.allowOutsideClick)&&s(q.DismissReason.backdrop)}}T.reverseButtons?j.confirmButton.parentNode.insertBefore(j.cancelButton,j.confirmButton):j.confirmButton.parentNode.insertBefore(j.confirmButton,j.cancelButton);var c=function(e,t){for(var n=$(T.focusCancel),o=0;o<n.length;o++)return(e+=t)===n.length?e=0:-1===e&&(e=n.length-1),n[e].focus();j.popup.focus()};ce.keydownHandlerAdded&&(ce.keydownTarget.removeEventListener("keydown",ce.keydownHandler,{capture:ce.keydownListenerCapture}),ce.keydownHandlerAdded=!1),T.toast||(ce.keydownHandler=function(e){return function(e,t){if(t.stopKeydownPropagation&&e.stopPropagation(),"Enter"!==e.key||e.isComposing)if("Tab"===e.key){for(var n=e.target,o=$(t.focusCancel),r=-1,i=0;i<o.length;i++)if(n===o[i]){r=i;break}e.shiftKey?c(r,-1):c(r,1),e.stopPropagation(),e.preventDefault()}else-1!==["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Left","Right","Up","Down"].indexOf(e.key)?document.activeElement===j.confirmButton&&K(j.cancelButton)?j.cancelButton.focus():document.activeElement===j.cancelButton&&K(j.confirmButton)&&j.confirmButton.focus():"Escape"!==e.key&&"Esc"!==e.key||!0!==M(t.allowEscapeKey)||s(q.DismissReason.esc);else if(e.target&&L.getInput()&&e.target.outerHTML===L.getInput().outerHTML){if(-1!==["textarea","file"].indexOf(t.input))return;q.clickConfirm(),e.preventDefault()}}(e,T)},ce.keydownTarget=T.keydownListenerCapture?window:j.popup,ce.keydownListenerCapture=T.keydownListenerCapture,ce.keydownTarget.addEventListener("keydown",ce.keydownHandler,{capture:ce.keydownListenerCapture}),ce.keydownHandlerAdded=!0),L.enableButtons(),L.hideLoading(),L.resetValidationError(),T.toast&&(T.input||T.footer||T.showCloseButton)?D(document.body,I["toast-column"]):N(document.body,I["toast-column"]);for(var d,p,f=["input","file","range","select","radio","checkbox","textarea"],m=0;m<f.length;m++){var h=I[f[m]],g=W(j.content,h);if(d=L.getInput(f[m])){for(var b in d.attributes)if(d.attributes.hasOwnProperty(b)){var y=d.attributes[b].name;"type"!==y&&"value"!==y&&d.removeAttribute(y)}for(var v in T.inputAttributes)d.setAttribute(v,T.inputAttributes[v])}g.className=h,T.inputClass&&D(g,T.inputClass),U(g)}switch(T.input){case"text":case"email":case"password":case"number":case"tel":case"url":(d=W(j.content,I.input)).value=T.inputValue,d.placeholder=T.inputPlaceholder,d.type=T.input,z(d);break;case"file":(d=W(j.content,I.file)).placeholder=T.inputPlaceholder,d.type=T.input,z(d);break;case"range":var w=W(j.content,I.range),C=w.querySelector("input"),k=w.querySelector("output");C.value=T.inputValue,C.type=T.input,k.value=T.inputValue,z(w);break;case"select":var x=W(j.content,I.select);if(x.innerHTML="",T.inputPlaceholder){var A=document.createElement("option");A.innerHTML=T.inputPlaceholder,A.value="",A.disabled=!0,A.selected=!0,x.appendChild(A)}p=function(e){e.forEach(function(e){var t=e[0],n=e[1],o=document.createElement("option");o.value=t,o.innerHTML=n,T.inputValue.toString()===t.toString()&&(o.selected=!0),x.appendChild(o)}),z(x),x.focus()};break;case"radio":var B=W(j.content,I.radio);B.innerHTML="",p=function(e){e.forEach(function(e){var t=e[0],n=e[1],o=document.createElement("input"),r=document.createElement("label");o.type="radio",o.name=I.radio,o.value=t,T.inputValue.toString()===t.toString()&&(o.checked=!0);var i=document.createElement("span");i.innerHTML=n,i.className=I.label,r.appendChild(o),r.appendChild(i),B.appendChild(r)}),z(B);var t=B.querySelectorAll("input");t.length&&t[0].focus()};break;case"checkbox":var P=W(j.content,I.checkbox),S=L.getInput("checkbox");S.type="checkbox",S.value=1,S.id=I.checkbox,S.checked=Boolean(T.inputValue),P.querySelector("span").innerHTML=T.inputPlaceholder,z(P);break;case"textarea":var E=W(j.content,I.textarea);E.value=T.inputValue,E.placeholder=T.inputPlaceholder,z(E);break;case null:break;default:R('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(T.input,'"'))}if("select"===T.input||"radio"===T.input){var O=function(e){return p((t=e,n=[],"undefined"!=typeof Map&&t instanceof Map?t.forEach(function(e,t){n.push([t,e])}):Object.keys(t).forEach(function(e){n.push([e,t[e]])}),n));var t,n};H(T.inputOptions)?(q.showLoading(),T.inputOptions.then(function(e){L.hideLoading(),O(e)})):"object"===V(T.inputOptions)?O(T.inputOptions):R("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(V(T.inputOptions)))}else-1!==["text","email","number","tel","textarea"].indexOf(T.input)&&H(T.inputValue)&&(q.showLoading(),U(d),T.inputValue.then(function(e){d.value="number"===T.input?parseFloat(e)||0:e+"",z(d),d.focus(),L.hideLoading()}).catch(function(e){R("Error in inputValue promise: "+e),d.value="",z(d),d.focus(),L.hideLoading()}));Se(T),T.toast||(M(T.allowEnterKey)?T.focusCancel&&K(j.cancelButton)?j.cancelButton.focus():T.focusConfirm&&K(j.confirmButton)?j.confirmButton.focus():c(-1,1):document.activeElement&&document.activeElement.blur()),j.container.scrollTop=0})}});function Le(){if("undefined"!=typeof window){"undefined"==typeof Promise&&R("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)");for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return R("At least 1 argument is expected!"),!1;Ee=this;var o=Object.freeze(this.constructor.argsToParams(t));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0}});var r=this._main(this.params);xe.promise.set(this,r)}}Le.prototype.then=function(e,t){return xe.promise.get(this).then(e,t)},Le.prototype.catch=function(e){return xe.promise.get(this).catch(e)},Le.prototype.finally=function(e){return xe.promise.get(this).finally(e)},i(Le.prototype,Oe),i(Le,we),Object.keys(Oe).forEach(function(t){Le[t]=function(){var e;if(Ee)return(e=Ee)[t].apply(e,arguments)}}),Le.DismissReason=e,Le.noop=function(){},Le.version="7.26.28";var Te,je,qe=ue((Te=Le,je=function(e){function t(){return c(this,t),d(this,s(t).apply(this,arguments))}return a(t,Te),r(t,[{key:"_main",value:function(e){return p(s(t.prototype),"_main",this).call(this,i({},be,e))}}],[{key:"setDefaults",value:function(t){if(h(ge),!t||"object"!==V(t))throw new TypeError("SweetAlert2: The argument for setDefaults() is required and has to be a object");he(t),Object.keys(t).forEach(function(e){Te.isValidParameter(e)&&(be[e]=t[e])})}},{key:"resetDefaults",value:function(){h(ge),be={}}}]),t}(),"undefined"!=typeof window&&"object"===V(window._swalDefaults)&&je.setDefaults(window._swalDefaults),je));return qe.default=qe}),"undefined"!=typeof window&&window.Sweetalert2&&(window.swal=window.sweetAlert=window.Swal=window.SweetAlert=window.Sweetalert2);