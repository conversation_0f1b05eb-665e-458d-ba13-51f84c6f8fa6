<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>遗传算法监控 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/admin/assets/css/base.css">
    <link rel="stylesheet" href="/admin/assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="/admin/assets/css/algorithm-monitor.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="notificationDropdown" role="button"
                            data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu notification-dropdown">
                            <li>
                                <h6 class="dropdown-header">通知</h6>
                            </li>
                            <li><a class="dropdown-item notification-item" href="#">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    算法收敛异常检测
                                </a></li>
                            <li><a class="dropdown-item notification-item" href="#">
                                    <i class="fas fa-chart-line text-info me-2"></i>
                                    新的试卷生成完成
                                </a></li>
                            <li><a class="dropdown-item notification-item" href="#">
                                    <i class="fas fa-dna text-success me-2"></i>
                                    算法性能优化建议
                                </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i>
                            管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="logout()"><i
                                        class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 侧边栏和主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div
                    class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-dna me-2"></i>
                        遗传算法监控
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportReport()">
                                <i class="fas fa-download"></i> 导出报告
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="showSettings()">
                                <i class="fas fa-cog"></i> 监控设置
                            </button>
                        </div>
                        <button type="button" class="btn btn-sm btn-primary" onclick="refreshMonitor()">
                            <i class="fas fa-sync"></i> 刷新数据
                        </button>
                    </div>
                </div>

                <!-- 算法状态概览 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card border-left-primary h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">运行中的算法
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="runningAlgorithms">3
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card border-left-success h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">今日生成试卷
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayPapers">127</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card border-left-info h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">平均收敛代数</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgGenerations">85</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="card border-left-warning h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">异常检测
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="anomalyCount">2</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 试卷选择和筛选 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-filter me-2"></i>
                                    试卷筛选与选择
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="paperSelect" class="form-label">选择试卷</label>
                                        <select class="form-select" id="paperSelect" onchange="onPaperChange()">
                                            <option value="">-- 选择试卷 --</option>
                                            <!-- 试卷选项将通过JavaScript动态加载 -->
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateRange" class="form-label">时间范围</label>
                                        <select class="form-select" id="dateRange" onchange="onDateRangeChange()">
                                            <option value="today">今天</option>
                                            <option value="week">本周</option>
                                            <option value="month">本月</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="statusFilter" class="form-label">状态筛选</label>
                                        <select class="form-select" id="statusFilter" onchange="onStatusFilterChange()">
                                            <option value="all">全部</option>
                                            <option value="running">运行中</option>
                                            <option value="completed">已完成</option>
                                            <option value="failed">失败</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="performanceFilter" class="form-label">性能筛选</label>
                                        <select class="form-select" id="performanceFilter"
                                            onchange="onPerformanceFilterChange()">
                                            <option value="all">全部</option>
                                            <option value="excellent">优秀</option>
                                            <option value="good">良好</option>
                                            <option value="poor">较差</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要监控面板 -->
                <div class="row">
                    <!-- 多维度适应度分析 -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-radar me-2"></i>
                                    多维度适应度分析
                                </h5>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="toggleRealTime('fitness')">
                                        <i class="fas fa-play" id="fitnessRealTimeIcon"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="exportChart('fitness')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="fitnessRadarChart" style="height: 400px;"></div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">当前最优适应度</small>
                                            <div class="h6 mb-0" id="currentBestFitness">0.8547</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">适应度改进率</small>
                                            <div class="h6 mb-0 text-success" id="fitnessImprovement">+12.3%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 种群进化轨迹 -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-fire me-2"></i>
                                    种群进化热力图
                                </h5>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="toggleHeatmapMetric()">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="exportChart('heatmap')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="populationHeatmap" style="height: 400px;"></div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">当前代数</small>
                                            <div class="h6 mb-0" id="currentGeneration">85</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">种群多样性</small>
                                            <div class="h6 mb-0 text-info" id="populationDiversity">0.6234</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 约束处理分析 -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    约束处理分析
                                </h5>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="showConstraintDetails()">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="exportChart('constraint')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="constraintAnalysisChart" style="height: 300px;"></div>
                                <div class="mt-3">
                                    <div class="constraint-stats">
                                        <div class="row">
                                            <div class="col-4 text-center">
                                                <div class="constraint-stat">
                                                    <div class="stat-value text-success">92%</div>
                                                    <div class="stat-label">满足率</div>
                                                </div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="constraint-stat">
                                                    <div class="stat-value text-warning">15</div>
                                                    <div class="stat-label">违反次数</div>
                                                </div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="constraint-stat">
                                                    <div class="stat-value text-info">87%</div>
                                                    <div class="stat-label">修复成功率</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收敛性深度分析 -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    收敛性深度分析
                                </h5>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="toggleConvergenceView()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="exportChart('convergence')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="convergenceAnalysisChart" style="height: 300px;"></div>
                                <div class="mt-3">
                                    <div class="convergence-indicators">
                                        <div class="row">
                                            <div class="col-3 text-center">
                                                <div class="indicator">
                                                    <div class="indicator-value text-primary">0.023</div>
                                                    <div class="indicator-label">收敛速度</div>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center">
                                                <div class="indicator">
                                                    <div class="indicator-value text-success">稳定</div>
                                                    <div class="indicator-label">收敛状态</div>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center">
                                                <div class="indicator">
                                                    <div class="indicator-value text-warning">否</div>
                                                    <div class="indicator-label">早熟收敛</div>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center">
                                                <div class="indicator">
                                                    <div class="indicator-value text-info">12</div>
                                                    <div class="indicator-label">停滞期</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细数据表格 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    算法执行详情
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="algorithmDetailsTable">
                                        <thead>
                                            <tr>
                                                <th>试卷ID</th>
                                                <th>开始时间</th>
                                                <th>状态</th>
                                                <th>当前代数</th>
                                                <th>最佳适应度</th>
                                                <th>收敛速度</th>
                                                <th>约束满足率</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="algorithmDetailsBody">
                                            <!-- 动态加载数据 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-cog me-2"></i>
                        监控设置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>刷新设置</h6>
                            <div class="mb-3">
                                <label for="refreshInterval" class="form-label">刷新间隔 (秒)</label>
                                <input type="number" class="form-control" id="refreshInterval" value="5" min="1"
                                    max="60">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                    <label class="form-check-label" for="autoRefresh">
                                        自动刷新
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>显示设置</h6>
                            <div class="mb-3">
                                <label for="maxDataPoints" class="form-label">最大数据点数</label>
                                <input type="number" class="form-control" id="maxDataPoints" value="100"
                                    min="50" max="500">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showAnimations" checked>
                                    <label class="form-check-label" for="showAnimations">
                                        显示动画效果
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/admin/assets/js/algorithm-monitor.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            initializeMonitor();
        });

        // 注销功能
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                window.location.href = '/admin/logout';
            }
        }
    </script>
</body>

</html>