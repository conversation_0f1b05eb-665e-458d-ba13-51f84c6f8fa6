<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷配置管理 - 麦子教育系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!--  通用导航栏样式 -->
    <link rel="stylesheet" href="/static/css/navbar.css">

    <style>
        /*  统计卡片样式 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        /*  用户下拉菜单样式 */
        .nav-user {
            position: relative;
        }

        .nav-user .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-user .user-info:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-user .dropdown-arrow {
            margin-left: 8px;
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .nav-user .user-info.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .nav-user .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            min-width: 280px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            border: 1px solid #e0e0e0;
        }

        .nav-user .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 16px;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px 12px 0 0;
            color: white;
        }

        .dropdown-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .dropdown-user-info {
            flex: 1;
        }

        .dropdown-username {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 2px;
        }

        .dropdown-email {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .dropdown-divider {
            height: 1px;
            background: #e9ecef;
            margin: 8px 0;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #007bff;
            text-decoration: none;
        }

        .dropdown-item.text-danger:hover {
            background-color: #fff5f5;
            color: #dc3545;
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        .config-card {
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
        }

        .config-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .config-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            position: relative;
        }

        .config-header.default {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .config-header.public {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .config-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 0.75rem;
        }

        .config-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .config-body {
            padding: 1.5rem;
        }

        .config-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .summary-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .summary-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: #495057;
        }

        .summary-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .config-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-action {
            flex: 1;
            min-width: 80px;
            font-size: 0.85rem;
            padding: 0.5rem 0.75rem;
        }

        .search-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #007bff;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .floating-actions {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }

        .fab-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .fab-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            display: none;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .filter-tabs {
            margin-bottom: 2rem;
        }

        .filter-tabs .nav-link {
            border-radius: 25px;
            margin-right: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .filter-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }

        .filter-tabs .nav-link:not(.active) {
            color: #6c757d;
            border-color: #e9ecef;
        }

        .filter-tabs .nav-link:not(.active):hover {
            color: #007bff;
            border-color: #007bff;
            background: rgba(0,123,255,0.1);
        }

        /*  页面特定样式 */
        .main-content {
            padding-top: 1rem;
        }

        /*  修复下拉菜单样式 */
        .nav-user .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            padding: 0.5rem 0;
            min-width: 150px;
            display: none;
            z-index: 1000;
        }

        .nav-user .dropdown-menu.show {
            display: block;
        }

        .nav-user .dropdown-menu a {
            display: block;
            padding: 0.5rem 1rem;
            color: #333;
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .nav-user .dropdown-menu a:hover {
            background-color: #f8f9fa;
        }

        /*  浮动按钮和加载遮罩样式 */
        .floating-actions {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            z-index: 1000;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .fab-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .fab-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .fab:hover {
            transform: scale(1.1);
        }

        .fab i {
            font-size: 1.2rem;
        }

        /* 加载遮罩 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-light">
    <!--  通用导航栏 -->
    <div th:replace="fragments/navbar :: navbar('configs')"></div>

    <!-- 主要内容 -->
    <div class="container main-content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="fas fa-cog text-primary me-2"></i>
                    试卷配置管理
                </h1>
                <p class="text-muted mb-0">管理您的试卷生成配置，提高组卷效率</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" id="importConfigBtn">
                    <i class="fas fa-upload me-1"></i>
                    导入配置
                </button>
                <a href="/paper/generate" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    新建配置
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards" id="statsCards">
            <!-- 统计信息将通过JavaScript动态加载 -->
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="search-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput"
                               placeholder="搜索配置名称...">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2 justify-content-end">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includePublicConfigs">
                            <label class="form-check-label" for="includePublicConfigs">
                                包含公共配置
                            </label>
                        </div>
                        <button class="btn btn-outline-secondary" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-sort me-1"></i>
                                排序
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-sort="recent">最近使用</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="usage">使用次数</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="name">配置名称</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="created">创建时间</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选标签 -->
        <ul class="nav nav-pills filter-tabs" id="filterTabs">
            <li class="nav-item">
                <a class="nav-link active" href="#" data-filter="all">
                    <i class="fas fa-list me-1"></i>
                    全部配置
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-filter="my">
                    <i class="fas fa-user me-1"></i>
                    我的配置
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-filter="default">
                    <i class="fas fa-star me-1"></i>
                    默认配置
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-filter="public">
                    <i class="fas fa-globe me-1"></i>
                    公共配置
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-filter="recent">
                    <i class="fas fa-clock me-1"></i>
                    最近使用
                </a>
            </li>
        </ul>

        <!-- 配置列表 -->
        <div class="row" id="configsList">
            <!-- 配置卡片将通过JavaScript动态加载 -->
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-cog"></i>
            <h4>暂无配置</h4>
            <p>您还没有创建任何试卷配置，点击"新建配置"开始创建吧！</p>
            <a href="/paper/generate" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                新建配置
            </a>
        </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
        <button class="fab fab-secondary" id="exportSelectedBtn" title="导出选中配置" style="display: none;">
            <i class="fas fa-download"></i>
        </button>
        <button class="fab fab-primary" id="scrollTopBtn" title="回到顶部">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- 文件上传隐藏表单 -->
    <input type="file" id="fileInput" accept=".json" style="display: none;">

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!--  通用导航栏功能 -->
    <script src="/static/js/navbar.js"></script>
    <script src="/static/js/paper-config-management.js"></script>

    <script>
        //  页面加载完成后初始化
        $(document).ready(function() {
            console.log('🔧 配置管理页面DOM加载完成');

            //  确保加载遮罩在合理时间后隐藏
            setTimeout(function() {
                if ($('#loadingOverlay').is(':visible')) {
                    console.warn('⚠️ 强制隐藏加载遮罩（超时）');
                    $('#loadingOverlay').hide();
                }
            }, 10000); // 10秒后强制隐藏
        });


    </script>
</body>
</html>
