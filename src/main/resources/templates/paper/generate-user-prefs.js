/**
 * 用户偏好持久化功能
 * 保存和加载用户的组卷配置
 */

// 保存用户组卷偏好到localStorage
function saveUserPreferences() {
    try {
        const preferences = {
            // 题型数量
            singleChoiceCount: $('#singleChoiceCount').val(),
            multipleChoiceCount: $('#multipleChoiceCount').val(),
            judgmentCount: $('#judgmentCount').val(),
            fillCount: $('#fillCount').val(),
            shortAnswerCount: $('#shortAnswerCount').val(),
            
            // 题型分值
            singleChoiceScore: $('#singleChoiceScore').val(),
            multipleChoiceScore: $('#multipleChoiceScore').val(),
            judgmentScore: $('#judgmentScore').val(),
            fillScore: $('#fillScore').val(),
            shortAnswerScore: $('#shortAnswerScore').val(),
            
            // 难度分布
            easyPercentage: $('#easyPercentage').val(),
            mediumPercentage: $('#mediumPercentage').val(),
            hardPercentage: $('#hardPercentage').val(),
            
            // 上次保存时间
            lastSaved: new Date().toISOString()
        };
        
        localStorage.setItem('paperGenerationPreferences', JSON.stringify(preferences));
        console.log('组卷配置已保存:', preferences);
    } catch (error) {
        console.error('保存组卷配置失败:', error);
    }
}

// 从localStorage加载用户组卷偏好
function loadUserPreferences() {
    try {
        const savedPrefs = localStorage.getItem('paperGenerationPreferences');
        if (!savedPrefs) {
            console.log('未找到保存的组卷配置');
            return false;
        }
        
        const preferences = JSON.parse(savedPrefs);
        console.log('加载已保存的组卷配置:', preferences);
        
        // 验证保存时间 - 如果超过30天则不加载
        const lastSaved = new Date(preferences.lastSaved);
        const now = new Date();
        const daysDiff = Math.floor((now - lastSaved) / (1000 * 60 * 60 * 24));
        
        if (daysDiff > 30) {
            console.log('保存的组卷配置已过期');
            localStorage.removeItem('paperGenerationPreferences');
            return false;
        }
        
        // 应用题型数量
        if (preferences.singleChoiceCount) $('#singleChoiceCount').val(preferences.singleChoiceCount);
        if (preferences.multipleChoiceCount) $('#multipleChoiceCount').val(preferences.multipleChoiceCount);
        if (preferences.judgmentCount) $('#judgmentCount').val(preferences.judgmentCount);
        if (preferences.fillCount) $('#fillCount').val(preferences.fillCount);
        if (preferences.shortAnswerCount) $('#shortAnswerCount').val(preferences.shortAnswerCount);
        
        // 应用题型分值
        if (preferences.singleChoiceScore) $('#singleChoiceScore').val(preferences.singleChoiceScore);
        if (preferences.multipleChoiceScore) $('#multipleChoiceScore').val(preferences.multipleChoiceScore);
        if (preferences.judgmentScore) $('#judgmentScore').val(preferences.judgmentScore);
        if (preferences.fillScore) $('#fillScore').val(preferences.fillScore);
        if (preferences.shortAnswerScore) $('#shortAnswerScore').val(preferences.shortAnswerScore);
        
        // 应用难度分布
        if (preferences.easyPercentage) $('#easyPercentage').val(preferences.easyPercentage);
        if (preferences.mediumPercentage) $('#mediumPercentage').val(preferences.mediumPercentage);
        if (preferences.hardPercentage) $('#hardPercentage').val(preferences.hardPercentage);
        
        // 更新图表和预览
        updateDifficultyChart();
        updateQuestionDistributionChart();
        calculateTotalScore();
        updatePaperPreview();
        
        return true;
    } catch (error) {
        console.error('加载组卷配置失败:', error);
        return false;
    }
}

// 初始化用户偏好功能
function initUserPreferences() {
    // 监听表单变化，自动保存配置
    const formElements = [
        '#singleChoiceCount', '#singleChoiceScore',
        '#multipleChoiceCount', '#multipleChoiceScore',
        '#judgmentCount', '#judgmentScore',
        '#fillCount', '#fillScore',
        '#shortAnswerCount', '#shortAnswerScore',
        '#easyPercentage', '#mediumPercentage', '#hardPercentage'
    ];
    
    // 使用防抖函数减少保存频率
    let saveTimer;
    $(formElements.join(',')).on('change input', function() {
        clearTimeout(saveTimer);
        saveTimer = setTimeout(function() {
            saveUserPreferences();
        }, 500);
    });
    
    // 添加重置和恢复按钮
    if ($('#resetPreferencesBtn').length === 0) {
        const btnGroup = $('<div class="btn-group btn-group-sm mt-3" role="group"></div>');
        
        const resetBtn = $('<button type="button" class="btn btn-outline-secondary" id="resetPreferencesBtn">' +
                          '<i class="fas fa-undo-alt mr-1"></i>重置默认配置</button>');
        resetBtn.on('click', function() {
            Swal.fire({
                title: '确定要重置配置吗?',
                text: '这将恢复所有题型数量、分值和难度分布到系统默认值',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '确定重置',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    resetPaperGenerationForm();
                    localStorage.removeItem('paperGenerationPreferences');
                    Swal.fire('已重置', '组卷配置已恢复到默认值', 'success');
                }
            });
        });
        
        btnGroup.append(resetBtn);
        
        // 添加到合适的位置
        const targetContainer = $('.modal-footer');
        if (targetContainer.length > 0) {
            targetContainer.prepend(btnGroup);
        }
    }
}
