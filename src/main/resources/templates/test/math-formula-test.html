<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学公式渲染测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.css">
    <style>
        .formula-test {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .formula-source {
            background-color: #f0f0f0;
            padding: 5px;
            font-family: monospace;
            border-left: 3px solid #007cba;
            margin-bottom: 10px;
        }
        .formula-rendered {
            font-size: 18px;
            text-align: center;
            padding: 10px;
            background-color: white;
            border: 1px solid #ccc;
        }
        .log-area {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">数学公式渲染测试</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>数学公式测试样例</h5>
                    </div>
                    <div class="card-body">
                        
                        <!-- 基础代数 -->
                        <div class="formula-test">
                            <h6>基础代数</h6>
                            <div class="formula-source">$x^2 + 2x + 1 = (x+1)^2$</div>
                            <div class="formula-rendered" id="formula1">$x^2 + 2x + 1 = (x+1)^2$</div>
                        </div>

                        <!-- 分数和根式 -->
                        <div class="formula-test">
                            <h6>分数和根式</h6>
                            <div class="formula-source">$\frac{a}{b} + \sqrt{c^2 + d^2} = \frac{a\sqrt{c^2 + d^2} + b}{b\sqrt{c^2 + d^2}}$</div>
                            <div class="formula-rendered" id="formula2">$\frac{a}{b} + \sqrt{c^2 + d^2} = \frac{a\sqrt{c^2 + d^2} + b}{b\sqrt{c^2 + d^2}}$</div>
                        </div>

                        <!-- 积分 -->
                        <div class="formula-test">
                            <h6>积分</h6>
                            <div class="formula-source">$$\int_0^1 x^2 dx = \left[\frac{x^3}{3}\right]_0^1 = \frac{1}{3}$$</div>
                            <div class="formula-rendered" id="formula3">$$\int_0^1 x^2 dx = \left[\frac{x^3}{3}\right]_0^1 = \frac{1}{3}$$</div>
                        </div>

                        <!-- 求和 -->
                        <div class="formula-test">
                            <h6>求和</h6>
                            <div class="formula-source">$$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$$</div>
                            <div class="formula-rendered" id="formula4">$$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$$</div>
                        </div>

                        <!-- 矩阵 -->
                        <div class="formula-test">
                            <h6>矩阵</h6>
                            <div class="formula-source">$$A = \begin{pmatrix} 1 & 2 \\ 3 & 4 \end{pmatrix}, \quad \det(A) = 1 \times 4 - 2 \times 3 = -2$$</div>
                            <div class="formula-rendered" id="formula5">$$A = \begin{pmatrix} 1 & 2 \\ 3 & 4 \end{pmatrix}, \quad \det(A) = 1 \times 4 - 2 \times 3 = -2$$</div>
                        </div>

                        <!-- 三角函数 -->
                        <div class="formula-test">
                            <h6>三角函数</h6>
                            <div class="formula-source">$\sin^2 x + \cos^2 x = 1, \quad e^{i\theta} = \cos\theta + i\sin\theta$</div>
                            <div class="formula-rendered" id="formula6">$\sin^2 x + \cos^2 x = 1, \quad e^{i\theta} = \cos\theta + i\sin\theta$</div>
                        </div>

                        <!-- 极限 -->
                        <div class="formula-test">
                            <h6>极限</h6>
                            <div class="formula-source">$$\lim_{x \to 0} \frac{\sin x}{x} = 1, \quad \lim_{n \to \infty} \left(1 + \frac{1}{n}\right)^n = e$$</div>
                            <div class="formula-rendered" id="formula7">$$\lim_{x \to 0} \frac{\sin x}{x} = 1, \quad \lim_{n \to \infty} \left(1 + \frac{1}{n}\right)^n = e$$</div>
                        </div>

                        <!-- 物理公式 -->
                        <div class="formula-test">
                            <h6>物理公式</h6>
                            <div class="formula-source">$E = mc^2, \quad F = ma, \quad s = v_0 t + \frac{1}{2}at^2$</div>
                            <div class="formula-rendered" id="formula8">$E = mc^2, \quad F = ma, \quad s = v_0 t + \frac{1}{2}at^2$</div>
                        </div>

                        <!-- 化学方程式 -->
                        <div class="formula-test">
                            <h6>化学方程式</h6>
                            <div class="formula-source">$CH_4 + 2O_2 \rightarrow CO_2 + 2H_2O$</div>
                            <div class="formula-rendered" id="formula9">$CH_4 + 2O_2 \rightarrow CO_2 + 2H_2O$</div>
                        </div>

                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5>PDF生成测试</h5>
                    </div>
                    <div class="card-body">
                        <p>测试包含数学公式的试卷PDF生成</p>
                        <div class="btn-group" role="group">
                            <button class="btn btn-primary" onclick="testMathPaperDownload()">下载数学试卷PDF</button>
                            <button class="btn btn-success" onclick="testBasicPaperDownload()">下载基础试卷PDF</button>
                            <button class="btn btn-info" onclick="testDebugPdf()">调试PDF生成</button>
                        </div>
                        <div id="downloadResult" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>测试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="logArea" class="log-area"></div>
                        <button class="btn btn-sm btn-secondary mt-2" onclick="clearLog()">清空日志</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KaTeX JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/contrib/auto-render.min.js"></script>
    
    <script>
        // 渲染数学公式
        document.addEventListener("DOMContentLoaded", function() {
            renderMathInElement(document.body, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ]
            });
            log('页面加载完成，数学公式已渲染');
        });

        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        // 测试数学试卷下载
        async function testMathPaperDownload() {
            log('开始测试数学试卷下载...');
            await testDownloadWithPath('/api/papers/download/100?format=pdf', '数学试卷PDF下载');
        }

        // 测试基础试卷下载
        async function testBasicPaperDownload() {
            log('开始测试基础试卷下载...');
            await testDownloadWithPath('/api/papers/download/1?format=pdf', '基础试卷PDF下载');
        }

        // 测试调试PDF
        async function testDebugPdf() {
            log('开始测试调试PDF生成...');
            await testDownloadWithPath('/debug/pdf', '调试PDF生成');
        }

        // 通用下载测试函数
        async function testDownloadWithPath(url, description) {
            try {
                log(`请求URL: ${url}`);
                const response = await fetch(url);
                
                log(`${description}请求状态: ${response.status}`);
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    log(`响应内容类型: ${contentType}`);
                    
                    document.getElementById('downloadResult').innerHTML = 
                        `<div class="alert alert-success">${description}成功！<br>内容类型: ${contentType}</div>`;
                    log(`${description}成功`);
                } else {
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorText = await response.text();
                        if (errorText) {
                            errorMessage += `: ${errorText}`;
                        }
                    } catch (e) {
                        log(`无法读取错误响应: ${e.message}`);
                    }
                    
                    document.getElementById('downloadResult').innerHTML = 
                        `<div class="alert alert-danger">${description}失败: ${errorMessage}</div>`;
                    log(`${description}失败: ${errorMessage}`);
                }
            } catch (error) {
                document.getElementById('downloadResult').innerHTML = 
                    `<div class="alert alert-danger">${description}异常: ${error.message}</div>`;
                log(`${description}异常: ${error.message}`);
            }
        }
    </script>
</body>
</html>
