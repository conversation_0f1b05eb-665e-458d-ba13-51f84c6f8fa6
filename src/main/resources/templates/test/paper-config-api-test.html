<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷配置API测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>试卷配置API测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试配置ID 32</h5>
                    </div>
                    <div class="card-body">
                        <button id="testBtn" class="btn btn-primary">测试 /api/paper-configs/32</button>
                        <div id="loading" class="mt-3" style="display: none;">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API响应</h5>
                    </div>
                    <div class="card-body">
                        <pre id="response" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">点击测试按钮查看响应</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>知识点配置分析</h5>
                    </div>
                    <div class="card-body">
                        <div id="analysis">点击测试按钮查看分析结果</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('#testBtn').click(function() {
                $('#loading').show();
                $('#response').text('请求中...');
                $('#analysis').text('分析中...');
                
                $.ajax({
                    url: '/api/paper-configs/32',
                    method: 'GET',
                    success: function(response) {
                        $('#loading').hide();
                        
                        // 显示完整响应
                        $('#response').text(JSON.stringify(response, null, 2));
                        
                        // 分析知识点配置
                        analyzeKnowledgePointConfigs(response);
                    },
                    error: function(xhr, status, error) {
                        $('#loading').hide();
                        $('#response').text('请求失败: ' + error + '\n\n' + xhr.responseText);
                        $('#analysis').html('<div class="alert alert-danger">请求失败: ' + error + '</div>');
                    }
                });
            });
        });
        
        function analyzeKnowledgePointConfigs(response) {
            let analysisHtml = '';
            
            if (response.success && response.data && response.data.knowledgePointConfigs) {
                const configs = response.data.knowledgePointConfigs;
                
                analysisHtml += '<div class="alert alert-success">API请求成功！</div>';
                analysisHtml += '<h6>知识点配置分析：</h6>';
                analysisHtml += '<table class="table table-striped">';
                analysisHtml += '<thead><tr><th>知识点ID</th><th>知识点名称</th><th>基础题量</th><th>简答题</th><th>简答题数量</th></tr></thead>';
                analysisHtml += '<tbody>';
                
                configs.forEach((config, index) => {
                    const hasName = config.knowledgeName && config.knowledgeName.trim() !== '';
                    const nameClass = hasName ? 'text-success' : 'text-danger';
                    const nameText = hasName ? config.knowledgeName : '❌ 缺失';
                    
                    analysisHtml += '<tr>';
                    analysisHtml += '<td>' + config.knowledgeId + '</td>';
                    analysisHtml += '<td class="' + nameClass + '">' + nameText + '</td>';
                    analysisHtml += '<td>' + (config.questionCount || config.basicQuestionCount || 0) + '</td>';
                    analysisHtml += '<td>' + (config.includeShortAnswer ? '✅' : '❌') + '</td>';
                    analysisHtml += '<td>' + (config.shortAnswerCount || 0) + '</td>';
                    analysisHtml += '</tr>';
                });
                
                analysisHtml += '</tbody></table>';
                
                // 统计信息
                const totalConfigs = configs.length;
                const configsWithNames = configs.filter(c => c.knowledgeName && c.knowledgeName.trim() !== '').length;
                const configsWithShortAnswer = configs.filter(c => c.includeShortAnswer).length;
                
                analysisHtml += '<div class="mt-3">';
                analysisHtml += '<h6>统计信息：</h6>';
                analysisHtml += '<ul>';
                analysisHtml += '<li>总知识点数量: ' + totalConfigs + '</li>';
                analysisHtml += '<li>有名称的知识点: ' + configsWithNames + ' / ' + totalConfigs + '</li>';
                analysisHtml += '<li>启用简答题的知识点: ' + configsWithShortAnswer + ' / ' + totalConfigs + '</li>';
                analysisHtml += '</ul>';
                analysisHtml += '</div>';
                
                if (configsWithNames === totalConfigs) {
                    analysisHtml += '<div class="alert alert-success mt-3">✅ 所有知识点都有名称，修复成功！</div>';
                } else {
                    analysisHtml += '<div class="alert alert-warning mt-3">⚠️ 还有 ' + (totalConfigs - configsWithNames) + ' 个知识点缺少名称</div>';
                }
                
            } else {
                analysisHtml += '<div class="alert alert-danger">API响应格式异常或没有知识点配置数据</div>';
            }
            
            $('#analysis').html(analysisHtml);
        }
    </script>
</body>
</html>
